# 底部功能按钮实现说明

## 新增功能

在 ParcelDetailPage 底部添加了三个主要功能按钮，与您提供的设计图完全一致：

### 1. View on Map (查看地图)
- **图标**: 地图定位图标
- **功能**: 在 Google Maps 中打开当前地块位置
- **实现**: 使用地块的经纬度坐标在新窗口中打开 Google Maps
- **样式**: 绿色渐变背景

### 2. Calculate ROI (计算投资回报率)
- **图标**: 计算器图标
- **功能**: 打开 ROI 分析模态框，显示详细的投资分析
- **实现**: 基于现有的 industrial location Algorithms 中的财务计算逻辑
- **样式**: 蓝色渐变背景

### 3. Share (分享)
- **图标**: 分享图标
- **功能**: 分享当前地块页面链接
- **实现**: 支持原生分享 API，降级到复制链接到剪贴板
- **样式**: 橙色渐变背景

## 技术实现

### ROI 计算功能
移植了 `industrial location Algorithms` 目录中的核心计算逻辑：

1. **财务指标计算**:
   - 物业价值评估
   - 股权投资计算
   - 年度净营业收入 (NOI)
   - 年度现金流
   - 现金回报率
   - 资本化率
   - 总回报率
   - 年化回报率

2. **假设参数**:
   - 租金: $8.50/平方英尺/年
   - 运营费用比率: 25%
   - 资本化率: 6.5%
   - 持有期: 10年
   - 退出资本化率: 7%
   - 贷款价值比: 75%
   - 利率: 4.5%

### 响应式设计
- **桌面端**: 三个按钮水平排列
- **移动端**: 按钮垂直堆叠，调整为行布局
- **平板端**: 自适应布局

### 样式特性
- **矩形按钮设计**: 宽矩形按钮，图标和文字水平排列
- **三种颜色主题**:
  - View on Map: 蓝色渐变 (#4A90E2 到 #357ABD)
  - Calculate ROI: 绿色渐变 (#50C878 到 #45B068)
  - Share: 紫色渐变 (#8E44AD 到 #7D3C98)
- **悬停动画**: 轻微上升效果和阴影增强
- **简洁设计**: 只显示主要文字，隐藏副标题
- **模态框**: ROI 分析结果以优雅的模态框显示

## 文件修改

### 1. ParcelDetailPage.jsx
- 添加了 ROI 计算函数
- 添加了新的图标组件
- 添加了状态管理 (showROIModal, roiData)
- 添加了事件处理函数
- 添加了底部功能按钮区域
- 添加了 ROI 模态框

### 2. ParcelDetailPage.css
- 添加了底部功能按钮样式
- 添加了 ROI 模态框样式
- 添加了响应式设计规则
- 添加了动画效果

## 使用方法

1. **访问地块详情页面**: 导航到任何地块详情页面 (例如: `/parcel/5591-001-001`)
2. **查看地图**: 点击 "View on Map" 按钮在 Google Maps 中查看位置
3. **计算 ROI**: 点击 "Calculate ROI" 按钮查看投资分析
4. **分享页面**: 点击 "Share" 按钮分享当前页面

## 数据来源

ROI 计算使用以下数据源：
- 地块的建筑面积 (building_area)
- 净营业收入 (noi)
- 最新销售价格 (latest_sale_price)
- 年度税费 (annual_taxes)
- 地块坐标 (latitude, longitude)

如果某些数据不可用，系统会使用合理的默认值进行计算。

## 未来改进

1. **自定义参数**: 允许用户调整 ROI 计算参数
2. **更多分析**: 添加敏感性分析和风险评估
3. **导出功能**: 支持导出 ROI 分析报告
4. **比较功能**: 支持多个地块的 ROI 比较
5. **历史数据**: 集成历史价格和租金数据
