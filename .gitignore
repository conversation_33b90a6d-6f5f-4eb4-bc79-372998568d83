# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
node_modules*/
dist
dist-ssr
*.local

# 依赖相关文件
package-lock.json*
.npm/
.node_repl_history

# Environment variables
.env
.env.local
.env.production
.env.development
backend-server/.env

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Industrial location algorithms folder (contains large files)
industrial location Algorithms/

# Python cache and compiled files
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
