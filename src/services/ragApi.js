// RAG API Service - RAG API服务
// 用于与后端RAG（检索增强生成）系统通信

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

class RagApiService {
  constructor() {
    this.baseUrl = `${API_BASE_URL}/api/rag`;
  }

  /**
   * 流式问答 - 核心功能
   * @param {string|object} questionOrOptions - 用户问题或请求选项对象
   * @param {Function} onMessage - 消息处理回调
   * @param {Function} onError - 错误处理回调
   * @param {Function} onComplete - 完成回调
   * @returns {Function} 取消函数
   */
  askQuestionStream(questionOrOptions, onMessage, onError, onComplete) {
    const abortController = new AbortController();
    
    // 立即启动流式请求
    this._startStreamRequest(questionOrOptions, onMessage, onError, onComplete, abortController);
    
    // 返回取消函数
    return () => {
      abortController.abort();
    };
  }

  /**
   * 启动流式请求的私有方法
   */
  async _startStreamRequest(questionOrOptions, onMessage, onError, onComplete, abortController) {
    try {
      // 支持传入字符串或对象
      const requestData = typeof questionOrOptions === 'string' 
        ? { question: questionOrOptions }
        : questionOrOptions;

      const response = await fetch(`${this.baseUrl}/ask-stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
        signal: abortController.signal
      });

      if (!response.ok) {
        throw new Error(`Stream request failed: ${response.status} ${response.statusText}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ') && line.length > 6) {
            try {
              const data = JSON.parse(line.slice(6));
              onMessage(data);
              
              if (data.type === 'complete') {
                onComplete();
                return;
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', line);
            }
          }
        }
      }
      
      onComplete();
      
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('Stream request was cancelled');
        return;
      }
      
      console.error('Stream request error:', error);
      onError(error);
    }
  }

  /**
   * 获取建议问题
   * @returns {Promise<Array>} 建议问题列表
   */
  async getSuggestions() {
    try {
      const response = await fetch(`${this.baseUrl}/suggestions`);
      
      if (!response.ok) {
        throw new Error(`Suggestions fetch failed: ${response.status}`);
      }

      const data = await response.json();
      return data.success ? data.data : [];
    } catch (error) {
      console.error('Failed to load suggestions from server:', error);
      
      // 返回后备建议问题，确保用户体验
      return [
        {
          category: "快速入门",
          questions: [
            "如何开始使用这个系统？",
            "如何选择和查看工业园区？",
            "地图上的标记点代表什么？"
          ]
        },
        {
          category: "劳动力分析",
          questions: [
            "如何分析劳动力趋势？",
            "在哪里可以查看工资数据？",
            "如何查看就业率和失业率？"
          ]
        },
        {
          category: "地图功能",
          questions: [
            "如何在地图上导航和缩放？",
            "如何切换不同的数据图层？",
            "如何搜索特定的园区？"
          ]
        }
      ];
    }
  }

  /**
   * 发送查询到RAG系统
   * @param {string} query - 用户查询
   * @param {Object} context - 可选的上下文信息
   * @returns {Promise<Object>} RAG响应
   */
  async query(query, context = {}) {
    try {
      const response = await fetch(`${this.baseUrl}/ask`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: query,
          context,
          timestamp: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`RAG API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('RAG query error:', error);
      
      // 返回默认响应，避免应用崩溃
      return {
        success: false,
        error: error.message,
        response: '抱歉，AI助手暂时无法处理您的请求，请稍后重试。',
        sources: []
      };
    }
  }

  /**
   * 获取RAG系统状态
   * @returns {Promise<Object>} 系统状态
   */
  async getStatus() {
    try {
      const response = await fetch(`${this.baseUrl}/status`);
      
      if (!response.ok) {
        throw new Error(`Status check failed: ${response.status}`);
      }

      const data = await response.json();
      return {
        initialized: data.success && data.status?.initialized,
        documentCount: data.status?.documentCount || 0,
        ...data.status
      };
    } catch (error) {
      console.error('RAG status check error:', error);
      return {
        initialized: false,
        error: error.message
      };
    }
  }

  /**
   * 触发知识库构建
   * @returns {Promise<Object>} 构建结果
   */
  async buildKnowledgeBase() {
    try {
      const response = await fetch(`${this.baseUrl}/build-knowledge-base`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Build failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Knowledge base build error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 上传文档到RAG系统
   * @param {File} file - 要上传的文件
   * @param {Object} metadata - 文档元数据
   * @returns {Promise<Object>} 上传结果
   */
  async uploadDocument(file, metadata = {}) {
    try {
      const formData = new FormData();
      formData.append('document', file);
      formData.append('metadata', JSON.stringify(metadata));

      const response = await fetch(`${this.baseUrl}/upload`, {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Document upload error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取会话历史
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Array>} 会话历史
   */
  async getHistory(sessionId) {
    try {
      const response = await fetch(`${this.baseUrl}/history/${sessionId}`);
      
      if (!response.ok) {
        throw new Error(`History fetch failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('History fetch error:', error);
      return [];
    }
  }

  /**
   * 清除会话历史
   * @param {string} sessionId - 会话ID
   * @returns {Promise<boolean>} 是否成功
   */
  async clearHistory(sessionId) {
    try {
      const response = await fetch(`${this.baseUrl}/history/${sessionId}`, {
        method: 'DELETE'
      });

      return response.ok;
    } catch (error) {
      console.error('History clear error:', error);
      return false;
    }
  }

  /**
   * 获取可用的文档列表
   * @returns {Promise<Array>} 文档列表
   */
  async getDocuments() {
    try {
      const response = await fetch(`${this.baseUrl}/documents`);
      
      if (!response.ok) {
        throw new Error(`Documents fetch failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Documents fetch error:', error);
      return [];
    }
  }
}

// 创建单例实例
const ragApiService = new RagApiService();

export default ragApiService; 