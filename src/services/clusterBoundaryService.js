/**
 * Cluster Boundary Service
 * 处理cluster边界计算和分层数据加载
 */

class ClusterBoundaryService {
  static clusterBoundaries = null;
  static clusterSampleData = {};
  static clusterFullData = {};

  /**
   * 计算真正的外围边界 - 改进的凸包算法
   */
  static convexHull(points) {
    if (points.length < 3) return points;

    // 创建点的副本，避免修改原数组
    const pts = [...points];

    // 找到最左下角的点作为起始点
    let start = 0;
    for (let i = 1; i < pts.length; i++) {
      if (pts[i].latitude < pts[start].latitude ||
          (pts[i].latitude === pts[start].latitude && pts[i].longitude < pts[start].longitude)) {
        start = i;
      }
    }

    // 将起始点移到第一位
    [pts[0], pts[start]] = [pts[start], pts[0]];
    const pivot = pts[0];

    // 按极角排序其余点
    const remaining = pts.slice(1);
    remaining.sort((a, b) => {
      const angleA = Math.atan2(a.latitude - pivot.latitude, a.longitude - pivot.longitude);
      const angleB = Math.atan2(b.latitude - pivot.latitude, b.longitude - pivot.longitude);

      if (Math.abs(angleA - angleB) < 1e-9) {
        // 如果角度相同，选择距离更远的点
        const distA = this.distance(pivot, a);
        const distB = this.distance(pivot, b);
        return distA - distB;
      }
      return angleA - angleB;
    });

    // Graham扫描算法构建凸包
    const hull = [pivot];

    for (let i = 0; i < remaining.length; i++) {
      // 移除不在凸包上的点
      while (hull.length > 1 &&
             this.crossProduct(hull[hull.length-2], hull[hull.length-1], remaining[i]) < 0) {
        hull.pop();
      }
      hull.push(remaining[i]);
    }

    return hull;
  }

  /**
   * 计算两点间距离
   */
  static distance(p1, p2) {
    const dx = p1.longitude - p2.longitude;
    const dy = p1.latitude - p2.latitude;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 计算叉积判断转向
   */
  static crossProduct(o, a, b) {
    return (a.longitude - o.longitude) * (b.latitude - o.latitude) -
           (a.latitude - o.latitude) * (b.longitude - o.longitude);
  }

  /**
   * 高级地理边界检测算法 - 多层次优化
   *
   * 算法实现逻辑：
   * 1. 预处理：去除重复点和异常点
   * 2. 极值检测：确保四个方向的极值点被包含
   * 3. 径向扫描：按角度分区，每区间选择最远点
   * 4. 密度过滤：移除过于密集区域的冗余点
   * 5. 平滑处理：确保边界的连续性和平滑度
   * 6. 凸包优化：最终的几何优化
   */
  static findOuterBoundary(points) {
    if (points.length < 4) return points;

    console.log(`🔍 开始边界检测: ${points.length} 个输入点`);

    // === 第1步：预处理 - 去重和异常点检测 ===
    const cleanedPoints = this.preprocessPoints(points);
    console.log(`📋 预处理完成: ${cleanedPoints.length} 个有效点`);

    // === 第2步：极值点检测 ===
    const extremePoints = this.findExtremePoints(cleanedPoints);
    console.log(`🎯 极值点检测: 找到 ${extremePoints.length} 个关键点`);

    // === 第3步：计算几何中心（质心） ===
    const center = this.calculateCentroid(cleanedPoints);
    console.log(`📍 几何中心: (${center.latitude.toFixed(6)}, ${center.longitude.toFixed(6)})`);

    // === 第4步：径向扫描 - 角度分区选择 ===
    const radialPoints = this.radialScan(cleanedPoints, center, extremePoints);
    console.log(`🌟 径向扫描: 选择了 ${radialPoints.length} 个径向点`);

    // === 第5步：密度过滤 - 移除冗余点 ===
    const filteredPoints = this.densityFilter(radialPoints, center);
    console.log(`🔧 密度过滤: 保留 ${filteredPoints.length} 个关键点`);

    // === 第6步：边界平滑处理 ===
    const smoothedPoints = this.smoothBoundary(filteredPoints, center);
    console.log(`✨ 边界平滑: 优化为 ${smoothedPoints.length} 个边界点`);

    // === 第7步：最终凸包优化 ===
    const finalBoundary = this.convexHull(smoothedPoints);
    console.log(`🎯 最终边界: ${finalBoundary.length} 个边界点`);

    console.log(`✅ 边界检测完成: ${points.length} → ${finalBoundary.length} (减少 ${((1 - finalBoundary.length / points.length) * 100).toFixed(1)}%)`);

    return finalBoundary;
  }

  /**
   * 第1步：预处理 - 去重和异常点检测
   */
  static preprocessPoints(points) {
    // 去除重复点（容差：0.0001度，约11米）
    const tolerance = 0.0001;
    const uniquePoints = [];
    const seen = new Set();

    points.forEach(point => {
      const key = `${Math.round(point.latitude / tolerance)}_${Math.round(point.longitude / tolerance)}`;
      if (!seen.has(key)) {
        seen.add(key);
        uniquePoints.push(point);
      }
    });

    // 移除明显的异常点（距离中心超过3个标准差的点）
    if (uniquePoints.length > 10) {
      const tempCenter = this.calculateCentroid(uniquePoints);
      const distances = uniquePoints.map(p => this.distance(tempCenter, p));
      const mean = distances.reduce((sum, d) => sum + d, 0) / distances.length;
      const variance = distances.reduce((sum, d) => sum + Math.pow(d - mean, 2), 0) / distances.length;
      const stdDev = Math.sqrt(variance);
      const threshold = mean + 3 * stdDev;

      return uniquePoints.filter(p => this.distance(tempCenter, p) <= threshold);
    }

    return uniquePoints;
  }

  /**
   * 第2步：极值点检测
   */
  static findExtremePoints(points) {
    let northmost = points[0];
    let southmost = points[0];
    let eastmost = points[0];
    let westmost = points[0];

    points.forEach(point => {
      if (point.latitude > northmost.latitude) northmost = point;
      if (point.latitude < southmost.latitude) southmost = point;
      if (point.longitude > eastmost.longitude) eastmost = point;
      if (point.longitude < westmost.longitude) westmost = point;
    });

    // 确保极值点是唯一的
    const extremes = [northmost, southmost, eastmost, westmost];
    const uniqueExtremes = [];
    extremes.forEach(point => {
      if (!uniqueExtremes.some(p =>
        Math.abs(p.latitude - point.latitude) < 0.00001 &&
        Math.abs(p.longitude - point.longitude) < 0.00001)) {
        uniqueExtremes.push(point);
      }
    });

    return uniqueExtremes;
  }

  /**
   * 第3步：计算几何中心（质心）
   */
  static calculateCentroid(points) {
    const centerLat = points.reduce((sum, p) => sum + p.latitude, 0) / points.length;
    const centerLng = points.reduce((sum, p) => sum + p.longitude, 0) / points.length;
    return { latitude: centerLat, longitude: centerLng };
  }

  /**
   * 第4步：径向扫描 - 角度分区选择
   */
  static radialScan(points, center, extremePoints) {
    // 为每个点计算角度和距离
    const pointsWithPolar = points.map(point => ({
      ...point,
      angle: Math.atan2(point.latitude - center.latitude, point.longitude - center.longitude),
      distance: this.distance(center, point)
    }));

    // 动态确定角度分区数量（基于点的数量）
    const baseSegments = 36; // 基础36个分区（每10度）
    const segments = Math.min(baseSegments, Math.max(12, Math.floor(points.length / 10)));
    const angleStep = (2 * Math.PI) / segments;

    console.log(`🔄 径向扫描: 使用 ${segments} 个角度分区`);

    // 按角度分组
    const angleGroups = {};
    pointsWithPolar.forEach(point => {
      // 将角度标准化到 [0, 2π]
      let normalizedAngle = point.angle;
      if (normalizedAngle < 0) normalizedAngle += 2 * Math.PI;

      const groupIndex = Math.floor(normalizedAngle / angleStep);

      if (!angleGroups[groupIndex]) {
        angleGroups[groupIndex] = [];
      }
      angleGroups[groupIndex].push(point);
    });

    // 每个分区选择最远的点
    const selectedPoints = [];
    Object.values(angleGroups).forEach(group => {
      if (group.length > 0) {
        // 选择距离中心最远的点
        const farthest = group.reduce((max, point) =>
          point.distance > max.distance ? point : max
        );
        selectedPoints.push(farthest);
      }
    });

    // 确保极值点被包含
    extremePoints.forEach(extreme => {
      const isIncluded = selectedPoints.some(p =>
        Math.abs(p.latitude - extreme.latitude) < 0.00001 &&
        Math.abs(p.longitude - extreme.longitude) < 0.00001
      );
      if (!isIncluded) {
        selectedPoints.push(extreme);
      }
    });

    return selectedPoints;
  }

  /**
   * 第5步：密度过滤 - 移除冗余点
   */
  static densityFilter(points, center) {
    if (points.length <= 8) return points; // 保持最少8个点

    // 按角度排序
    const sortedPoints = points.map(point => ({
      ...point,
      angle: Math.atan2(point.latitude - center.latitude, point.longitude - center.longitude)
    })).sort((a, b) => a.angle - b.angle);

    const filtered = [];
    const minAngleDiff = Math.PI / 18; // 最小角度差：10度
    const minDistanceRatio = 0.1; // 最小距离比例

    for (let i = 0; i < sortedPoints.length; i++) {
      const current = sortedPoints[i];
      let shouldInclude = true;

      // 检查与已选择点的角度和距离
      for (let j = 0; j < filtered.length; j++) {
        const existing = filtered[j];
        const angleDiff = Math.abs(current.angle - existing.angle);
        const distanceRatio = Math.abs(current.distance - existing.distance) / Math.max(current.distance, existing.distance);

        // 如果角度太近且距离相似，则跳过
        if (angleDiff < minAngleDiff && distanceRatio < minDistanceRatio) {
          shouldInclude = false;
          break;
        }
      }

      if (shouldInclude) {
        filtered.push(current);
      }
    }

    return filtered;
  }

  /**
   * 第6步：边界平滑处理
   */
  static smoothBoundary(points, center) {
    if (points.length <= 6) return points;

    // 按角度排序确保连续性
    const sortedPoints = points.map(point => ({
      ...point,
      angle: Math.atan2(point.latitude - center.latitude, point.longitude - center.longitude)
    })).sort((a, b) => a.angle - b.angle);

    // 移除尖锐的突出点（角度变化过大的点）
    const smoothed = [];
    const maxAngleChange = Math.PI / 3; // 最大角度变化：60度

    for (let i = 0; i < sortedPoints.length; i++) {
      const prev = sortedPoints[(i - 1 + sortedPoints.length) % sortedPoints.length];
      const current = sortedPoints[i];
      const next = sortedPoints[(i + 1) % sortedPoints.length];

      // 计算角度变化
      const angle1 = Math.atan2(current.latitude - prev.latitude, current.longitude - prev.longitude);
      const angle2 = Math.atan2(next.latitude - current.latitude, next.longitude - current.longitude);
      let angleChange = Math.abs(angle2 - angle1);
      if (angleChange > Math.PI) angleChange = 2 * Math.PI - angleChange;

      // 保留角度变化不太剧烈的点
      if (angleChange <= maxAngleChange || smoothed.length < 6) {
        smoothed.push(current);
      }
    }

    return smoothed;
  }

  /**
   * 生成cluster边界数据
   */
  static async generateClusterBoundaries() {
    if (this.clusterBoundaries) {
      return this.clusterBoundaries;
    }

    try {
      console.log('🔄 Generating cluster boundaries...');
      
      // 加载cluster summary获取基本信息
      const summaryResponse = await fetch('/data/cluster_summary.csv');
      const summaryText = await summaryResponse.text();
      const summaryData = this.parseCSV(summaryText);

      const boundaries = {};
      
      // 为每个cluster生成边界
      for (let clusterId = 0; clusterId <= 10; clusterId++) {
        try {
          // 加载cluster数据
          const response = await fetch(`/data/cluster_${clusterId}_parcels.csv`);
          const text = await response.text();
          const parcels = this.parseCSV(text);
          
          // 采样数据以提高性能（取每50个点中的1个，确保有足够的边界点）
          const sampledParcels = parcels.filter((_, index) => index % 50 === 0);

          // 使用改进的外围边界检测
          const boundaryPoints = this.findOuterBoundary(sampledParcels);
          
          // 计算中心点
          const centerLat = sampledParcels.reduce((sum, p) => sum + p.latitude, 0) / sampledParcels.length;
          const centerLng = sampledParcels.reduce((sum, p) => sum + p.longitude, 0) / sampledParcels.length;
          
          // 获取cluster统计信息
          const clusterStats = summaryData.find(s => s.cluster_id === clusterId) || {};
          
          boundaries[clusterId] = {
            id: clusterId,
            boundary: boundaryPoints,
            center: { latitude: centerLat, longitude: centerLng },
            totalParcels: parcels.length,
            sampleSize: sampledParcels.length,
            stats: clusterStats
          };
          
          // 存储采样数据用于中等缩放级别
          this.clusterSampleData[clusterId] = sampledParcels.slice(0, 50); // 最多50个点
          
          console.log(`✅ Generated boundary for cluster ${clusterId}: ${boundaryPoints.length} boundary points, ${sampledParcels.length} sample points`);
          
        } catch (error) {
          console.warn(`⚠️ Failed to generate boundary for cluster ${clusterId}:`, error);
        }
      }
      
      this.clusterBoundaries = boundaries;
      console.log(`✅ Generated boundaries for ${Object.keys(boundaries).length} clusters`);
      
      return boundaries;
      
    } catch (error) {
      console.error('❌ Failed to generate cluster boundaries:', error);
      return {};
    }
  }

  /**
   * 根据缩放级别获取适当的数据
   */
  static async getDataForZoomLevel(zoomLevel) {
    const boundaries = await this.generateClusterBoundaries();
    
    if (zoomLevel <= 8) {
      // 远距离：只显示边界
      return {
        type: 'boundaries',
        data: boundaries,
        description: 'Cluster boundaries only'
      };
    } else if (zoomLevel <= 12) {
      // 中距离：边界 + 采样点
      return {
        type: 'boundaries_and_samples',
        data: {
          boundaries: boundaries,
          samples: this.clusterSampleData
        },
        description: 'Cluster boundaries with sample points'
      };
    } else {
      // 近距离：加载完整数据（但仍然采样以避免性能问题）
      return await this.getDetailedData();
    }
  }

  /**
   * 获取详细数据（高缩放级别）
   */
  static async getDetailedData() {
    try {
      console.log('🔄 Loading detailed cluster data...');
      
      const allParcels = [];
      
      for (let clusterId = 0; clusterId <= 10; clusterId++) {
        try {
          const response = await fetch(`/data/cluster_${clusterId}_parcels.csv`);
          const text = await response.text();
          const parcels = this.parseCSV(text);
          
          // 采样：每10个点取1个，确保性能
          const sampledParcels = parcels.filter((_, index) => index % 10 === 0);
          
          // 转换为地图格式
          const formattedParcels = sampledParcels.map((parcel, idx) => ({
            id: `${clusterId}_${idx}`,
            name: `Parcel ${parcel.apn}`,
            latitude: parseFloat(parcel.latitude),
            longitude: parseFloat(parcel.longitude),
            type: 'industrial',
            cluster_id: clusterId,
            ...parcel
          }));
          
          allParcels.push(...formattedParcels);
          
        } catch (error) {
          console.warn(`⚠️ Failed to load detailed data for cluster ${clusterId}:`, error);
        }
      }
      
      console.log(`✅ Loaded ${allParcels.length} detailed parcels`);
      
      return {
        type: 'detailed',
        data: allParcels,
        description: `${allParcels.length} sampled parcels`
      };
      
    } catch (error) {
      console.error('❌ Failed to load detailed data:', error);
      return {
        type: 'detailed',
        data: [],
        description: 'Failed to load data'
      };
    }
  }

  /**
   * 获取cluster颜色
   */
  static getClusterColor(clusterId) {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#F8C471'
    ];
    return colors[clusterId % colors.length] || '#FF6B6B';
  }

  /**
   * 解析CSV数据
   */
  static parseCSV(csvText) {
    const lines = csvText.trim().split('\n');
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map(h => h.trim());
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i]);
      if (values.length === headers.length) {
        const row = {};
        headers.forEach((header, index) => {
          const value = values[index];
          if (value && !isNaN(value) && value !== '') {
            row[header] = parseFloat(value);
          } else {
            row[header] = value || '';
          }
        });
        data.push(row);
      }
    }

    return data;
  }

  /**
   * 解析CSV行
   */
  static parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current.trim());
    return result;
  }

  /**
   * 清除缓存
   */
  static clearCache() {
    this.clusterBoundaries = null;
    this.clusterSampleData = {};
    this.clusterFullData = {};
  }
}

export default ClusterBoundaryService;
