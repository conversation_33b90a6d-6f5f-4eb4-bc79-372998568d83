/**
 * Cluster Data Service
 * 处理工业地块cluster数据的加载和转换
 */

class ClusterDataService {
  static clusterData = null;
  static clusterSummary = null;
  static maxParcelsPerCluster = 500; // 限制每个cluster最多显示500个地块
  static sampleRate = 0.005; // 采样率：0.5%的数据

  /**
   * 数据采样方法 - 减少数据量以提高性能
   */
  static sampleData(data, maxItems = null) {
    if (!data || data.length === 0) return data;

    const targetSize = maxItems || Math.max(
      Math.floor(data.length * this.sampleRate),
      Math.min(this.maxParcelsPerCluster, data.length)
    );

    if (data.length <= targetSize) {
      return data;
    }

    // 使用系统采样确保地理分布均匀
    const step = Math.floor(data.length / targetSize);
    const sampledData = [];

    for (let i = 0; i < data.length; i += step) {
      if (sampledData.length >= targetSize) break;
      sampledData.push(data[i]);
    }

    console.log(`Sampled ${sampledData.length} parcels from ${data.length} total`);
    return sampledData;
  }

  /**
   * 加载所有cluster数据 - 优化版本，支持进度回调
   */
  static async loadClusterData(progressCallback = null) {
    if (this.clusterData) {
      return this.clusterData;
    }

    try {
      console.log('🔄 Loading cluster data with optimized parallel loading...');

      // 报告进度：开始加载
      if (progressCallback) {
        progressCallback({ stage: 'starting', progress: 0, message: '开始加载数据...' });
      }

      // 并行加载cluster summary和所有cluster数据
      const summaryPromise = fetch('/data/cluster_summary.csv')
        .then(response => response.text())
        .then(text => this.parseCSV(text));

      // 创建所有cluster数据的加载promises
      const clusterPromises = [];
      for (let i = 0; i <= 10; i++) {
        clusterPromises.push(
          fetch(`/data/cluster_${i}_parcels.csv`)
            .then(response => {
              if (progressCallback) {
                const progress = Math.round(((i + 1) / 11) * 50); // 前50%进度用于网络请求
                progressCallback({
                  stage: 'loading',
                  progress,
                  message: `加载集群 ${i + 1}/11...`
                });
              }
              return response.text();
            })
            .then(text => {
              const parsedData = this.parseCSV(text);
              // 使用更激进的采样率以提高初始加载速度
              return this.sampleData(parsedData, Math.min(50, parsedData.length));
            })
        );
      }

      // 等待所有数据加载完成
      const [summaryData, ...clusterResults] = await Promise.all([summaryPromise, ...clusterPromises]);

      this.clusterSummary = summaryData;

      if (progressCallback) {
        progressCallback({ stage: 'processing', progress: 75, message: '处理数据中...' });
      }

      // 合并所有cluster数据
      let allParcels = [];
      clusterResults.forEach((clusterParcels, index) => {
        const parcelsWithCluster = clusterParcels.map(parcel => ({
          ...parcel,
          cluster_id: index
        }));
        allParcels = allParcels.concat(parcelsWithCluster);
      });

      // 转换为地图可用的格式
      this.clusterData = this.transformToMapFormat(allParcels);

      if (progressCallback) {
        progressCallback({ stage: 'complete', progress: 100, message: '加载完成！' });
      }

      console.log(`✅ Loaded ${this.clusterData.length} parcels from ${clusterResults.length} clusters`);
      return this.clusterData;

    } catch (error) {
      console.error('❌ Error loading cluster data:', error);
      if (progressCallback) {
        progressCallback({ stage: 'error', progress: 0, message: '加载失败，请重试' });
      }
      // 返回空数组而不是抛出错误，保证应用稳定性
      return [];
    }
  }

  /**
   * 解析CSV文本为对象数组
   * 支持包含复杂数据结构的CSV
   */
  static parseCSV(csvText) {
    const lines = csvText.trim().split('\n');
    if (lines.length < 2) return [];

    // 使用更智能的CSV解析，处理包含逗号的字段
    const parseCSVLine = (line) => {
      const result = [];
      let current = '';
      let inQuotes = false;
      let inBraces = 0;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"' && (i === 0 || line[i-1] !== '\\')) {
          inQuotes = !inQuotes;
        } else if (char === '{' && !inQuotes) {
          inBraces++;
          current += char;
        } else if (char === '}' && !inQuotes) {
          inBraces--;
          current += char;
        } else if (char === ',' && !inQuotes && inBraces === 0) {
          result.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }

      if (current) {
        result.push(current.trim());
      }

      return result;
    };

    const headers = parseCSVLine(lines[0]);
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = parseCSVLine(lines[i]);
      const row = {};

      headers.forEach((header, index) => {
        const value = values[index] || '';

        // 尝试转换数字
        if (value && !isNaN(value) && value !== '' && !value.startsWith('{')) {
          row[header] = parseFloat(value);
        } else {
          row[header] = value;
        }
      });

      data.push(row);
    }

    return data;
  }

  /**
   * 将cluster数据转换为地图组件可用的格式
   */
  static transformToMapFormat(parcels) {
    return parcels.map((parcel, index) => {
      // 从FIPS代码推断州和城市信息
      const locationInfo = this.getLocationFromFips(parcel.fips);
      
      return {
        id: parcel.apn || `parcel_${index}`,
        park: `Industrial Parcel ${parcel.apn || index}`,
        apn: parcel.apn, // 添加APN字段用于详情页面导航
        latitude: parcel.latitude,
        longitude: parcel.longitude,
        city: locationInfo.city,
        state: locationInfo.state,

        // 财务数据转换
        monthly_leasing_cost: Math.round((parcel.noi || 0) / 12), // NOI转月租金
        square_footage: parcel.building_area || 0,

        // Cluster特有数据
        cluster_id: parcel.cluster_id,
        noi: parcel.noi || 0,
        annual_taxes: parcel.annual_taxes || 0,
        building_class: parcel.building_class || 'Unknown',
        asset_category: parcel.asset_category || 'Industrial',
        lot_area_ft: parcel.lot_area_ft || 0,
        latest_sale_price: parcel.latest_sale_price || 0,
        loan_balance: parcel.loan_balance || 0,
        owner_name: parcel.owner_name || 'Unknown',

        // 状态信息
        status: this.determineStatus(parcel),

        // 用于兼容现有组件的字段
        'Nearest Port': 'N/A',
        'Port_Min_Cost': 0,
        'Nearest City': locationInfo.city,
        'City_Min_Cost': 0
      };
    }).filter(parcel => 
      // 过滤掉无效坐标的数据
      parcel.latitude && parcel.longitude && 
      !isNaN(parcel.latitude) && !isNaN(parcel.longitude)
    );
  }

  /**
   * 从FIPS代码获取位置信息
   */
  static getLocationFromFips(fips) {
    // 简化的FIPS到位置映射，实际应用中可以使用更完整的映射表
    const fipsMap = {
      '6071': { state: 'CA', city: 'San Bernardino' },
      '6037': { state: 'CA', city: 'Los Angeles' },
      '6059': { state: 'CA', city: 'Orange' },
      '6065': { state: 'CA', city: 'Riverside' }
    };

    return fipsMap[fips] || { state: 'CA', city: 'California' };
  }

  /**
   * 根据parcel数据确定状态
   */
  static determineStatus(parcel) {
    if (parcel.loan_balance > 0) {
      return 'Financed';
    } else if (parcel.latest_sale_price > 0) {
      return 'Recently Sold';
    } else {
      return 'Available';
    }
  }

  /**
   * 获取cluster摘要信息
   */
  static getClusterSummary() {
    return this.clusterSummary;
  }

  /**
   * 根据cluster ID获取cluster颜色
   */
  static getClusterColor(clusterId) {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#F8C471'
    ];
    return colors[clusterId % colors.length];
  }

  /**
   * 快速加载少量数据用于初始显示
   */
  static async loadQuickPreview(progressCallback = null) {
    try {
      if (progressCallback) {
        progressCallback({ stage: 'starting', progress: 0, message: '快速加载预览数据...' });
      }

      // 只加载前3个cluster的少量数据
      const quickPromises = [];
      for (let i = 0; i <= 2; i++) {
        quickPromises.push(
          fetch(`/data/cluster_${i}_parcels.csv`)
            .then(response => response.text())
            .then(text => {
              const parsedData = this.parseCSV(text);
              // 只取前20个数据点
              return parsedData.slice(0, 20).map(parcel => ({
                ...parcel,
                cluster_id: i
              }));
            })
        );
      }

      const quickResults = await Promise.all(quickPromises);
      let quickParcels = [];
      quickResults.forEach(clusterParcels => {
        quickParcels = quickParcels.concat(clusterParcels);
      });

      const quickData = this.transformToMapFormat(quickParcels);

      if (progressCallback) {
        progressCallback({ stage: 'complete', progress: 100, message: '预览数据加载完成！' });
      }

      console.log(`🚀 Quick preview loaded: ${quickData.length} parcels`);
      return quickData;

    } catch (error) {
      console.error('❌ Error loading quick preview:', error);
      if (progressCallback) {
        progressCallback({ stage: 'error', progress: 0, message: '预览加载失败' });
      }
      return [];
    }
  }

  /**
   * 获取cluster统计信息
   */
  static getClusterStats(clusterId) {
    if (!this.clusterSummary) return null;

    return this.clusterSummary.find(cluster =>
      cluster.cluster_id === clusterId
    );
  }
}

export default ClusterDataService;
