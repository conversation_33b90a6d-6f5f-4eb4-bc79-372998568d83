import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import ParcelDetailPage from '../../pages/ParcelDetailPage';

// Mock data for testing
const mockParcelData = {
  apn: "5421-003-015",
  latitude: 34.0522,
  longitude: -118.2437,
  building_class: "A",
  asset_category: "Industrial Warehouse",
  cluster_id: 7,
  noi: 2450000,
  building_area: 125000,
  lot_area_ft: 180000,
  annual_taxes: 89500,
  latest_sale_price: 18500000,
  latest_sale_date: "2023-08-15",
  loan_balance: 12000000,
  loan_rate: 4.25,
  loan_due_date: "2028-08-15",
  owner_name: "Pacific Industrial Holdings LLC",
  owner_phone: "(*************",
  owner_email: "<EMAIL>",
  fips: "06037",
};

// Test component to verify the ParcelDetailPage works
const ParcelDetailTest = () => {
  return (
    <BrowserRouter>
      <div style={{ 
        width: '100vw', 
        height: '100vh', 
        background: '#0a0a0f',
        overflow: 'hidden'
      }}>
        <ParcelDetailPage />
      </div>
    </BrowserRouter>
  );
};

export default ParcelDetailTest;
