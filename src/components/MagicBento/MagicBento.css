:root {
  --hue: 27;
  --sat: 69%;
  --white: hsl(0, 0%, 100%);
  --purple-primary: rgba(132, 0, 255, 1);
  --purple-glow: rgba(132, 0, 255, 0.2);
  --purple-border: rgba(132, 0, 255, 0.8);
  --border-color: #392e4e;
  --background-dark: #060010;
  color-scheme: light dark;
}

.card-grid {
  display: grid;
  gap: 0.5em;
  padding: 0.75em;
  max-width: 54em;
  font-size: clamp(1rem, 0.9rem + 0.5vw, 1.5rem);
}

.card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  aspect-ratio: 4/3;
  min-height: 200px;
  width: 100%;
  max-width: 100%;
  padding: 1.25em;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: var(--background-dark);
  font-weight: 300;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;

  --glow-x: 50%;
  --glow-y: 50%;
  --glow-intensity: 0;
  --glow-radius: 200px;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card__header,
.card__content {
  display: flex;
  position: relative;
  color: var(--white);
}

.card__header {
  gap: 0.75em;
  justify-content: space-between;
}

.card__content {
  flex-direction: column;
}

.card__label {
  font-size: 16px;
  opacity: 0.8;
  font-weight: 500;
}

.card__title,
.card__description {
  --clamp-title: 1;
  --clamp-desc: 2;
}

.card__title {
  font-weight: 400;
  font-size: 16px;
  margin: 0 0 0.25em;
}

.card__description {
  font-size: 12px;
  line-height: 1.2;
  opacity: 0.9;
}

.card__icon {
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

.card__icon svg {
  width: 24px;
  height: 24px;
}

.card--text-autohide .card__title,
.card--text-autohide .card__description {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card--text-autohide .card__title {
  -webkit-line-clamp: var(--clamp-title);
  line-clamp: var(--clamp-title);
}

.card--text-autohide .card__description {
  -webkit-line-clamp: var(--clamp-desc);
  line-clamp: var(--clamp-desc);
}

@media (max-width: 599px) {
  .card-grid {
    grid-template-columns: 1fr;
    width: 90%;
    margin: 0 auto;
    padding: 0.5em;
  }

  .card {
    width: 100%;
    min-height: 180px;
  }
}

@media (min-width: 600px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .card:nth-child(3) {
    grid-column: span 2;
    grid-row: span 2;
  }

  .card:nth-child(4) {
    grid-column: 1/span 2;
    grid-row: 2/span 2;
  }

  .card:nth-child(6) {
    grid-column: 4;
    grid-row: 3;
  }
}

/* Border glow effect */
.card--border-glow::after {
  content: '';
  position: absolute;
  inset: 0;
  padding: 6px;
  background: radial-gradient(var(--glow-radius) circle at var(--glow-x) var(--glow-y),
      rgba(132, 0, 255, calc(var(--glow-intensity) * 0.8)) 0%,
      rgba(132, 0, 255, calc(var(--glow-intensity) * 0.4)) 30%,
      transparent 60%);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: subtract;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.card--border-glow:hover::after {
  opacity: 1;
}

.card--border-glow:hover {
  box-shadow: 0 4px 20px rgba(46, 24, 78, 0.4), 0 0 30px var(--purple-glow);
}

.particle-container {
  position: relative;
  overflow: hidden;
}

.particle::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: rgba(132, 0, 255, 0.2);
  border-radius: 50%;
  z-index: -1;
}

.particle-container:hover {
  box-shadow: 0 4px 20px rgba(46, 24, 78, 0.2), 0 0 30px var(--purple-glow);
}

/* Global spotlight styles */
.global-spotlight {
  mix-blend-mode: screen;
  will-change: transform, opacity;
  z-index: 200 !important;
  pointer-events: none;
}

.bento-section {
  position: relative;
  user-select: none;
}

/* Contact specific styles */
.contact-bento-grid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.contact-bento-grid .card-grid {
  max-width: none;
  gap: 1rem;
}

.contact-bento-grid .card {
  min-height: 250px;
  background: rgba(6, 0, 16, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-bento-grid .card:hover {
  border-color: rgba(132, 0, 255, 0.5);
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(132, 0, 255, 0.2);
}

/* Special layout for contact cards */
@media (min-width: 1024px) {
  .contact-bento-grid .card-grid {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, minmax(120px, 1fr));
    gap: 1rem;
    align-items: stretch;
  }

  /* Analytics */
  .contact-bento-grid .card:nth-child(1) {
    grid-column: 1;
    grid-row: 1;
  }

  /* Dashboard */
  .contact-bento-grid .card:nth-child(2) {
    grid-column: 2;
    grid-row: 1;
  }

  /* AI Assistant (large) */
  .contact-bento-grid .card:nth-child(3) {
    grid-column: 3 / 5;
    grid-row: 1 / 3;
  }

  /* Automation (efficiency - large) */
  .contact-bento-grid .card:nth-child(4) {
    grid-column: 1 / 3;
    grid-row: 2 / 4;
  }

  /* Collaboration (small) - 与效率模块底部对齐 */
  .contact-bento-grid .card:nth-child(5) {
    grid-column: 3;
    grid-row: 3;
    align-self: end;
  }

  /* Security - 与效率模块底部对齐 */
  .contact-bento-grid .card:nth-child(6) {
    grid-column: 4;
    grid-row: 3;
    align-self: end;
  }
}

/* 联系信息部分样式 */
.contact-info-section {
  margin-top: 3rem;
  padding: 0 1rem;
}

.contact-info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

.contact-info-card {
  background: rgba(6, 0, 16, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.contact-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(132, 0, 255, 0.1), rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.contact-info-card:hover {
  transform: translateY(-4px);
  border-color: rgba(132, 0, 255, 0.4);
  box-shadow:
    0 12px 40px rgba(132, 0, 255, 0.15),
    0 0 0 1px rgba(132, 0, 255, 0.1);
}

.contact-info-card:hover::before {
  opacity: 1;
}

.contact-info-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(132, 0, 255, 0.2), rgba(168, 85, 247, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(132, 0, 255, 0.9);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.contact-info-card:hover .contact-info-icon {
  background: linear-gradient(135deg, rgba(132, 0, 255, 0.3), rgba(168, 85, 247, 0.2));
  color: rgba(132, 0, 255, 1);
  transform: scale(1.1);
}

.contact-info-content {
  flex: 1;
  min-width: 0;
}

.contact-info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-info-value {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact-info-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .contact-info-card {
    padding: 1.25rem;
  }

  .contact-info-icon {
    width: 40px;
    height: 40px;
  }
}

.contact-card-small {
  aspect-ratio: 1/1 !important;
}

.contact-card-large {
  aspect-ratio: 2/2 !important;
}

.contact-card-efficiency {
  aspect-ratio: 2/2 !important;
}

.contact-card-action {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.contact-card-action .card__content {
  align-items: center;
  justify-content: center;
  text-align: center;
}

.contact-card-stat {
  text-align: center;
}

.contact-card-stat .card__content {
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: rgba(132, 0, 255, 1);
  display: block;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.contact-button {
  background: linear-gradient(135deg, rgba(132, 0, 255, 1), rgba(74, 158, 255, 1));
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem auto 0;
}

.contact-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(132, 0, 255, 0.4);
}

.contact-button svg {
  width: 16px;
  height: 16px;
}
