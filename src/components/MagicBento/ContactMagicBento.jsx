import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import MagicBento from './MagicBento';

const ContactMagicBento = ({
  isEnglish = true,
  onContactUs,
  onTryPlatform
}) => {
  const navigate = useNavigate();
  const contactCardData = useMemo(() => [
    {
      color: "#060010",
      title: isEnglish ? "Analytics" : "分析洞察",
      description: isEnglish
        ? "Track user behavior and site performance with comprehensive analytics"
        : "通过全面的分析跟踪用户行为和站点性能",
      label: isEnglish ? "INSIGHTS" : "洞察",
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M3 3V21H21M7 14L12 9L16 13L21 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <circle cx="7" cy="14" r="1" fill="currentColor"/>
          <circle cx="12" cy="9" r="1" fill="currentColor"/>
          <circle cx="16" cy="13" r="1" fill="currentColor"/>
          <circle cx="21" cy="8" r="1" fill="currentColor"/>
        </svg>
      ),
      onClick: () => {
        if (onContactUs) onContactUs();
      },
      className: "contact-card-small"
    },
    {
      color: "#060010",
      title: isEnglish ? "Dashboard" : "仪表板",
      description: isEnglish
        ? "Centralized data view for comprehensive site management"
        : "集中式数据视图，全面管理站点",
      label: isEnglish ? "OVERVIEW" : "概览",
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <rect x="3" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
          <rect x="14" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
          <rect x="3" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
          <rect x="14" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
        </svg>
      ),
      onClick: () => {
        navigate('/main');
      },
      className: "contact-card-small"
    },
    {
      color: "#060010",
      title: isEnglish ? "AI Assistant" : "AI助手",
      description: isEnglish
        ? "Intelligent AI assistant for industrial site analysis and decision support"
        : "智能AI助手，提供工业选址分析和决策支持",
      label: isEnglish ? "AI ASSISTANT" : "AI助手",
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L13.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L10.91 8.26L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
          <path d="M8 12L10 14L16 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
      onClick: () => {
        // 触发右下角AI助手展开
        const aiAssistantButton = document.querySelector('.ai-button-container');
        if (aiAssistantButton) {
          aiAssistantButton.click();
        }
      },
      className: "contact-card-large"
    },
    {
      color: "#060010",
      title: isEnglish ? "Automation" : "自动化",
      description: isEnglish
        ? "Streamline workflows with intelligent automation"
        : "通过智能自动化简化工作流程",
      label: isEnglish ? "EFFICIENCY" : "效率",
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L13.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L10.91 8.26L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
      onClick: () => {
        navigate('/ai-analyzer');
      },
      className: "contact-card-efficiency"
    },
    {
      color: "#060010",
      title: isEnglish ? "Collaboration" : "团队协作",
      description: isEnglish
        ? "Work together seamlessly with integrated collaboration tools"
        : "通过集成协作工具实现无缝团队合作",
      label: isEnglish ? "TEAMWORK" : "团队合作",
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <circle cx="9" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
          <path d="M23 21V19C23 18.1645 22.7155 17.3541 22.2094 16.6988C21.7033 16.0434 20.9983 15.5769 20.2 15.3746" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
      onClick: () => {
        navigate('/contact-us');
      },
      className: "contact-card-small"
    },
    {
      color: "#060010",
      title: isEnglish ? "Security" : "安全防护",
      description: isEnglish
        ? "Enterprise-grade protection for your industrial data"
        : "为您的工业数据提供企业级保护",
      label: isEnglish ? "PROTECTION" : "保护",
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M12 22S8 18 8 12V7L12 5L16 7V12C16 18 12 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
      onClick: () => {
        navigate('/main');
      },
      className: "contact-card-small"
    }
  ], [isEnglish, onContactUs, onTryPlatform]);

  return (
    <div className="contact-bento-grid">
      <div className="contact-header" style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <h2 style={{
          fontSize: '2.5rem',
          fontWeight: 'bold',
          color: 'white',
          marginBottom: '1rem',
          background: 'linear-gradient(135deg, #ffffff, #a855f7)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}>
          {isEnglish ? 'Ready to Transform Your Industrial Strategy?' : '准备好转型您的工业战略了吗？'}
        </h2>
        <p style={{
          fontSize: '1.2rem',
          color: 'rgba(255, 255, 255, 0.8)',
          maxWidth: '700px',
          margin: '0 auto'
        }}>
          {isEnglish
            ? 'Comprehensive platform for industrial site selection, data analytics, and team collaboration'
            : '工业选址、数据分析和团队协作的综合平台'
          }
        </p>
      </div>
      
      <MagicBento
        cardData={contactCardData}
        textAutoHide={true}
        enableStars={true}
        enableSpotlight={true}
        enableBorderGlow={true}
        enableTilt={true}
        enableMagnetism={true}
        clickEffect={true}
        spotlightRadius={300}
        particleCount={12}
        glowColor="132, 0, 255"
      />
      
      {/* 现代化联系信息卡片 */}
      <div className="contact-info-section">
        <div className="contact-info-cards">
          <div className="contact-info-card">
            <div className="contact-info-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <polyline points="22,6 12,13 2,6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div className="contact-info-content">
              <div className="contact-info-label">
                {isEnglish ? 'Email' : '邮箱'}
              </div>
              <div className="contact-info-value">
                <EMAIL>
              </div>
            </div>
          </div>

          <div className="contact-info-card">
            <div className="contact-info-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2"/>
                <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.03 7.03 1 12 1S21 5.03 21 10Z" stroke="currentColor" strokeWidth="2"/>
              </svg>
            </div>
            <div className="contact-info-content">
              <div className="contact-info-label">
                {isEnglish ? 'Location' : '位置'}
              </div>
              <div className="contact-info-value">
                Delaware, United States
              </div>
            </div>
          </div>

          <div className="contact-info-card">
            <div className="contact-info-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59344 1.99522 8.06544 2.16708 8.43945 2.48353C8.81346 2.79999 9.06681 3.23945 9.15999 3.72C9.33657 4.68007 9.63248 5.61273 10.04 6.5C10.2 6.89 10.24 7.33 10.16 7.75L8.89999 9.01C10.3357 11.4135 12.4865 13.5644 14.89 15L16.15 13.74C16.57 13.66 17.01 13.7 17.4 13.86C18.2873 14.2675 19.2199 14.5634 20.18 14.74C20.6657 14.8336 21.1093 15.0902 21.4259 15.4705C21.7425 15.8508 21.9116 16.3287 21.9 16.82L22 16.92Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div className="contact-info-content">
              <div className="contact-info-label">
                {isEnglish ? 'Support' : '支持'}
              </div>
              <div className="contact-info-value">
                {isEnglish ? '24/7 Available' : '24/7 可用'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactMagicBento;
