import React, { useRef, useEffect, useMemo, useCallback } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';
import performanceManager from '../../utils/PerformanceManager';

/**
 * 优化的粒子系统组件
 * 实现LOD、对象池、时间分片等性能优化技术
 */

// 粒子对象池
class ParticlePool {
  constructor(maxSize = 2000) {
    this.pool = [];
    this.maxSize = maxSize;
    this.activeParticles = new Set();
  }
  
  getParticle() {
    let particle;
    if (this.pool.length > 0) {
      particle = this.pool.pop();
    } else {
      particle = {
        position: new THREE.Vector3(),
        velocity: new THREE.Vector3(),
        life: 1.0,
        maxLife: 1.0,
        size: 1.0,
        color: new THREE.Color(),
        active: false
      };
    }
    
    this.activeParticles.add(particle);
    particle.active = true;
    return particle;
  }
  
  releaseParticle(particle) {
    if (this.activeParticles.has(particle)) {
      this.activeParticles.delete(particle);
      particle.active = false;
      
      if (this.pool.length < this.maxSize) {
        this.pool.push(particle);
      }
    }
  }
  
  getActiveCount() {
    return this.activeParticles.size;
  }
  
  clear() {
    this.activeParticles.clear();
    this.pool = [];
  }
}

// 空间分区系统用于优化粒子交互
class SpatialGrid {
  constructor(width, height, cellSize = 50) {
    this.width = width;
    this.height = height;
    this.cellSize = cellSize;
    this.cols = Math.ceil(width / cellSize);
    this.rows = Math.ceil(height / cellSize);
    this.grid = new Array(this.cols * this.rows).fill(null).map(() => []);
  }
  
  clear() {
    this.grid.forEach(cell => cell.length = 0);
  }
  
  insert(particle, x, y) {
    const col = Math.floor(x / this.cellSize);
    const row = Math.floor(y / this.cellSize);
    
    if (col >= 0 && col < this.cols && row >= 0 && row < this.rows) {
      const index = row * this.cols + col;
      this.grid[index].push(particle);
    }
  }
  
  getNearby(x, y, radius = 1) {
    const nearby = [];
    const col = Math.floor(x / this.cellSize);
    const row = Math.floor(y / this.cellSize);
    
    for (let r = row - radius; r <= row + radius; r++) {
      for (let c = col - radius; c <= col + radius; c++) {
        if (c >= 0 && c < this.cols && r >= 0 && r < this.rows) {
          const index = r * this.cols + c;
          nearby.push(...this.grid[index]);
        }
      }
    }
    
    return nearby;
  }
}

const OptimizedParticles = ({
  particleCount = 500,
  particleSpread = 10,
  speed = 0.1,
  particleColors = ['#ffffff', '#888888'],
  particleBaseSize = 80,
  enableInteraction = true,
  className = ''
}) => {
  const meshRef = useRef();
  const particlePool = useRef(new ParticlePool(particleCount * 2));
  const spatialGrid = useRef(new SpatialGrid(window.innerWidth, window.innerHeight));
  const frameCounter = useRef(0);
  const lastUpdateTime = useRef(0);
  const performanceConfig = useRef(performanceManager.getConfig());
  
  // 粒子数据缓存
  const particleData = useRef({
    positions: new Float32Array(particleCount * 3),
    colors: new Float32Array(particleCount * 3),
    sizes: new Float32Array(particleCount),
    activeCount: 0
  });
  
  // 性能监听
  useEffect(() => {
    const handlePerformanceChange = (event, level, config) => {
      performanceConfig.current = config;
      updateParticleCount();
    };
    
    const id = `particles-${Date.now()}`;
    performanceManager.onPerformanceChange(id, handlePerformanceChange);
    
    return () => {
      performanceManager.offPerformanceChange(id);
      particlePool.current.clear();
    };
  }, []);
  
  // 根据性能配置更新粒子数量
  const updateParticleCount = useCallback(() => {
    const config = performanceConfig.current;
    const targetCount = Math.floor(particleCount * config.particleMultiplier);
    
    // 调整粒子数据数组大小
    if (targetCount !== particleData.current.activeCount) {
      const newPositions = new Float32Array(targetCount * 3);
      const newColors = new Float32Array(targetCount * 3);
      const newSizes = new Float32Array(targetCount);
      
      // 复制现有数据
      const copyCount = Math.min(targetCount, particleData.current.activeCount);
      newPositions.set(particleData.current.positions.slice(0, copyCount * 3));
      newColors.set(particleData.current.colors.slice(0, copyCount * 3));
      newSizes.set(particleData.current.sizes.slice(0, copyCount));
      
      particleData.current.positions = newPositions;
      particleData.current.colors = newColors;
      particleData.current.sizes = newSizes;
      particleData.current.activeCount = targetCount;
      
      // 初始化新粒子
      initializeParticles();
    }
  }, [particleCount]);
  
  // 初始化粒子
  const initializeParticles = useCallback(() => {
    const { positions, colors, sizes, activeCount } = particleData.current;
    
    for (let i = 0; i < activeCount; i++) {
      // 位置
      positions[i * 3] = (Math.random() - 0.5) * particleSpread;
      positions[i * 3 + 1] = (Math.random() - 0.5) * particleSpread;
      positions[i * 3 + 2] = (Math.random() - 0.5) * particleSpread;
      
      // 颜色
      const colorIndex = Math.floor(Math.random() * particleColors.length);
      const color = new THREE.Color(particleColors[colorIndex]);
      colors[i * 3] = color.r;
      colors[i * 3 + 1] = color.g;
      colors[i * 3 + 2] = color.b;
      
      // 大小
      sizes[i] = Math.random() * 0.5 + 0.5;
    }
  }, [particleSpread, particleColors]);
  
  // 时间分片更新函数
  const updateParticlesSliced = useCallback((deltaTime, startIndex, endIndex) => {
    const { positions } = particleData.current;
    const config = performanceConfig.current;
    const adjustedSpeed = speed * config.animationSpeedMultiplier;
    
    for (let i = startIndex; i < endIndex; i++) {
      const baseIndex = i * 3;
      
      // 简单的波动动画
      const time = Date.now() * 0.001 * adjustedSpeed;
      positions[baseIndex] += Math.sin(time + i * 0.1) * 0.01;
      positions[baseIndex + 1] += Math.cos(time + i * 0.1) * 0.01;
      positions[baseIndex + 2] += Math.sin(time * 0.5 + i * 0.05) * 0.005;
      
      // 边界检查
      const spread = particleSpread * 0.5;
      if (Math.abs(positions[baseIndex]) > spread) {
        positions[baseIndex] = (Math.random() - 0.5) * particleSpread;
      }
      if (Math.abs(positions[baseIndex + 1]) > spread) {
        positions[baseIndex + 1] = (Math.random() - 0.5) * particleSpread;
      }
      if (Math.abs(positions[baseIndex + 2]) > spread) {
        positions[baseIndex + 2] = (Math.random() - 0.5) * particleSpread;
      }
    }
  }, [speed, particleSpread]);
  
  // 主更新循环
  useFrame((state, deltaTime) => {
    if (!meshRef.current) return;
    
    const config = performanceConfig.current;
    frameCounter.current++;
    
    // 根据性能配置调整更新频率
    if (frameCounter.current % config.updateFrequency !== 0) {
      return;
    }
    
    const currentTime = state.clock.elapsedTime;
    const actualDelta = currentTime - lastUpdateTime.current;
    lastUpdateTime.current = currentTime;
    
    const { positions, activeCount } = particleData.current;
    
    // 时间分片更新 - 每帧只更新一部分粒子
    const sliceSize = Math.ceil(activeCount / config.updateFrequency);
    const startIndex = (frameCounter.current % config.updateFrequency) * sliceSize;
    const endIndex = Math.min(startIndex + sliceSize, activeCount);
    
    updateParticlesSliced(actualDelta, startIndex, endIndex);
    
    // 更新几何体
    if (meshRef.current.geometry) {
      meshRef.current.geometry.attributes.position.needsUpdate = true;
    }
  });
  
  // 几何体和材质
  const geometry = useMemo(() => {
    const geom = new THREE.BufferGeometry();
    
    updateParticleCount();
    initializeParticles();
    
    const { positions, colors, sizes } = particleData.current;
    
    geom.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geom.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    geom.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    
    return geom;
  }, [updateParticleCount, initializeParticles]);
  
  const material = useMemo(() => {
    return new THREE.PointsMaterial({
      size: particleBaseSize * 0.01,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
      sizeAttenuation: true,
      depthWrite: false,
      blending: THREE.AdditiveBlending
    });
  }, [particleBaseSize]);
  
  return (
    <points ref={meshRef} geometry={geometry} material={material} />
  );
};

export default OptimizedParticles;
