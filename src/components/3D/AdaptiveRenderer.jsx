import React, { useRef, useEffect, useMemo, useCallback } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Suspense } from 'react';
import * as THREE from 'three';
import performanceManager from '../../utils/PerformanceManager';

/**
 * 自适应渲染器 - 根据性能动态调整渲染质量
 */

// 视锥剔除工具
class FrustumCuller {
  constructor() {
    this.frustum = new THREE.Frustum();
    this.cameraMatrix = new THREE.Matrix4();
  }
  
  updateFrustum(camera) {
    this.cameraMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
    this.frustum.setFromProjectionMatrix(this.cameraMatrix);
  }
  
  isVisible(object) {
    if (!object.geometry || !object.geometry.boundingSphere) {
      return true; // 如果没有边界球，假设可见
    }
    
    return this.frustum.intersectsSphere(object.geometry.boundingSphere);
  }
}

// LOD管理器
class LODManager {
  constructor() {
    this.lodLevels = {
      high: { distance: 50, quality: 1.0 },
      medium: { distance: 100, quality: 0.6 },
      low: { distance: 200, quality: 0.3 },
      hidden: { distance: Infinity, quality: 0 }
    };
  }
  
  getLODLevel(distance, performanceLevel) {
    const baseMultiplier = performanceLevel === 'high' ? 1.0 : 
                          performanceLevel === 'medium' ? 0.7 : 0.4;
    
    if (distance < this.lodLevels.high.distance * baseMultiplier) {
      return 'high';
    } else if (distance < this.lodLevels.medium.distance * baseMultiplier) {
      return 'medium';
    } else if (distance < this.lodLevels.low.distance * baseMultiplier) {
      return 'low';
    } else {
      return 'hidden';
    }
  }
  
  getQualityMultiplier(lodLevel) {
    return this.lodLevels[lodLevel]?.quality || 0;
  }
}

// 渲染优化器组件
const RenderOptimizer = ({ children }) => {
  const { gl, camera, scene } = useThree();
  const frustumCuller = useRef(new FrustumCuller());
  const lodManager = useRef(new LODManager());
  const frameCounter = useRef(0);
  const performanceConfig = useRef(performanceManager.getConfig());
  const visibleObjects = useRef(new Set());
  const lastCullTime = useRef(0);
  
  // 性能配置更新
  useEffect(() => {
    const handlePerformanceChange = (event, level, config) => {
      performanceConfig.current = config;
      updateRenderSettings();
    };
    
    const id = `renderer-${Date.now()}`;
    performanceManager.onPerformanceChange(id, handlePerformanceChange);
    
    return () => {
      performanceManager.offPerformanceChange(id);
    };
  }, []);
  
  // 更新渲染设置
  const updateRenderSettings = useCallback(() => {
    const config = performanceConfig.current;
    
    // 调整渲染器设置
    gl.setPixelRatio(Math.min(window.devicePixelRatio, config.renderQuality * 2));
    
    // 调整阴影设置
    if (gl.shadowMap) {
      gl.shadowMap.enabled = config.enableComplexEffects;
      gl.shadowMap.type = config.performanceLevel === 'high' ? 
        THREE.PCFSoftShadowMap : THREE.BasicShadowMap;
    }
    
    // 调整抗锯齿
    gl.antialias = config.performanceLevel !== 'low';
    
  }, [gl]);
  
  // 视锥剔除
  const performFrustumCulling = useCallback(() => {
    const currentTime = performance.now();
    
    // 根据性能级别调整剔除频率
    const cullInterval = performanceConfig.current.performanceLevel === 'high' ? 16 : 
                        performanceConfig.current.performanceLevel === 'medium' ? 33 : 66;
    
    if (currentTime - lastCullTime.current < cullInterval) {
      return;
    }
    
    lastCullTime.current = currentTime;
    frustumCuller.current.updateFrustum(camera);
    visibleObjects.current.clear();
    
    scene.traverse((object) => {
      if (object.isMesh || object.isPoints) {
        const isVisible = frustumCuller.current.isVisible(object);
        object.visible = isVisible;
        
        if (isVisible) {
          visibleObjects.current.add(object);
          
          // LOD处理
          const distance = camera.position.distanceTo(object.position);
          const lodLevel = lodManager.current.getLODLevel(distance, performanceConfig.current.performanceLevel);
          const qualityMultiplier = lodManager.current.getQualityMultiplier(lodLevel);
          
          // 根据LOD调整对象属性
          if (object.material) {
            object.material.opacity = Math.min(1.0, qualityMultiplier * 1.2);
            
            // 对于粒子系统，调整粒子数量
            if (object.isPoints && object.geometry.attributes.position) {
              const originalCount = object.geometry.attributes.position.count;
              const targetCount = Math.floor(originalCount * qualityMultiplier);
              object.geometry.setDrawRange(0, targetCount);
            }
          }
          
          // 距离太远时隐藏
          if (lodLevel === 'hidden') {
            object.visible = false;
          }
        }
      }
    });
  }, [camera, scene]);
  
  // 主渲染循环优化
  useFrame(() => {
    frameCounter.current++;
    
    // 执行视锥剔除
    performFrustumCulling();
    
    // 根据性能级别调整渲染频率
    const config = performanceConfig.current;
    if (frameCounter.current % config.updateFrequency === 0) {
      // 强制垃圾回收（在空闲时）
      if (frameCounter.current % 300 === 0 && window.requestIdleCallback) {
        window.requestIdleCallback(() => {
          if (window.gc) {
            window.gc();
          }
        });
      }
    }
  });
  
  return null;
};

// 自适应Canvas组件
const AdaptiveCanvas = ({ 
  children, 
  camera = { position: [0, 0, 5], fov: 45 },
  enableShadows = true,
  enableAntialias = true,
  ...props 
}) => {
  const canvasRef = useRef();
  const performanceConfig = useRef(performanceManager.getConfig());
  
  // 根据性能动态调整Canvas设置
  const canvasSettings = useMemo(() => {
    const config = performanceConfig.current;
    
    return {
      dpr: Math.min(window.devicePixelRatio, config.renderQuality * 2),
      antialias: enableAntialias && config.performanceLevel !== 'low',
      alpha: true,
      powerPreference: config.performanceLevel === 'high' ? 'high-performance' : 'default',
      stencil: config.enableComplexEffects,
      depth: true,
      premultipliedAlpha: false,
      preserveDrawingBuffer: false,
      failIfMajorPerformanceCaveat: false,
      ...props
    };
  }, [enableAntialias, props]);
  
  // 自适应相机设置
  const adaptiveCamera = useMemo(() => {
    const config = performanceConfig.current;
    
    return {
      ...camera,
      far: config.performanceLevel === 'high' ? 1000 : 
           config.performanceLevel === 'medium' ? 500 : 250,
      near: 0.1
    };
  }, [camera]);
  
  return (
    <Canvas
      ref={canvasRef}
      camera={adaptiveCamera}
      shadows={enableShadows && performanceConfig.current.enableComplexEffects}
      {...canvasSettings}
    >
      <RenderOptimizer />
      <Suspense fallback={null}>
        {children}
      </Suspense>
    </Canvas>
  );
};

// 性能监控显示组件（开发用）
const PerformanceMonitor = ({ show = false }) => {
  const [stats, setStats] = React.useState(performanceManager.getStats());
  
  useEffect(() => {
    if (!show) return;
    
    const interval = setInterval(() => {
      setStats(performanceManager.getStats());
    }, 1000);
    
    return () => clearInterval(interval);
  }, [show]);
  
  if (!show) return null;
  
  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 10000,
      fontFamily: 'monospace'
    }}>
      <div>FPS: {stats.frameRate.toFixed(1)}</div>
      <div>Level: {stats.performanceLevel}</div>
      <div>Particles: {(stats.adaptiveConfig.particleMultiplier * 100).toFixed(0)}%</div>
      <div>Quality: {(stats.adaptiveConfig.renderQuality * 100).toFixed(0)}%</div>
    </div>
  );
};

export { AdaptiveCanvas, RenderOptimizer, PerformanceMonitor };
export default AdaptiveCanvas;
