.sidebar-skeleton {
  padding: 20px;
  animation: fadeIn 0.3s ease-in-out;
}

.skeleton-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-item {
  padding: 12px;
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.03);
}

.skeleton-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.skeleton-name {
  height: 16px;
  background: rgba(0, 0, 0, 0.08);
  border-radius: 3px;
  width: 60%;
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-price {
  height: 16px;
  background: rgba(0, 0, 0, 0.08);
  border-radius: 3px;
  width: 25%;
  animation: pulse 1.5s ease-in-out infinite;
  animation-delay: 0.2s;
}

.skeleton-item-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skeleton-location {
  height: 12px;
  background: rgba(0, 0, 0, 0.06);
  border-radius: 3px;
  width: 40%;
  animation: pulse 1.5s ease-in-out infinite;
  animation-delay: 0.4s;
}

.skeleton-area {
  height: 12px;
  background: rgba(0, 0, 0, 0.06);
  border-radius: 3px;
  width: 30%;
  animation: pulse 1.5s ease-in-out infinite;
  animation-delay: 0.6s;
}

/* 简单脉动动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 夜间模式支持 */
.map-night-mode .skeleton-title,
.map-night-mode .skeleton-subtitle,
.map-night-mode .skeleton-name,
.map-night-mode .skeleton-price,
.map-night-mode .skeleton-location,
.map-night-mode .skeleton-area {
  background: linear-gradient(90deg, #2d3748 25%, #4a5568 50%, #2d3748 75%);
  background-size: 200% 100%;
}

.map-night-mode .skeleton-item {
  background: rgba(45, 55, 72, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .skeleton-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .skeleton-name,
  .skeleton-price {
    width: 100%;
  }
}
