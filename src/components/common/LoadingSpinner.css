.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 简约旋转圆圈 */
.simple-spinner {
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: #3498db;
  animation: simple-spin 0.8s linear infinite;
}

/* 尺寸变体 */
.spinner-small {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.spinner-medium {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.spinner-large {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

/* 颜色变体 */
.spinner-primary {
  border-top-color: #3498db;
}

.spinner-secondary {
  border-top-color: #95a5a6;
}

.spinner-success {
  border-top-color: #27ae60;
}

.spinner-warning {
  border-top-color: #f39c12;
}

.spinner-danger {
  border-top-color: #e74c3c;
}

/* 简单旋转动画 */
@keyframes simple-spin {
  to {
    transform: rotate(360deg);
  }
}

/* 加载文字 */
.loading-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  text-align: center;
  margin-top: 8px;
}

/* 夜间模式支持 */
.map-night-mode .loading-text {
  color: #ccc;
}

.map-night-mode .spinner-primary .spinner-ring {
  border-top-color: #5dade2;
}

/* 脉动效果变体 */
.loading-spinner.pulse .spinner-ring {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}
