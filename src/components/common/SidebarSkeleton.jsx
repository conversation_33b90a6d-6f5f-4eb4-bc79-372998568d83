import React from 'react';
import './SidebarSkeleton.css';

const SidebarSkeleton = ({ itemCount = 8 }) => {
  return (
    <div className="sidebar-skeleton">
      <div className="skeleton-header">
        <div className="skeleton-title"></div>
        <div className="skeleton-subtitle"></div>
      </div>
      
      <div className="skeleton-list">
        {Array.from({ length: itemCount }, (_, index) => (
          <div key={index} className="skeleton-item">
            <div className="skeleton-item-header">
              <div className="skeleton-name"></div>
              <div className="skeleton-price"></div>
            </div>
            <div className="skeleton-item-details">
              <div className="skeleton-location"></div>
              <div className="skeleton-area"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SidebarSkeleton;
