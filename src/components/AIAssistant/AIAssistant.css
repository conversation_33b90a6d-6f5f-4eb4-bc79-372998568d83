/* <PERSON> Styles */
.ai-button-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 50;
  cursor: pointer;
}

.ai-button-wrapper {
  position: relative;
  width: 5rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-button-background {
  position: absolute;
  inset: 0;
  background: rgba(30, 41, 59, 0.2);
  backdrop-filter: blur(4px);
  border-radius: 0.5rem;
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.ai-button-svg {
  position: relative;
  z-index: 10;
}

/* Default State Animations */
.default-state {
  transition: opacity 0.5s ease;
}

.default-state.visible {
  opacity: 1;
}

.default-state.hidden {
  opacity: 0;
}

.main-dash-path {
  stroke: rgba(59, 130, 246, 0.8);
  stroke-width: 2;
  stroke-dasharray: 4, 4;
  fill: none;
  animation: dashBounce 3s ease-in-out infinite;
}

.secondary-dash-path {
  stroke: rgba(59, 130, 246, 0.4);
  stroke-width: 1.5;
  stroke-dasharray: 3, 3;
  fill: none;
  animation: dashBounce 3s ease-in-out infinite 0.5s;
}

.connection-point {
  fill: rgba(59, 130, 246, 0.6);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes dashBounce {
  0%,
  100% {
    stroke-dashoffset: 0;
    transform: translateY(0);
  }
  50% {
    stroke-dashoffset: -8px;
  transform: translateY(-2px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Hover State Animations */
.hover-state {
  transition: opacity 0.5s ease;
}

.hover-state.visible {
  opacity: 1;
}

.hover-state.hidden {
  opacity: 0;
}

.letter-a,
.letter-i,
.connector-line {
  stroke: rgba(59, 130, 246, 1);
  stroke-width: 2.5;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

.letter-a {
  stroke-dasharray: 60;
  stroke-dashoffset: 60;
}

.letter-a.animate {
  stroke-dashoffset: 0;
  transition: stroke-dashoffset 0.8s ease-out;
}

.letter-i {
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
}

.letter-i.animate {
  stroke-dashoffset: 0;
  transition: stroke-dashoffset 0.8s ease-out 0.3s;
}

.connector-line {
  stroke-width: 1;
  stroke-dasharray: 4;
  stroke-dashoffset: 4;
}

.connector-line.animate {
  stroke-dashoffset: 0;
  transition: stroke-dashoffset 0.4s ease-out 0.6s;
}

/* Tooltip */
.tooltip {
  position: absolute;
  top: -3rem;
  left: 50%;
  transform: translateX(-50%);
  transition: all 0.3s ease;
}

.tooltip.visible {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.tooltip.hidden {
  opacity: 0;
  transform: translateX(-50%) translateY(0.5rem);
}

.tooltip-content {
  background: rgb(30, 41, 59);
  color: rgb(226, 232, 240);
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  border: 1px solid rgb(71, 85, 105);
  white-space: nowrap;
}

.tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgb(30, 41, 59);
}

/* Main AI Assistant Overlay */
.ai-assistant-overlay {
  position: fixed;
  inset: 0;
  z-index: 9999;
  background: rgba(2, 6, 23, 0.92);
  backdrop-filter: blur(6px);
}

/* Control Buttons */
.control-buttons {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  display: flex;
  gap: 0.5rem;
  z-index: 20;
}

.control-button {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(4px);
  border: 1px solid rgb(71, 85, 105);
  color: rgb(148, 163, 184);
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.control-button:hover {
  color: white;
  background: rgb(51, 65, 85);
  transform: scale(1.05);
}

.control-button.build-control {
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.9), rgba(234, 179, 8, 0.9));
  color: white;
  border: 2px solid rgba(249, 115, 22, 0.8);
  font-size: 1.2rem;
  box-shadow: 
    0 4px 20px rgba(249, 115, 22, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  animation: pulseGlow 3s ease-in-out infinite;
  transform: scale(1.05);
}

.control-button.build-control::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.control-button.build-control:hover:not(:disabled)::before {
  opacity: 1;
}

.control-button.build-control:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(249, 115, 22, 1), rgba(234, 179, 8, 1));
  transform: scale(1.15);
  box-shadow: 
    0 8px 25px rgba(249, 115, 22, 0.5),
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  animation: none;
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 
      0 4px 20px rgba(249, 115, 22, 0.4),
      0 2px 8px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow: 
      0 6px 25px rgba(249, 115, 22, 0.6),
      0 3px 12px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
}

.control-icon {
  width: 1rem;
  height: 1rem;
}

/* 知识库构建按钮容器和提示 */
.build-kb-container {
  position: relative;
  display: inline-block;
}

.build-kb-tooltip {
  position: absolute;
  bottom: calc(100% + 0.75rem);
  right: 0;
  background: rgba(15, 23, 42, 0.95);
  color: white;
  padding: 1rem 1.25rem;
  border-radius: 0.75rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(249, 115, 22, 0.4);
  min-width: 260px;
  max-width: 320px;
  z-index: 1003;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px) scale(0.95);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 确保tooltip不会超出屏幕右边界 */
@media (max-width: 768px) {
  .build-kb-tooltip {
    right: -50px;
    min-width: 240px;
    max-width: calc(100vw - 2rem);
  }
}

/* 对于更小的屏幕，进一步调整 */
@media (max-width: 480px) {
  .build-kb-tooltip {
    right: -100px;
    min-width: 200px;
  }
}

.build-kb-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  right: 20px;
  border: 6px solid transparent;
  border-top-color: rgba(15, 23, 42, 0.95);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 响应式箭头位置调整 */
@media (max-width: 768px) {
  .build-kb-tooltip::after {
    right: 70px;
  }
}

@media (max-width: 480px) {
  .build-kb-tooltip::after {
    right: 120px;
  }
}

.build-kb-container:hover .build-kb-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

.tooltip-title {
  color: rgb(249, 115, 22);
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tooltip-title::before {
  content: '🔨';
  font-size: 1rem;
}

.tooltip-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.8rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.tooltip-benefits {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
  line-height: 1.3;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.building-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(59, 130, 246, 0.15);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  position: relative;
  overflow: hidden;
}

.building-indicator::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), transparent);
  animation: shimmer 2s ease-in-out infinite;
}

.modern-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.spinner-ring {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-left: 2px solid rgb(59, 130, 246);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.kb-status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(34, 197, 94, 0.15);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 0.5rem;
  color: rgb(34, 197, 94);
}

.kb-ready-icon {
  font-size: 1rem;
  font-weight: bold;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes shimmer {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

/* Node Counter */
.node-counter {
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  z-index: 20;
}

.counter-badge {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(4px);
  border: 1px solid rgb(71, 85, 105);
  color: rgb(203, 213, 225);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

/* Main Content */
.main-content {
  padding-bottom: 125px;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.background-pattern {
  position: absolute;
  inset: 0;
  opacity: 0.1;
}

.gradient-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom right, rgba(30, 58, 138, 0.2), rgba(88, 28, 135, 0.2), rgba(15, 23, 42, 0.2));
}

.dot-pattern {
  position: absolute;
  inset: 0;
  background-image: radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.1) 1px, transparent 0);
  background-size: 50px 50px;
}

/* Node Canvas */
.node-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 2rem;
  user-select: none;
}

.connection-svg {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.connection-line {
  stroke: rgba(59, 130, 246, 0.8);
  stroke-width: 2.5;
  stroke-dasharray: 8, 4;
  filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.4));
}

/* AI Nodes */
.ai-node {
  position: absolute;
  width: 16rem;
  padding: 0.75rem;
  cursor: move;
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
  border: 1px solid;
  z-index: 10;
  border-radius: 0.75rem;
}

.ai-node:hover {
  transform: translate(-50%, -50%) scale(1.05);
}

.ai-node.active {
  ring: 2px solid rgb(59, 130, 246);
  transform: translate(-50%, -50%) scale(1.05);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.ai-node.dragging {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translate(-50%, -50%) scale(1.02);
  z-index: 100;
}

/* Node Types */
.node-insight {
  background: linear-gradient(to bottom right, rgba(59, 130, 246, 0.15), rgba(6, 182, 212, 0.15));
  border-color: rgba(59, 130, 246, 0.4);
}

.node-action {
  background: linear-gradient(to bottom right, rgba(34, 197, 94, 0.15), rgba(16, 185, 129, 0.15));
  border-color: rgba(34, 197, 94, 0.4);
}

.node-data {
  background: linear-gradient(to bottom right, rgba(168, 85, 247, 0.15), rgba(139, 92, 246, 0.15));
  border-color: rgba(168, 85, 247, 0.4);
}

.node-suggestion {
  background: linear-gradient(to bottom right, rgba(249, 115, 22, 0.15), rgba(234, 179, 8, 0.15));
  border-color: rgba(249, 115, 22, 0.4);
}

.ai-node.error {
  background: linear-gradient(to bottom right, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.15));
  border-color: rgba(239, 68, 68, 0.4);
}

.node-content {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.node-icon-wrapper {
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.node-icon {
  width: 1rem;
  height: 1rem;
  color: white;
}

.node-text {
  flex: 1;
  min-width: 0;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.node-badge {
  font-size: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.sparkle-icon {
  width: 0.75rem;
  height: 0.75rem;
  color: rgb(96, 165, 250);
}

.node-title {
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-description {
  color: rgb(203, 213, 225);
  font-size: 0.75rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.node-actions {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.explore-button {
  width: 100%;
  background: rgb(37, 99, 235);
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.explore-button:hover {
  background: rgb(29, 78, 216);
}

.arrow-icon {
  width: 0.75rem;
  height: 0.75rem;
}

.typing-indicator {
  animation: blink 1s infinite;
  color: #4dc8ff;
  margin-left: 0.25rem;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 展开内容样式 */
.node-expanded-content {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: rgba(15, 23, 42, 0.7);
  border-radius: 0.5rem;
  border: 1px solid rgba(59, 130, 246, 0.3);
  backdrop-filter: blur(8px);
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.5) transparent;
}

.node-expanded-content::-webkit-scrollbar {
  width: 6px;
}

.node-expanded-content::-webkit-scrollbar-track {
  background: transparent;
}

.node-expanded-content::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 3px;
}

.expanded-text {
  color: #e2e8f0;
  line-height: 1.6;
  font-size: 0.95rem;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.sources-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

.sources-section h4 {
  margin: 0 0 0.5rem 0;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
}

.source-item {
  background: rgba(59, 130, 246, 0.1);
  padding: 0.5rem;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.source-type {
  color: #60a5fa;
  font-weight: 500;
}

.source-files {
  color: #cbd5e1;
}

.collapse-button {
  background: rgba(148, 163, 184, 0.2);
  border: 1px solid rgba(148, 163, 184, 0.3);
  color: #94a3b8;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  margin-top: 1rem;
  width: 100%;
  justify-content: center;
}

.collapse-button:hover {
  background: rgba(148, 163, 184, 0.3);
  color: white;
}

.minimize-icon {
  width: 1rem;
  height: 1rem;
}

/* 展开状态的节点 */
.ai-node.expanded {
  width: 320px;
  max-width: 85vw;
  z-index: 1000;
  transform: translate(-50%, -50%) scale(1.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-node.expanded .node-content {
  max-height: none;
}

.ai-node.expanded .node-description {
  max-height: 60px;
  overflow: hidden;
}

/* 节点管理功能样式 */
.node-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.node-header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.node-menu-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  padding: 0.25rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-menu-button:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.menu-icon {
  width: 1rem;
  height: 1rem;
}

.node-menu {
  position: fixed;
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  backdrop-filter: blur(12px);
  padding: 0.5rem;
  min-width: 180px;
  z-index: 1001;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  /* 动态定位将通过JavaScript设置 */
}

/* 展开状态下菜单位置调整 - 现在使用动态定位 */

.menu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.5rem;
  background: transparent;
  border: none;
  color: #e2e8f0;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.menu-item:hover {
  background: rgba(59, 130, 246, 0.2);
  color: white;
}

.menu-item-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.menu-divider {
  height: 1px;
  background: rgba(59, 130, 246, 0.2);
  margin: 0.5rem 0;
}

.menu-section-title {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 600;
  margin: 0.5rem 0 0.25rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.connection-item {
  font-size: 0.8rem;
  color: #cbd5e1;
}

/* 连接模式样式 */
.connection-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: rgba(59, 130, 246, 0.8);
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  color: white;
  z-index: 10;
  transition: all 0.2s ease;
}

.connection-indicator.selected {
  background: rgba(34, 197, 94, 0.8);
  transform: scale(1.1);
}





@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}



/* 控制按钮活跃状态 */
.control-button.active {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.6);
  color: #60a5fa;
}

.control-button.active:hover {
  background: rgba(59, 130, 246, 0.4);
}

.control-button.menu-open {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
}

/* 控制按钮包装器 */
.control-button-wrapper {
  position: relative;
  display: inline-block;
}

/* 链接下拉菜单 */
.link-dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 280px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3),
              0 0 0 1px rgba(59, 130, 246, 0.2);
  z-index: 1000;
  animation: menuSlideIn 0.2s ease-out;
  overflow: hidden;
}

@keyframes menuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.menu-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  pointer-events: none; /* 确保头部不可点击 */
}

.menu-header-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #60a5fa;
}

.menu-title {
  font-weight: 600;
  color: white;
  font-size: 0.875rem;
}

.menu-content {
  padding: 0.5rem;
}

.menu-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.75rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.menu-option:hover {
  background: rgba(59, 130, 246, 0.1);
}

.menu-option.active {
  background: rgba(59, 130, 246, 0.15);
}

.option-info {
  flex: 1;
}

.option-title {
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.option-description {
  color: #94a3b8;
  font-size: 0.75rem;
  line-height: 1.4;
}

.option-toggle {
  margin-left: 1rem;
}

.toggle-switch {
  width: 44px;
  height: 24px;
  background: rgba(71, 85, 105, 0.5);
  border-radius: 12px;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.toggle-switch.on {
  background: rgba(59, 130, 246, 0.8);
}

.toggle-handle {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch.on .toggle-handle {
  transform: translateX(20px);
}

.connection-status {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 8px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.status-label {
  color: #94a3b8;
  font-size: 0.75rem;
}

.status-value {
  color: #60a5fa;
  font-weight: 600;
  font-size: 0.75rem;
}

.status-hint {
  color: #94a3b8;
  font-size: 0.7rem;
  font-style: italic;
}

/* 连接模式下的节点选中状态 */
.ai-node.selected-for-connection {
  border-color: rgba(34, 197, 94, 0.8);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.3), 
              0 8px 32px rgba(34, 197, 94, 0.2);
  transform: translate(-50%, -50%) scale(1.02);
}

/* 连接线交互样式 */
.connection-line.interactive {
  stroke-width: 2;
  stroke: #3b82f6;
  stroke-dasharray: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.connection-line.interactive:hover {
  stroke-width: 3;
  stroke: #60a5fa;
}

.connection-delete-button {
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.connection-delete-button:hover {
  opacity: 1;
}

.delete-button-bg {
  fill: rgba(239, 68, 68, 0.9);
  stroke: white;
  stroke-width: 1;
}

.delete-button-text {
  fill: white;
  font-size: 12px;
  font-weight: bold;
  pointer-events: none;
}

/* Input Area */
.input-area {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1.25rem 3rem 1.75rem 3rem;
  background: linear-gradient(to top, rgba(15, 23, 42, 0.98), rgba(15, 23, 42, 0.9));
  backdrop-filter: blur(12px);
  border-top: 1px solid rgba(71, 85, 105, 0.3);
  z-index: 10;
  min-height: 105px;
}

.input-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.input-row {
  display: flex;
  align-items: stretch;
  gap: 0.75rem;
  margin-bottom: 1rem;
  width: 100%;
  height: 52px;
}

/* 网络搜索开关样式 */
.web-search-toggle {
  display: flex;
  align-items: center;
}

.toggle-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 28px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(71, 85, 105, 0.6);
  background: rgba(30, 41, 59, 0.9);
  color: rgba(148, 163, 184, 0.7);
  height: 52px;
  white-space: nowrap;
  cursor: pointer;
  backdrop-filter: blur(12px);
  user-select: none;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.toggle-button:hover {
  background: rgba(51, 65, 85, 0.95);
  border-color: rgba(59, 130, 246, 0.7);
  color: rgba(203, 213, 225, 0.9);
  transform: translateY(-1px);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.toggle-button.enabled {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.95), rgba(37, 99, 235, 0.95));
  color: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.9);
  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.toggle-button.enabled:hover {
  background: linear-gradient(135deg, rgba(37, 99, 235, 1), rgba(29, 78, 216, 1));
  color: white;
  transform: translateY(-1px);
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.toggle-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  opacity: 0.9;
  transition: all 0.2s ease;
}

.toggle-button:hover .toggle-icon {
  opacity: 1;
  transform: scale(1.05);
}

.toggle-button.enabled .toggle-icon {
  opacity: 1;
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.3));
}

.toggle-text {
  font-size: 12px;
  font-weight: 500;
}

.input-wrapper {
  flex: 1;
  position: relative;
}

.message-input {
  width: 100%;
  height: 52px;
  padding: 0 1.5rem;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 26px;
  color: white;
  font-size: 0.95rem;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.message-input::placeholder {
  color: rgba(148, 163, 184, 0.8);
  font-size: 0.95rem;
}

.message-input:focus {
  border-color: rgba(59, 130, 246, 0.7);
  box-shadow: 
    0 0 0 4px rgba(59, 130, 246, 0.15),
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  background: rgba(30, 41, 59, 0.85);
  transform: translateY(-1px);
}

.message-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-button {
  background: linear-gradient(135deg, rgb(37, 99, 235), rgb(59, 130, 246));
  color: white;
  border: none;
  border-radius: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 52px;
  height: 52px;
  flex-shrink: 0;
  box-shadow: 
    0 4px 16px rgba(37, 99, 235, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.send-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 26px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.send-button:hover:not(:disabled)::before {
  opacity: 1;
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, rgb(29, 78, 216), rgb(37, 99, 235));
  transform: translateY(-1px) scale(1.02);
  box-shadow: 
    0 6px 20px rgba(37, 99, 235, 0.4),
    0 3px 10px rgba(0, 0, 0, 0.2);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.send-icon {
  width: 1.25rem;
  height: 1.25rem;
  position: relative;
  z-index: 1;
}

.quick-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
  justify-content: center;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
}

.quick-action-button {
  border: 1px solid rgba(71, 85, 105, 0.3);
  color: rgba(203, 213, 225, 0.95);
  background: rgba(30, 41, 59, 0.4);
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.6rem 1.2rem;
  border-radius: 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(12px);
  white-space: nowrap;
  text-align: center;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
  position: relative;
  overflow: hidden;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  letter-spacing: 0.02em;
  min-width: 140px;
}

.quick-action-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 18px;
}

.quick-action-button:hover::before {
  opacity: 1;
}

.quick-action-button:hover {
  background: rgba(51, 65, 85, 0.7);
  border-color: rgba(59, 130, 246, 0.4);
  color: white;
  transform: translateY(-1px) scale(1.02);
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.15),
    0 2px 8px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-assistant-overlay {
    padding: 1rem;
  }
  
  .ai-node {
    width: 14rem;
  }
  
  .input-area {
    padding: 1.5rem 2rem 2rem 2rem;
    min-height: 140px;
  }
  
  .input-container {
    max-width: none;
  }
  
  .main-content {
    padding-bottom: 170px;
  }
  
  .control-buttons {
    top: 1rem;
    right: 1rem;
  }
  
  .node-counter {
    top: 1rem;
    left: 1rem;
  }
  
  .input-row {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }
  
  .message-input {
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
    min-height: 52px;
  }
  
  .send-button {
    width: 52px;
    height: 52px;
  }
  
  .send-icon {
    width: 1rem;
    height: 1rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.6rem;
  }
  
  .quick-action-button {
    font-size: 0.8rem;
    padding: 0.8rem 1.2rem;
  }




}

@media (max-width: 480px) {
  .ai-button-container {
    bottom: 1rem;
    right: 1rem;
  }
  
  .ai-node {
    width: 12rem;
    font-size: 0.75rem;
  }
  
  .node-title {
    font-size: 0.75rem;
  }
  
  .node-description {
    font-size: 0.6875rem;
  }
  
  .input-area {
    padding: 1.25rem 1.5rem 1.75rem 1.5rem;
    min-height: 120px;
  }
  
  .main-content {
    padding-bottom: 140px;
  }
  
  .input-row {
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }
  
  .message-input {
    padding: 0.875rem 1.25rem;
    font-size: 0.875rem;
    min-height: 48px;
    border-radius: 2rem;
  }
  
  .send-button {
    width: 48px;
    height: 48px;
  }
  
  .send-icon {
    width: 1rem;
    height: 1rem;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    margin-top: 0.75rem;
  }
  
  .quick-action-button {
    font-size: 0.8rem;
    padding: 0.75rem 1rem;
    border-radius: 1.25rem;
  }
}

/* 暗色模式优化 */
@media (prefers-color-scheme: dark) {
  .ai-assistant-overlay {
    background: rgba(0, 0, 0, 0.97);
  }
  
  .background-pattern {
    opacity: 0.05;
  }
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  .ai-node {
    transition: none;
  }
  
  .control-button {
    transition: none;
  }
  
  .send-button {
    transition: none;
  }
  
  .main-dash-path,
  .secondary-dash-path,
  .connection-point {
    animation: none;
  }
  
  .typing-indicator {
    animation: none;
  }
}

/* 移动端节点管理优化 */
@media (max-width: 768px) {
  .ai-node.expanded {
    width: 280px;
    max-width: 95vw;
  }

  .node-menu {
    min-width: 160px;
    right: calc(100% + 0.25rem);
    top: -0.25rem;
  }

  .menu-item {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }



  .connection-indicator {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }

  .node-expanded-content {
    max-height: 250px;
    padding: 0.5rem;
  }

  .control-buttons {
    gap: 0.25rem;
  }

  .control-button {
    width: 40px;
    height: 40px;
  }

  .main-content {
    padding-bottom: 110px;
  }

  .input-area {
    padding: 1rem 1.5rem 1.25rem 1.5rem;
    min-height: 85px;
  }

  .input-row {
    height: 44px;
    margin-bottom: 0.75rem;
  }

  .message-input {
    height: 44px;
    padding: 0 1rem;
    font-size: 0.9rem;
  }

  .send-button {
    width: 44px;
    height: 44px;
  }

  .quick-actions {
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .quick-action-button {
    padding: 0.5rem 0.9rem;
    font-size: 0.75rem;
    height: 32px;
    border-radius: 16px;
    min-width: 110px;
    letter-spacing: 0.01em;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ai-node {
    border-width: 2px;
  }
  
  .control-button {
    border-width: 2px;
  }
  
  .message-input {
    border-width: 2px;
  }
}
