/**
 * Cluster Boundary Layer Component
 * 根据缩放级别动态显示cluster边界和数据点
 */

import React, { useEffect, useState } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import ClusterBoundaryService from '../../services/clusterBoundaryService';

const ClusterBoundaryLayer = ({ 
  onDataLoad, 
  onClusterClick,
  isVisible = true 
}) => {
  const map = useMap();
  const [currentZoom, setCurrentZoom] = useState(map.getZoom());
  const [layerGroup, setLayerGroup] = useState(null);
  const [currentDataType, setCurrentDataType] = useState(null);

  // 初始化图层组
  useEffect(() => {
    const group = L.layerGroup().addTo(map);
    setLayerGroup(group);

    return () => {
      if (group) {
        map.removeLayer(group);
      }
    };
  }, [map]);

  // 监听缩放变化
  useEffect(() => {
    const handleZoomEnd = () => {
      const newZoom = map.getZoom();
      setCurrentZoom(newZoom);
    };

    map.on('zoomend', handleZoomEnd);
    
    return () => {
      map.off('zoomend', handleZoomEnd);
    };
  }, [map]);

  // 根据缩放级别加载和显示数据
  useEffect(() => {
    if (!layerGroup || !isVisible) return;

    const loadAndDisplayData = async () => {
      try {
        console.log(`🔍 Loading data for zoom level ${currentZoom}`);
        
        const result = await ClusterBoundaryService.getDataForZoomLevel(currentZoom);
        
        // 清除现有图层
        layerGroup.clearLayers();
        
        if (result.type === 'boundaries') {
          displayBoundariesOnly(result.data);
        } else if (result.type === 'boundaries_and_samples') {
          displayBoundariesWithSamples(result.data);
        } else if (result.type === 'detailed') {
          displayDetailedData(result.data);
          // 通知父组件数据已加载
          if (onDataLoad) {
            onDataLoad(result.data);
          }
        }
        
        setCurrentDataType(result.type);
        console.log(`✅ Displayed ${result.description}`);
        
      } catch (error) {
        console.error('❌ Failed to load cluster data:', error);
      }
    };

    loadAndDisplayData();
  }, [currentZoom, layerGroup, isVisible]);

  /**
   * 显示cluster边界（远距离视图）
   */
  const displayBoundariesOnly = (boundaries) => {
    Object.values(boundaries).forEach(cluster => {
      if (cluster.boundary && cluster.boundary.length > 2) {
        // 创建边界多边形
        const latLngs = cluster.boundary.map(point => [point.latitude, point.longitude]);
        
        const polygon = L.polygon(latLngs, {
          color: ClusterBoundaryService.getClusterColor(cluster.id),
          fillColor: ClusterBoundaryService.getClusterColor(cluster.id),
          fillOpacity: 0.1,
          weight: 2,
          opacity: 0.8
        });

        // 添加点击事件
        polygon.on('click', () => {
          if (onClusterClick) {
            onClusterClick(cluster.id, cluster);
          }
        });

        // 暂时禁用悬停提示，避免持久弹窗问题
        // polygon.bindTooltip(`
        //   <div style="font-size: 12px;">
        //     <strong>Cluster ${cluster.id}</strong><br/>
        //     Parcels: ${cluster.totalParcels?.toLocaleString() || 'N/A'}<br/>
        //     Avg NOI: $${cluster.stats?.avg_noi_per_parcel?.toLocaleString() || 'N/A'}
        //   </div>
        // `, {
        //   sticky: true,
        //   className: 'cluster-boundary-tooltip'
        // });

        layerGroup.addLayer(polygon);

        // 添加cluster标签
        const centerMarker = L.marker([cluster.center.latitude, cluster.center.longitude], {
          icon: L.divIcon({
            className: 'cluster-label',
            html: `<div style="
              background: ${ClusterBoundaryService.getClusterColor(cluster.id)};
              color: white;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 11px;
              font-weight: bold;
              text-align: center;
              box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            ">Cluster ${cluster.id}</div>`,
            iconSize: [60, 24],
            iconAnchor: [30, 12]
          })
        });

        layerGroup.addLayer(centerMarker);
      }
    });
  };

  /**
   * 显示边界和采样点（中距离视图）
   */
  const displayBoundariesWithSamples = (data) => {
    const { boundaries, samples } = data;
    
    // 先显示边界
    displayBoundariesOnly(boundaries);
    
    // 再添加采样点
    Object.entries(samples).forEach(([clusterId, parcels]) => {
      parcels.forEach((parcel, index) => {
        const marker = L.circleMarker([parcel.latitude, parcel.longitude], {
          radius: 4,
          fillColor: ClusterBoundaryService.getClusterColor(parseInt(clusterId)),
          color: '#fff',
          weight: 1,
          opacity: 1,
          fillOpacity: 0.8
        });

        // 暂时禁用工具提示，避免持久弹窗问题
        // marker.bindTooltip(`
        //   <div style="font-size: 11px;">
        //     <strong>Parcel ${parcel.apn}</strong><br/>
        //     Cluster: ${clusterId}<br/>
        //     NOI: $${parcel.noi?.toLocaleString() || 'N/A'}
        //   </div>
        // `, {
        //   className: 'parcel-sample-tooltip'
        // });

        marker.on('click', () => {
          if (onClusterClick) {
            onClusterClick(parseInt(clusterId), parcel);
          }
        });

        layerGroup.addLayer(marker);
      });
    });
  };

  /**
   * 显示详细数据点（近距离视图）
   * 在高缩放级别时，让MarkerClusterComponent处理点聚合显示
   */
  const displayDetailedData = (parcels) => {
    // 清除边界显示，让MarkerClusterComponent接管
    layerGroup.clearLayers();

    // 通知父组件有详细数据可用，让MarkerClusterComponent处理聚合
    console.log(`🎯 高缩放级别: 移交给MarkerClusterComponent处理 ${parcels.length} 个地块的聚合显示`);
  };

  return null; // 这是一个纯逻辑组件，不渲染DOM
};

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
  /* Cluster边界提示框样式 */
  .cluster-boundary-tooltip {
    background: rgba(0, 0, 0, 0.8) !important;
    border: none !important;
    border-radius: 4px !important;
    color: white !important;
  }

  .parcel-sample-tooltip {
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    color: #333 !important;
  }

  .cluster-label {
    background: none !important;
    border: none !important;
  }

  /* 智能聚合标记样式 */
  .cluster-marker {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    border: 2px solid rgba(255,255,255,0.8);
    transition: all 0.2s ease;
  }

  .cluster-marker:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
  }

  .cluster-count {
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
  }

  .cluster-label {
    font-size: 8px;
    opacity: 0.9;
    margin-top: 1px;
  }

  /* 密度指示颜色 */
  .cluster-high {
    background: linear-gradient(135deg, #ff4757, #ff3742);
  }

  .cluster-medium {
    background: linear-gradient(135deg, #ffa502, #ff9500);
  }

  .cluster-low {
    background: linear-gradient(135deg, #2ed573, #1dd1a1);
  }

  /* 聚合标记大小变体 */
  .marker-cluster-xlarge .cluster-count {
    font-size: 16px;
  }

  .marker-cluster-large .cluster-count {
    font-size: 14px;
  }

  .marker-cluster-medium .cluster-count {
    font-size: 12px;
  }

  .marker-cluster-small .cluster-count {
    font-size: 11px;
  }

  .marker-cluster-small .cluster-label {
    display: none; /* 小聚合不显示标签 */
  }
`;

if (!document.head.querySelector('style[data-cluster-boundary]')) {
  style.setAttribute('data-cluster-boundary', 'true');
  document.head.appendChild(style);
}

export default ClusterBoundaryLayer;
