import React, { useState, useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import '../../styles/USStatesLayer.css';

/**
 * USStatesLayer - 显示美国各州的GeoJSON图层，并在鼠标悬停时高亮显示
 * 使用原生Leaflet API直接创建和管理GeoJSON图层
 *
 * @param {boolean} show - 是否显示该图层
 * @param {string} mapStyle - 地图样式，用于判断是否为夜间模式
 */
const USStatesLayer = ({ show, mapStyle = 'day' }) => {
  const map = useMap();
  const [loading, setLoading] = useState(false);
  const geoJsonLayerRef = useRef(null);
  const [hoveredState, setHoveredState] = useState(null);

  // 加载GeoJSON数据并创建图层
  useEffect(() => {
    if (!show || geoJsonLayerRef.current) return;

    setLoading(true);

    // 定义地图点击事件处理函数，只清理州相关的工具提示，不影响工业地块弹窗
    const handleMapClick = (e) => {
      // 检查点击目标，如果是工业地块标记或弹窗，不执行清理
      const target = e.originalEvent?.target;
      if (target) {
        // 如果点击的是标记、弹窗或按钮，不清理
        if (target.closest('.leaflet-marker-icon') ||
            target.closest('.leaflet-popup') ||
            target.closest('.park-popup-button') ||
            target.classList.contains('park-popup-button')) {
          return;
        }
      }

      // 只清理州相关的工具提示，不关闭工业地块弹窗
      const stateTooltips = document.querySelectorAll('.compact-state-tooltip');
      stateTooltips.forEach(tooltip => {
        const parent = tooltip.closest('.leaflet-tooltip');
        if (parent) {
          parent.remove();
        }
      });

      // 清理州相关的工具提示
      const tooltips = document.querySelectorAll('.leaflet-tooltip');
      tooltips.forEach(tooltip => {
        // 只删除州工具提示，不删除其他工具提示
        if (tooltip.querySelector('.compact-state-tooltip')) {
          tooltip.remove();
        }
      });
    };

    // 创建一个新的地图窗格，确保它在标记窗格下方但仍能接收鼠标事件
    if (!map.getPane('statesPane')) {
      map.createPane('statesPane');
      const pane = map.getPane('statesPane');
      pane.style.zIndex = 200;
      pane.style.pointerEvents = 'all';
      pane.classList.add('states-pane');
    }

    fetch('/data/us_states.geojson')
      .then(res => {
        if (!res.ok) throw new Error(`加载 us_states.geojson 失败: ${res.status}`);
        return res.json();
      })
      .then(data => {
        console.log('US States GeoJSON 加载成功');

        // 创建GeoJSON图层
        const geoJsonLayer = L.geoJSON(data, {
          pane: 'statesPane',
          style: (feature) => ({
            color: '#666',
            weight: 1,
            opacity: 0.5,
            fillColor: 'transparent',
            fillOpacity: 0,
            className: 'us-state-boundary'
          }),
          onEachFeature: (feature, layer) => {
            // 添加鼠标事件处理
            layer.on({
              mouseover: (e) => {
                // 阻止事件冒泡
                L.DomEvent.stopPropagation(e);

                const stateCode = feature.properties.STUSPS;

                // 高亮显示州
                layer.setStyle({
                  weight: 2,
                  color: '#333',
                  opacity: 0.8,
                  fillColor: '#3388ff',
                  fillOpacity: 0.3,
                  className: 'us-state-boundary highlighted'
                });

                setHoveredState(stateCode);
              },
              mouseout: (e) => {
                // 阻止事件冒泡
                L.DomEvent.stopPropagation(e);

                // 重置样式
                layer.setStyle({
                  color: '#666',
                  weight: 1,
                  opacity: 0.5,
                  fillColor: 'transparent',
                  fillOpacity: 0,
                  className: 'us-state-boundary'
                });

                setHoveredState(null);
              },
              click: (e) => {
                // 阻止事件冒泡，防止点击时触发其他事件
                L.DomEvent.stopPropagation(e);

                // 强制关闭所有弹窗和工具提示
                map.closePopup();

                // 清理所有可能残留的弹窗和工具提示DOM元素
                setTimeout(() => {
                  // 清理工具提示
                  const tooltips = document.querySelectorAll('.leaflet-tooltip');
                  tooltips.forEach(tooltip => tooltip.remove());

                  // 清理弹窗
                  const popups = document.querySelectorAll('.leaflet-popup');
                  popups.forEach(popup => popup.remove());

                  // 清理特定的州工具提示
                  const stateTooltips = document.querySelectorAll('.compact-state-tooltip');
                  stateTooltips.forEach(tooltip => {
                    const parent = tooltip.closest('.leaflet-tooltip');
                    if (parent) {
                      parent.remove();
                    }
                  });
                }, 10);
              }
            });
          }
        });

        // 将图层添加到地图
        geoJsonLayer.addTo(map);
        geoJsonLayerRef.current = geoJsonLayer;

        // 添加地图点击事件监听器
        map.on('click', handleMapClick);

        // 确保所有路径元素都能接收鼠标事件
        setTimeout(() => {
          const pane = map.getPane('statesPane');
          const paths = pane.querySelectorAll('path');
          paths.forEach(path => {
            path.style.pointerEvents = 'auto';
            path.classList.add('us-state-path');
          });
        }, 100);

        setLoading(false);
      })
      .catch(err => {
        console.error('加载 US States GeoJSON 出错:', err);
        setLoading(false);
      });

    // 清理函数
    return () => {
      if (geoJsonLayerRef.current) {
        map.removeLayer(geoJsonLayerRef.current);
        geoJsonLayerRef.current = null;
      }

      // 移除地图点击事件监听器
      map.off('click', handleMapClick);

      // 关闭所有弹窗
      map.closePopup();

      // 清理所有可能残留的弹窗和工具提示
      const tooltips = document.querySelectorAll('.leaflet-tooltip');
      tooltips.forEach(tooltip => tooltip.remove());

      const popups = document.querySelectorAll('.leaflet-popup');
      popups.forEach(popup => popup.remove());

      const stateTooltips = document.querySelectorAll('.compact-state-tooltip');
      stateTooltips.forEach(tooltip => {
        const parent = tooltip.closest('.leaflet-tooltip');
        if (parent) {
          parent.remove();
        }
      });
    };
  }, [map, show, mapStyle]);

  // 当mapStyle改变时更新工具提示样式
  useEffect(() => {
    if (!geoJsonLayerRef.current) return;

    // 更新所有工具提示的类名
    const tooltips = document.querySelectorAll('.compact-state-tooltip');
    tooltips.forEach(tooltip => {
      if (mapStyle === 'night') {
        tooltip.classList.add('dark-mode');
      } else {
        tooltip.classList.remove('dark-mode');
      }
    });
  }, [mapStyle]);

  // 组件不渲染任何内容，因为我们直接使用Leaflet API
  return null;
};

export default USStatesLayer;
