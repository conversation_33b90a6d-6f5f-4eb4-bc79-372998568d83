import { useState, useEffect, useCallback, useMemo } from 'react';
import { getCachedResource, setCachedResource } from '../../hooks/useResourcePreloader';

// 优化的地理数据加载器
export const useOptimizedGeoLoader = () => {
  const [geoData, setGeoData] = useState({
    landData: null,
    countriesData: null,
    loading: true,
    error: null,
    progress: 0
  });

  // 分块加载地理数据
  const loadGeoDataOptimized = useCallback(async () => {
    try {
      setGeoData(prev => ({ ...prev, loading: true, progress: 0 }));

      // 检查缓存
      const cachedLandData = getCachedResource('/welcomeEarthLandData/ne_50m_land.json');
      const cachedCountriesData = getCachedResource('/welcomeEarthLandData/ne_50m_admin_0_countries.json');

      let landData = cachedLandData;
      let countriesData = cachedCountriesData;

      // 如果没有缓存，则加载数据
      if (!landData || !countriesData) {
        const loadPromises = [];
        
        if (!landData) {
          loadPromises.push(
            fetch('/welcomeEarthLandData/ne_50m_land.json')
              .then(response => {
                if (!response.ok) throw new Error('Failed to load land data');
                return response.json();
              })
              .then(data => {
                setCachedResource('/welcomeEarthLandData/ne_50m_land.json', data);
                return { type: 'land', data };
              })
          );
        }

        if (!countriesData) {
          loadPromises.push(
            fetch('/welcomeEarthLandData/ne_50m_admin_0_countries.json')
              .then(response => {
                if (!response.ok) throw new Error('Failed to load countries data');
                return response.json();
              })
              .then(data => {
                setCachedResource('/welcomeEarthLandData/ne_50m_admin_0_countries.json', data);
                return { type: 'countries', data };
              })
          );
        }

        // 并行加载并更新进度
        const results = await Promise.allSettled(loadPromises);
        
        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            const { type, data } = result.value;
            if (type === 'land') {
              landData = data;
            } else if (type === 'countries') {
              countriesData = data;
            }
          } else {
            console.warn(`Failed to load ${index === 0 ? 'land' : 'countries'} data:`, result.reason);
          }
          
          // 更新进度
          const progress = ((index + 1) / loadPromises.length) * 100;
          setGeoData(prev => ({ ...prev, progress }));
        });
      }

      // 设置最终数据
      setGeoData({
        landData,
        countriesData,
        loading: false,
        error: null,
        progress: 100
      });

    } catch (error) {
      console.error('Error loading geo data:', error);
      setGeoData(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
    }
  }, []);

  useEffect(() => {
    loadGeoDataOptimized();
  }, [loadGeoDataOptimized]);

  return geoData;
};

// 地理数据处理工具
export const geoDataUtils = {
  // 简化地理数据以提高性能
  simplifyGeoData: (geoData, tolerance = 0.01) => {
    if (!geoData || !geoData.features) return geoData;
    
    return {
      ...geoData,
      features: geoData.features.map(feature => ({
        ...feature,
        geometry: simplifyGeometry(feature.geometry, tolerance)
      }))
    };
  },

  // 按区域分块地理数据
  chunkGeoDataByRegion: (geoData) => {
    if (!geoData || !geoData.features) return {};
    
    const regions = {
      northAmerica: [],
      southAmerica: [],
      europe: [],
      africa: [],
      asia: [],
      oceania: []
    };

    geoData.features.forEach(feature => {
      const bounds = getFeatureBounds(feature);
      const region = determineRegion(bounds);
      if (regions[region]) {
        regions[region].push(feature);
      }
    });

    return regions;
  },

  // 获取可见区域的特征
  getVisibleFeatures: (geoData, viewport) => {
    if (!geoData || !geoData.features) return [];
    
    return geoData.features.filter(feature => {
      const bounds = getFeatureBounds(feature);
      return isInViewport(bounds, viewport);
    });
  }
};

// 简化几何图形
const simplifyGeometry = (geometry, tolerance) => {
  if (!geometry || !geometry.coordinates) return geometry;
  
  // 这里可以实现更复杂的简化算法
  // 目前返回原始几何图形
  return geometry;
};

// 获取特征边界
const getFeatureBounds = (feature) => {
  if (!feature || !feature.geometry || !feature.geometry.coordinates) {
    return { minLng: 0, maxLng: 0, minLat: 0, maxLat: 0 };
  }

  let minLng = Infinity, maxLng = -Infinity;
  let minLat = Infinity, maxLat = -Infinity;

  const processCoordinates = (coords) => {
    if (Array.isArray(coords[0])) {
      coords.forEach(processCoordinates);
    } else {
      const [lng, lat] = coords;
      minLng = Math.min(minLng, lng);
      maxLng = Math.max(maxLng, lng);
      minLat = Math.min(minLat, lat);
      maxLat = Math.max(maxLat, lat);
    }
  };

  if (feature.geometry.type === 'Polygon') {
    feature.geometry.coordinates.forEach(ring => processCoordinates(ring));
  } else if (feature.geometry.type === 'MultiPolygon') {
    feature.geometry.coordinates.forEach(polygon => 
      polygon.forEach(ring => processCoordinates(ring))
    );
  }

  return { minLng, maxLng, minLat, maxLat };
};

// 确定区域
const determineRegion = (bounds) => {
  const { minLng, maxLng, minLat, maxLat } = bounds;
  const centerLng = (minLng + maxLng) / 2;
  const centerLat = (minLat + maxLat) / 2;

  // 简单的区域划分逻辑
  if (centerLng >= -180 && centerLng <= -30) {
    return centerLat >= 0 ? 'northAmerica' : 'southAmerica';
  } else if (centerLng >= -30 && centerLng <= 60) {
    return centerLat >= 35 ? 'europe' : 'africa';
  } else if (centerLng >= 60 && centerLng <= 180) {
    return centerLat >= -10 ? 'asia' : 'oceania';
  }
  
  return 'asia'; // 默认
};

// 检查是否在视口内
const isInViewport = (bounds, viewport) => {
  if (!viewport) return true;
  
  const { minLng, maxLng, minLat, maxLat } = bounds;
  const { left, right, top, bottom } = viewport;
  
  return !(maxLng < left || minLng > right || maxLat < bottom || minLat > top);
};

export default useOptimizedGeoLoader;
