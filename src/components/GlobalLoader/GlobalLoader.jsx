import React, { useState, useEffect } from 'react';
import './GlobalLoader.css';

const GlobalLoader = ({ isLoading, progress = 0, stage = 'Initializing' }) => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => prev.length >= 3 ? '' : prev + '.');
    }, 500);
    return () => clearInterval(interval);
  }, []);

  if (!isLoading) return null;

  return (
    <div className="global-loader">
      {/* 立即显示的背景 */}
      <div className="loader-background" />
      
      {/* 加载内容 */}
      <div className="loader-content">
        {/* 科技感logo区域 */}
        <div className="loader-logo">
          <div className="logo-circle">
            <div className="logo-inner">
              <div className="logo-pulse" />
            </div>
          </div>
          <h1 className="logo-text">INDUSTRIAL DISCOVERY</h1>
        </div>

        {/* 加载进度 */}
        <div className="loader-progress">
          <div className="progress-container">
            <div 
              className="progress-bar" 
              style={{ width: `${progress}%` }}
            />
            <div className="progress-glow" style={{ left: `${progress}%` }} />
          </div>
          <div className="progress-info">
            <span className="progress-stage">{stage}{dots}</span>
            <span className="progress-percent">{Math.round(progress)}%</span>
          </div>
        </div>

        {/* 装饰元素 */}
        <div className="loader-decorations">
          <div className="decoration-line line-1" />
          <div className="decoration-line line-2" />
          <div className="decoration-dot dot-1" />
          <div className="decoration-dot dot-2" />
          <div className="decoration-dot dot-3" />
        </div>

        {/* 技术提示 */}
        <div className="loader-tips">
          <p>Loading geographic intelligence systems...</p>
          <p>Initializing 3D visualization engine...</p>
          <p>Preparing industrial data networks...</p>
        </div>
      </div>

      {/* 粒子效果 */}
      <div className="loader-particles">
        {[...Array(20)].map((_, i) => (
          <div 
            key={i} 
            className="particle" 
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default GlobalLoader;
