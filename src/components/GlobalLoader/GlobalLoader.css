/* 全局加载屏幕 - 立即显示，防止白屏 */
.global-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  /* 立即设置背景，防止白屏 */
  background-color: #010313;
}

/* 背景层 - 立即显示 */
.loader-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #010313 0%, #011528 50%, #010313 100%);
  z-index: -1;
}

/* 主要内容区域 */
.loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  z-index: 10;
  animation: contentFadeIn 0.8s ease-out;
}

/* Logo区域 */
.loader-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.logo-circle {
  position: relative;
  width: 80px;
  height: 80px;
  border: 2px solid rgba(77, 200, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: logoRotate 3s linear infinite;
}

.logo-inner {
  width: 50px;
  height: 50px;
  border: 1px solid rgba(77, 200, 255, 0.6);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-pulse {
  width: 20px;
  height: 20px;
  background: #4dc8ff;
  border-radius: 50%;
  animation: logoPulse 2s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(77, 200, 255, 0.8);
}

.logo-text {
  font-family: 'Orbitron', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 3px;
  text-align: center;
  margin: 0;
  text-shadow: 0 0 15px rgba(77, 200, 255, 0.5);
}

/* 进度条区域 */
.loader-progress {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.progress-container {
  position: relative;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4dc8ff, #00e5ff, #4dc8ff);
  border-radius: 2px;
  transition: width 0.3s ease;
  box-shadow: 0 0 15px rgba(77, 200, 255, 0.6);
  animation: progressGlow 2s ease-in-out infinite;
}

.progress-glow {
  position: absolute;
  top: -2px;
  width: 8px;
  height: 8px;
  background: #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
  transform: translateX(-50%);
  transition: left 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-stage {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.5px;
}

.progress-percent {
  color: #4dc8ff;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Orbitron', sans-serif;
}

/* 装饰元素 */
.loader-decorations {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.3), transparent);
  animation: lineMove 4s ease-in-out infinite;
}

.line-1 {
  width: 200px;
  height: 1px;
  top: 30%;
  left: 10%;
  animation-delay: 0s;
}

.line-2 {
  width: 150px;
  height: 1px;
  bottom: 30%;
  right: 15%;
  animation-delay: 2s;
}

.decoration-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #4dc8ff;
  border-radius: 50%;
  animation: dotPulse 3s ease-in-out infinite;
  box-shadow: 0 0 8px rgba(77, 200, 255, 0.6);
}

.dot-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.dot-2 {
  top: 60%;
  right: 25%;
  animation-delay: 1s;
}

.dot-3 {
  bottom: 25%;
  left: 30%;
  animation-delay: 2s;
}

/* 技术提示 */
.loader-tips {
  text-align: center;
  opacity: 0.6;
}

.loader-tips p {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  font-family: 'Inter', sans-serif;
  margin: 5px 0;
  letter-spacing: 0.5px;
  animation: tipsFade 4s ease-in-out infinite;
}

.loader-tips p:nth-child(2) {
  animation-delay: 1.5s;
}

.loader-tips p:nth-child(3) {
  animation-delay: 3s;
}

/* 粒子效果 */
.loader-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(77, 200, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat linear infinite;
}

/* 动画定义 */
@keyframes contentFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logoRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes logoPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes progressGlow {
  0%, 100% {
    box-shadow: 0 0 15px rgba(77, 200, 255, 0.6);
  }
  50% {
    box-shadow: 0 0 25px rgba(77, 200, 255, 0.9);
  }
}

@keyframes lineMove {
  0%, 100% {
    opacity: 0;
    transform: translateX(-50px);
  }
  50% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes dotPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

@keyframes tipsFade {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-10px) translateX(10px);
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logo-text {
    font-size: 18px;
    letter-spacing: 2px;
  }
  
  .loader-progress {
    width: 250px;
  }
  
  .logo-circle {
    width: 60px;
    height: 60px;
  }
  
  .logo-inner {
    width: 40px;
    height: 40px;
  }
  
  .logo-pulse {
    width: 15px;
    height: 15px;
  }
}
