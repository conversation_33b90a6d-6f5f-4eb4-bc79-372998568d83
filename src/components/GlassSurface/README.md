# GlassSurface Component

一个现代化的玻璃表面效果组件，实现了设计图中的 Glass Surface 效果。

## 特性

- ✨ 高级玻璃表面视觉效果
- 🎨 可自定义的失真和色彩偏移
- 📱 响应式设计支持
- 🔧 丰富的配置选项
- 🚀 高性能 SVG 滤镜实现

## 基本用法

```jsx
import GlassSurface from './GlassSurface'

// 基本使用
<GlassSurface 
  width={300} 
  height={200}
  borderRadius={24}
  className="my-custom-class"
>
  <h2>Glass Surface Content</h2>
</GlassSurface>
```

## 高级用法

```jsx
// 自定义失真效果
<GlassSurface
  displace={15}
  distortionScale={-150}
  redOffset={5}
  greenOffset={15}
  blueOffset={25}
  brightness={60}
  opacity={0.8}
  mixBlendMode="screen"
>
  <span>Advanced Glass Distortion</span>
</GlassSurface>
```

## 导航栏示例

```jsx
// 用于导航栏的配置
<GlassSurface
  width="fit-content"
  height={60}
  borderRadius={30}
  displace={12}
  distortionScale={-180}
  redOffset={3}
  greenOffset={12}
  blueOffset={20}
  brightness={50}
  opacity={0.85}
  backgroundOpacity={0.1}
  saturation={1.2}
  mixBlendMode="difference"
  className="glass-navigation"
>
  {/* 导航内容 */}
</GlassSurface>
```

## Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `children` | `React.ReactNode` | - | 内容 |
| `width` | `number \| string` | `200` | 宽度 |
| `height` | `number \| string` | `80` | 高度 |
| `borderRadius` | `number` | `20` | 边框圆角 |
| `borderWidth` | `number` | `0.07` | 边框宽度因子 |
| `brightness` | `number` | `50` | 亮度百分比 |
| `opacity` | `number` | `0.93` | 不透明度 |
| `blur` | `number` | `11` | 模糊程度 |
| `displace` | `number` | `0` | 输出模糊 |
| `backgroundOpacity` | `number` | `0` | 背景霜化不透明度 |
| `saturation` | `number` | `1` | 饱和度因子 |
| `distortionScale` | `number` | `-180` | 主失真比例 |
| `redOffset` | `number` | `0` | 红色通道偏移 |
| `greenOffset` | `number` | `10` | 绿色通道偏移 |
| `blueOffset` | `number` | `20` | 蓝色通道偏移 |
| `xChannel` | `'R' \| 'G' \| 'B'` | `'R'` | X 位移通道选择器 |
| `yChannel` | `'R' \| 'G' \| 'B'` | `'G'` | Y 位移通道选择器 |
| `mixBlendMode` | `BlendMode` | `'difference'` | 混合模式 |
| `className` | `string` | `''` | 额外的 CSS 类名 |
| `style` | `React.CSSProperties` | `{}` | 内联样式 |

## 浏览器兼容性

- ✅ Chrome/Edge (推荐)
- ✅ Firefox (降级模式)
- ✅ Safari (降级模式)

组件会自动检测浏览器支持并提供适当的降级效果。

## 性能优化

- 使用 ResizeObserver 进行高效的尺寸监听
- SVG 滤镜缓存优化
- 自动降级到 CSS backdrop-filter

## 样式定制

组件提供了完整的 CSS 变量支持：

```css
.glass-surface {
  --glass-frost: 0.1;
  --glass-saturation: 1.2;
  --filter-id: url(#custom-filter);
}
```

## 注意事项

1. 在某些浏览器中，复杂的 SVG 滤镜可能影响性能
2. 建议在生产环境中测试不同设备的兼容性
3. 可以通过 `className` 和 `style` 属性进行进一步定制
