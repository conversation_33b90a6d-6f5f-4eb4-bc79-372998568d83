import { useEffect, useRef, useId } from "react";
import "./GlassSurface.css";

const GlassSurface = ({
  children,
  width = 200,
  height = 80,
  borderRadius = 50,
  borderWidth = 0.07,
  brightness = 50,
  opacity = 0.93,
  blur = 11,
  displace = 8,
  backgroundOpacity = 0.1,
  saturation = 2.5,
  distortionScale = -180,
  redOffset = 0,
  greenOffset = 10,
  blueOffset = 20,
  xChannel = "R",
  yChannel = "G",
  mixBlendMode = "screen",
  className = "",
  style = {},
}) => {
  const uniqueId = useId().replace(/:/g, '-');
  const filterId = `glass-filter-${uniqueId}`;
  const redGradId = `red-grad-${uniqueId}`;
  const blueGradId = `blue-grad-${uniqueId}`;
  
  const containerRef = useRef(null);
  const feImageRef = useRef(null);
  const redChannelRef = useRef(null);
  const greenChannelRef = useRef(null);
  const blueChannelRef = useRef(null);
  const gaussianBlurRef = useRef(null);

  const generateDisplacementMap = () => {
    const rect = containerRef.current?.getBoundingClientRect();
    const actualWidth = rect?.width || 400;
    const actualHeight = rect?.height || 200;
    const edgeSize = Math.min(actualWidth, actualHeight) * (borderWidth * 0.5);

    const svgContent = `
      <svg viewBox="0 0 ${actualWidth} ${actualHeight}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <!-- 增强的红色通道渐变 - 更复杂的折射模式 -->
          <radialGradient id="${redGradId}" cx="30%" cy="30%" r="70%">
            <stop offset="0%" stop-color="#0000"/>
            <stop offset="40%" stop-color="rgba(255,0,0,0.3)"/>
            <stop offset="70%" stop-color="rgba(255,0,0,0.7)"/>
            <stop offset="100%" stop-color="red"/>
          </radialGradient>

          <!-- 增强的蓝色通道渐变 - 创造更真实的玻璃扭曲 -->
          <linearGradient id="${blueGradId}" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#0000"/>
            <stop offset="30%" stop-color="rgba(0,0,255,0.2)"/>
            <stop offset="60%" stop-color="rgba(0,0,255,0.5)"/>
            <stop offset="100%" stop-color="blue"/>
          </linearGradient>

          <!-- 新增：绿色通道渐变 - 增强色散效果 -->
          <linearGradient id="green-grad-${uniqueId}" x1="100%" y1="100%" x2="0%" y2="0%">
            <stop offset="0%" stop-color="#0000"/>
            <stop offset="50%" stop-color="rgba(0,255,0,0.4)"/>
            <stop offset="100%" stop-color="green"/>
          </linearGradient>

          <!-- 噪声纹理 - 增加玻璃表面的微妙变化 -->
          <filter id="noise-${uniqueId}">
            <feTurbulence baseFrequency="0.9" numOctaves="3" result="noise"/>
            <feColorMatrix in="noise" type="saturate" values="0"/>
            <feComponentTransfer>
              <feFuncA type="discrete" tableValues="0.1 0.2 0.3 0.4 0.5"/>
            </feComponentTransfer>
          </filter>
        </defs>

        <!-- 基础黑色背景 -->
        <rect x="0" y="0" width="${actualWidth}" height="${actualHeight}" fill="black"/>

        <!-- 主要折射层 - 红色通道 -->
        <rect x="0" y="0" width="${actualWidth}" height="${actualHeight}" rx="${borderRadius}"
              fill="url(#${redGradId})" opacity="0.8"/>

        <!-- 次要折射层 - 蓝色通道 -->
        <rect x="0" y="0" width="${actualWidth}" height="${actualHeight}" rx="${borderRadius}"
              fill="url(#${blueGradId})" style="mix-blend-mode: ${mixBlendMode}" opacity="0.7"/>

        <!-- 绿色通道层 - 增强色散 -->
        <rect x="0" y="0" width="${actualWidth}" height="${actualHeight}" rx="${borderRadius}"
              fill="url(#green-grad-${uniqueId})" style="mix-blend-mode: overlay" opacity="0.6"/>

        <!-- 中心高光区域 - 模拟玻璃厚度 -->
        <rect x="${edgeSize}" y="${edgeSize}" width="${actualWidth - edgeSize * 2}" height="${actualHeight - edgeSize * 2}"
              rx="${borderRadius}" fill="hsl(0 0% ${brightness}% / ${opacity})" style="filter:blur(${blur}px)"/>

        <!-- 边缘增强层 - 创造更强的立体感 -->
        <rect x="0" y="0" width="${actualWidth}" height="${actualHeight}" rx="${borderRadius}"
              fill="none" stroke="url(#${redGradId})" stroke-width="2" opacity="0.4"/>
        <rect x="1" y="1" width="${actualWidth - 2}" height="${actualHeight - 2}" rx="${borderRadius - 1}"
              fill="none" stroke="url(#${blueGradId})" stroke-width="1.5" opacity="0.3" style="mix-blend-mode: ${mixBlendMode}"/>

        <!-- 内部高光 - 模拟光线反射 -->
        <ellipse cx="${actualWidth * 0.3}" cy="${actualHeight * 0.25}" rx="${actualWidth * 0.15}" ry="${actualHeight * 0.1}"
                 fill="rgba(255,255,255,0.2)" style="filter:blur(${blur * 0.5}px)"/>

        <!-- 噪声纹理层 - 增加真实感 -->
        <rect x="0" y="0" width="${actualWidth}" height="${actualHeight}" rx="${borderRadius}"
              fill="white" filter="url(#noise-${uniqueId})" opacity="0.05" style="mix-blend-mode: overlay"/>
      </svg>
    `;

    return `data:image/svg+xml,${encodeURIComponent(svgContent)}`;
  };

  const updateDisplacementMap = () => {
    feImageRef.current?.setAttribute("href", generateDisplacementMap());
  };

  useEffect(() => {
    updateDisplacementMap();
    [
      { ref: redChannelRef, offset: redOffset },
      { ref: greenChannelRef, offset: greenOffset },
      { ref: blueChannelRef, offset: blueOffset },
    ].forEach(({ ref, offset }) => {
      if (ref.current) {
        ref.current.setAttribute(
          "scale",
          (distortionScale + offset).toString()
        );
        ref.current.setAttribute("xChannelSelector", xChannel);
        ref.current.setAttribute("yChannelSelector", yChannel);
      }
    });

    gaussianBlurRef.current?.setAttribute("stdDeviation", displace.toString());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    width,
    height,
    borderRadius,
    borderWidth,
    brightness,
    opacity,
    blur,
    displace,
    distortionScale,
    redOffset,
    greenOffset,
    blueOffset,
    xChannel,
    yChannel,
    mixBlendMode,
  ]);

  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver(() => {
      setTimeout(updateDisplacementMap, 0);
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver(() => {
      setTimeout(updateDisplacementMap, 0);
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setTimeout(updateDisplacementMap, 0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [width, height]);

  const supportsSVGFilters = () => {
    const isWebkit =
      /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
    const isFirefox = /Firefox/.test(navigator.userAgent);

    if (isWebkit || isFirefox) {
      return false;
    }

    const div = document.createElement("div");
    div.style.backdropFilter = `url(#${filterId})`;
    return div.style.backdropFilter !== "";
  };

  const containerStyle = {
    ...style,
    width: typeof width === "number" ? `${width}px` : width,
    height: typeof height === "number" ? `${height}px` : height,
    borderRadius: `${borderRadius}px`,
    "--glass-frost": backgroundOpacity,
    "--glass-saturation": saturation,
    "--filter-id": `url(#${filterId})`,
  };

  return (
    <div
      ref={containerRef}
      className={`glass-surface ${supportsSVGFilters() ? "glass-surface--svg" : "glass-surface--fallback"} ${className}`}
      style={containerStyle}
    >
      <svg className="glass-surface__filter" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <filter
            id={filterId}
            colorInterpolationFilters="sRGB"
            x="0%"
            y="0%"
            width="100%"
            height="100%"
          >
            <feImage
              ref={feImageRef}
              x="0"
              y="0"
              width="100%"
              height="100%"
              preserveAspectRatio="none"
              result="map"
            />

            {/* 增强的红色通道位移 */}
            <feDisplacementMap
              ref={redChannelRef}
              in="SourceGraphic"
              in2="map"
              id="redchannel"
              result="dispRed"
            />
            <feColorMatrix
              in="dispRed"
              type="matrix"
              values="1.2 0 0 0 0
                      0 0 0 0 0
                      0 0 0 0 0
                      0 0 0 1 0"
              result="red"
            />
            {/* 红色通道额外增强 */}
            <feGaussianBlur in="red" stdDeviation="0.3" result="redBlur"/>

            {/* 增强的绿色通道位移 */}
            <feDisplacementMap
              ref={greenChannelRef}
              in="SourceGraphic"
              in2="map"
              id="greenchannel"
              result="dispGreen"
            />
            <feColorMatrix
              in="dispGreen"
              type="matrix"
              values="0 0 0 0 0
                      0 1.1 0 0 0
                      0 0 0 0 0
                      0 0 0 1 0"
              result="green"
            />
            {/* 绿色通道额外增强 */}
            <feGaussianBlur in="green" stdDeviation="0.2" result="greenBlur"/>

            {/* 增强的蓝色通道位移 */}
            <feDisplacementMap
              ref={blueChannelRef}
              in="SourceGraphic"
              in2="map"
              id="bluechannel"
              result="dispBlue"
            />
            <feColorMatrix
              in="dispBlue"
              type="matrix"
              values="0 0 0 0 0
                      0 0 0 0 0
                      0 0 1.3 0 0
                      0 0 0 1 0"
              result="blue"
            />
            {/* 蓝色通道额外增强 */}
            <feGaussianBlur in="blue" stdDeviation="0.4" result="blueBlur"/>

            {/* 多层混合以创造更复杂的折射效果 */}
            <feBlend in="redBlur" in2="greenBlur" mode="screen" result="rg" />
            <feBlend in="rg" in2="blueBlur" mode="screen" result="rgb" />

            {/* 添加额外的光线散射效果 */}
            <feGaussianBlur in="rgb" stdDeviation="0.8" result="scattered"/>
            <feBlend in="rgb" in2="scattered" mode="overlay" result="enhanced"/>

            {/* 最终模糊处理 */}
            <feGaussianBlur
              ref={gaussianBlurRef}
              in="enhanced"
              stdDeviation="0.5"
              result="final"
            />

            {/* 增加对比度和亮度 */}
            <feComponentTransfer in="final" result="output">
              <feFuncA type="gamma" amplitude="1" exponent="0.8"/>
            </feComponentTransfer>
          </filter>
        </defs>
      </svg>

      <div className="glass-surface__content">{children}</div>
    </div>
  );
};

export default GlassSurface;
