.glass-surface {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: opacity 0.26s ease-out;
}

.glass-surface__filter {
  width: 100%;
  height: 100%;
  pointer-events: none;
  position: absolute;
  inset: 0;
  opacity: 0;
  z-index: -1;
}

.glass-surface__content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: inherit;
  position: relative;
  z-index: 1;
}

.glass-surface--svg {
  background: light-dark(
    linear-gradient(135deg,
      hsla(0, 0%, 100%, var(--glass-frost, 0.1)) 0%,
      hsla(0, 0%, 100%, calc(var(--glass-frost, 0.1) * 0.8)) 50%,
      hsla(0, 0%, 100%, calc(var(--glass-frost, 0.1) * 0.6)) 100%
    ),
    linear-gradient(135deg,
      hsla(0, 0%, 0%, var(--glass-frost, 0.05)) 0%,
      hsla(0, 0%, 0%, calc(var(--glass-frost, 0.05) * 0.8)) 50%,
      hsla(0, 0%, 0%, calc(var(--glass-frost, 0.05) * 0.6)) 100%
    )
  );
  backdrop-filter:
    var(--filter-id, url(#glass-filter))
    saturate(var(--glass-saturation, 2.5))
    blur(11px)
    brightness(1.15)
    contrast(1.2)
    hue-rotate(2deg);
  -webkit-backdrop-filter:
    var(--filter-id, url(#glass-filter))
    saturate(var(--glass-saturation, 2.5))
    blur(11px)
    brightness(1.15)
    contrast(1.2)
    hue-rotate(2deg);
  border: 0.07px solid rgba(255, 255, 255, 0.4);
  box-shadow:
    /* 内部高光 - 模拟玻璃厚度 */
    0 0 0 0.5px rgba(255, 255, 255, 0.6) inset,
    0 0 2px 1px rgba(255, 255, 255, 0.3) inset,
    0 0 6px 2px rgba(255, 255, 255, 0.15) inset,
    0 0 12px 3px rgba(255, 255, 255, 0.08) inset,
    /* 外部阴影 - 增强立体感 */
    0px 1px 3px rgba(0, 0, 0, 0.12),
    0px 3px 8px rgba(0, 0, 0, 0.1),
    0px 6px 16px rgba(0, 0, 0, 0.08),
    0px 12px 32px rgba(0, 0, 0, 0.06),
    /* 彩色折射边缘 - 增强色散效果 */
    0 0 0 1px rgba(74, 158, 255, 0.15),
    0 0 0 2px rgba(255, 74, 158, 0.08),
    0 0 0 3px rgba(158, 255, 74, 0.05),
    /* 额外的光谱效果 */
    0 0 1px 1px rgba(255, 255, 255, 0.2),
    0 0 3px 2px rgba(74, 158, 255, 0.05),
    0 0 6px 3px rgba(255, 74, 158, 0.03);
}

.glass-surface--fallback {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.06) 100%
  );
  backdrop-filter:
    blur(11px)
    saturate(2.5)
    brightness(1.15)
    contrast(1.2)
    hue-rotate(2deg);
  -webkit-backdrop-filter:
    blur(11px)
    saturate(2.5)
    brightness(1.15)
    contrast(1.2)
    hue-rotate(2deg);
  border: 0.07px solid rgba(255, 255, 255, 0.4);
  box-shadow:
    /* 内部高光 */
    0 0 0 0.5px rgba(255, 255, 255, 0.5) inset,
    0 0 2px 1px rgba(255, 255, 255, 0.25) inset,
    0 0 6px 2px rgba(255, 255, 255, 0.12) inset,
    0 0 12px 3px rgba(255, 255, 255, 0.06) inset,
    /* 外部阴影 */
    0px 1px 3px rgba(0, 0, 0, 0.12),
    0px 3px 8px rgba(0, 0, 0, 0.1),
    0px 6px 16px rgba(0, 0, 0, 0.08),
    0px 12px 32px rgba(0, 0, 0, 0.06),
    /* 彩色折射边缘效果 */
    0 0 0 1px rgba(74, 158, 255, 0.12),
    0 0 0 2px rgba(255, 74, 158, 0.06),
    0 0 0 3px rgba(158, 255, 74, 0.04),
    /* 额外光谱效果 */
    0 0 1px 1px rgba(255, 255, 255, 0.15),
    0 0 3px 2px rgba(74, 158, 255, 0.04);
}

@media (prefers-color-scheme: dark) {
  .glass-surface--fallback {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(11px) saturate(2.5) brightness(1.2) contrast(1.1);
    -webkit-backdrop-filter: blur(11px) saturate(2.5) brightness(1.2) contrast(1.1);
    border: 0.07px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      /* 暗色主题下的玻璃边缘效果 */
      0 0 1px 0.5px rgba(255, 255, 255, 0.3) inset,
      0 0 3px 1px rgba(255, 255, 255, 0.15) inset,
      0 0 8px 2px rgba(255, 255, 255, 0.08) inset,
      /* 外部阴影 */
      0px 2px 8px rgba(0, 0, 0, 0.2),
      0px 4px 16px rgba(0, 0, 0, 0.15),
      0px 8px 32px rgba(0, 0, 0, 0.1),
      /* 彩色折射边缘效果 */
      0 0 0 1px rgba(74, 158, 255, 0.15),
      0 0 0 2px rgba(255, 74, 158, 0.08),
      0 0 0 3px rgba(158, 255, 74, 0.05);
  }
}

@supports not (backdrop-filter: blur(10px)) {
  .glass-surface--fallback {
    background: rgba(255, 255, 255, 0.4);
    box-shadow:
      inset 0 1px 0 0 rgba(255, 255, 255, 0.5),
      inset 0 -1px 0 0 rgba(255, 255, 255, 0.3);
  }

  .glass-surface--fallback::before {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.15);
    border-radius: inherit;
    z-index: -1;
  }
}

@supports not (backdrop-filter: blur(10px)) {
  @media (prefers-color-scheme: dark) {
    .glass-surface--fallback {
      background: rgba(0, 0, 0, 0.4);
    }

    .glass-surface--fallback::before {
      background: rgba(255, 255, 255, 0.05);
    }
  }
}

.glass-surface:focus-visible {
  outline: 2px solid light-dark(#007AFF, #0A84FF);
  outline-offset: 2px;
}

/* 增强玻璃导航栏的特殊效果 */
.glass-navigation {
  position: relative;
  overflow: visible;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation:
    chromaticAberration 12s ease-in-out infinite,
    subtlePulse 8s ease-in-out infinite;
}

/* 导航栏悬停效果 */
.glass-navigation:hover {
  transform: translateY(-1px);
  box-shadow:
    0 0 0 0.5px rgba(255, 255, 255, 0.7) inset,
    0 0 2px 1px rgba(255, 255, 255, 0.4) inset,
    0 0 6px 2px rgba(255, 255, 255, 0.2) inset,
    0px 2px 6px rgba(0, 0, 0, 0.15),
    0px 6px 20px rgba(0, 0, 0, 0.12),
    0px 12px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(74, 158, 255, 0.2),
    0 0 0 2px rgba(255, 74, 158, 0.1),
    0 0 0 3px rgba(158, 255, 74, 0.08);
}

.glass-navigation::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: inherit;
  background: linear-gradient(135deg,
    rgba(74, 158, 255, 0.15) 0%,
    rgba(255, 74, 158, 0.08) 25%,
    rgba(158, 255, 74, 0.06) 50%,
    rgba(255, 255, 255, 0.04) 75%,
    rgba(74, 158, 255, 0.12) 100%);
  background-size: 400% 400%;
  animation: glassRefraction 8s ease-in-out infinite;
  z-index: -1;
  opacity: 0.8;
  filter: blur(1px);
}

.glass-navigation::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background:
    radial-gradient(ellipse at 25% 25%,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.08) 30%,
      transparent 70%),
    radial-gradient(ellipse at 75% 75%,
      rgba(74, 158, 255, 0.1) 0%,
      rgba(74, 158, 255, 0.05) 40%,
      transparent 80%);
  animation: lightScatter 12s linear infinite;
  pointer-events: none;
  z-index: 1;
  opacity: 0.7;
}

/* 增强下拉菜单的玻璃效果 */
.dropdown-menu {
  position: relative;
  overflow: visible;
  animation: dropdownFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top center;
}

/* 下拉菜单出现动画 */
@keyframes dropdownFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-menu::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: inherit;
  background: linear-gradient(135deg,
    rgba(74, 158, 255, 0.12) 0%,
    rgba(255, 74, 158, 0.06) 30%,
    rgba(158, 255, 74, 0.04) 60%,
    rgba(255, 255, 255, 0.03) 100%);
  background-size: 200% 200%;
  animation: glassRefraction 6s ease-in-out infinite;
  z-index: -1;
  opacity: 0.9;
  filter: blur(0.5px);
}

/* 下拉菜单内部高光效果 */
.dropdown-menu::after {
  content: '';
  position: absolute;
  top: 5%;
  left: 5%;
  right: 5%;
  bottom: 5%;
  border-radius: inherit;
  background: radial-gradient(ellipse at 30% 20%,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0.06) 40%,
    transparent 80%);
  pointer-events: none;
  z-index: 0;
  opacity: 0.8;
}

/* 增强的玻璃折射动画效果 */
@keyframes glassRefraction {
  0% {
    background-position: 0% 50%;
    transform: translateZ(0) scale(1);
  }
  25% {
    background-position: 50% 25%;
    transform: translateZ(0) scale(1.002);
  }
  50% {
    background-position: 100% 50%;
    transform: translateZ(0) scale(1);
  }
  75% {
    background-position: 50% 75%;
    transform: translateZ(0) scale(0.998);
  }
  100% {
    background-position: 0% 50%;
    transform: translateZ(0) scale(1);
  }
}

/* 光线散射动画 */
@keyframes lightScatter {
  0% {
    opacity: 0.3;
    transform: rotate(0deg) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: rotate(180deg) scale(1.05);
  }
  100% {
    opacity: 0.3;
    transform: rotate(360deg) scale(1);
  }
}

/* 色散效果动画 */
@keyframes chromaticAberration {
  0% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
  }
  25% {
    filter: hue-rotate(1deg) saturate(1.05) brightness(1.02);
  }
  50% {
    filter: hue-rotate(2deg) saturate(1.1) brightness(1.05);
  }
  75% {
    filter: hue-rotate(-1deg) saturate(0.95) brightness(1.02);
  }
  100% {
    filter: hue-rotate(0deg) saturate(1) brightness(1);
  }
}

/* 微妙的脉冲效果 */
@keyframes subtlePulse {
  0% {
    box-shadow:
      0 0 0 0.5px rgba(255, 255, 255, 0.6) inset,
      0 0 2px 1px rgba(255, 255, 255, 0.3) inset,
      0 0 0 1px rgba(74, 158, 255, 0.15),
      0 0 0 2px rgba(255, 74, 158, 0.08);
  }
  50% {
    box-shadow:
      0 0 0 0.5px rgba(255, 255, 255, 0.8) inset,
      0 0 3px 1.5px rgba(255, 255, 255, 0.4) inset,
      0 0 0 1px rgba(74, 158, 255, 0.25),
      0 0 0 2px rgba(255, 74, 158, 0.15);
  }
  100% {
    box-shadow:
      0 0 0 0.5px rgba(255, 255, 255, 0.6) inset,
      0 0 2px 1px rgba(255, 255, 255, 0.3) inset,
      0 0 0 1px rgba(74, 158, 255, 0.15),
      0 0 0 2px rgba(255, 74, 158, 0.08);
  }
}

/* 为玻璃表面添加多层动态效果 */
.glass-surface {
  position: relative;
  animation: chromaticAberration 12s ease-in-out infinite;
}

.glass-surface::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background: linear-gradient(45deg,
    transparent 0%,
    rgba(74, 158, 255, 0.03) 20%,
    rgba(255, 74, 158, 0.02) 40%,
    rgba(158, 255, 74, 0.025) 60%,
    rgba(255, 255, 255, 0.015) 80%,
    transparent 100%);
  background-size: 300% 300%;
  animation: glassRefraction 10s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
  opacity: 0.8;
}

/* 额外的光线效果层 */
.glass-surface::after {
  content: '';
  position: absolute;
  top: 10%;
  left: 10%;
  right: 10%;
  bottom: 10%;
  border-radius: inherit;
  background: radial-gradient(ellipse at 30% 30%,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(74, 158, 255, 0.05) 30%,
    transparent 70%);
  animation: lightScatter 15s linear infinite;
  pointer-events: none;
  z-index: 0;
  opacity: 0.6;
}

.glass-surface__content {
  position: relative;
  z-index: 1;
}
