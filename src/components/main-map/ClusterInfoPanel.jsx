import React from 'react';
import './ClusterInfoPanel.css';

/**
 * Cluster信息面板组件
 * 显示选中cluster的详细统计信息
 */
const ClusterInfoPanel = ({ 
  selectedCluster, 
  clusterStats, 
  isVisible, 
  onClose,
  clusterColor 
}) => {
  if (!isVisible || selectedCluster === null || !clusterStats) {
    return null;
  }

  const formatCurrency = (value) => {
    if (!value || isNaN(value)) return '$0';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatNumber = (value) => {
    if (!value || isNaN(value)) return '0';
    return new Intl.NumberFormat('en-US').format(value);
  };

  const formatPercentage = (value) => {
    if (!value || isNaN(value)) return '0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  return (
    <div className="cluster-info-panel">
      <div className="cluster-panel-header">
        <div className="cluster-title">
          <div 
            className="cluster-color-indicator"
            style={{ backgroundColor: clusterColor }}
          ></div>
          <h3>Cluster {selectedCluster} Analysis</h3>
        </div>
        <button 
          className="close-button"
          onClick={onClose}
          title="Close panel"
        >
          ×
        </button>
      </div>

      <div className="cluster-panel-content">
        <div className="metrics-grid">
          {/* 基础统计 */}
          <div className="metric-section">
            <h4>Basic Statistics</h4>
            <div className="metric-row">
              <span className="metric-label">Total Parcels:</span>
              <span className="metric-value">{formatNumber(clusterStats.num_parcels)}</span>
            </div>
            <div className="metric-row">
              <span className="metric-label">Avg Parcel Size:</span>
              <span className="metric-value">{formatNumber(clusterStats.avg_lot_area_ft)} sq ft</span>
            </div>
            <div className="metric-row">
              <span className="metric-label">Building Density:</span>
              <span className="metric-value">{formatPercentage(clusterStats.building_density_ratio)}</span>
            </div>
          </div>

          {/* 财务指标 */}
          <div className="metric-section">
            <h4>Financial Metrics</h4>
            <div className="metric-row">
              <span className="metric-label">Total NOI:</span>
              <span className="metric-value highlight">{formatCurrency(clusterStats.total_noi)}</span>
            </div>
            <div className="metric-row">
              <span className="metric-label">Avg NOI per Parcel:</span>
              <span className="metric-value">{formatCurrency(clusterStats.avg_noi)}</span>
            </div>
            <div className="metric-row">
              <span className="metric-label">Total Annual Taxes:</span>
              <span className="metric-value">{formatCurrency(clusterStats.total_annual_taxes)}</span>
            </div>
          </div>

          {/* 市场价值 */}
          <div className="metric-section">
            <h4>Market Value</h4>
            <div className="metric-row">
              <span className="metric-label">Total Market Value:</span>
              <span className="metric-value highlight">{formatCurrency(clusterStats.total_latest_sale_price)}</span>
            </div>
            <div className="metric-row">
              <span className="metric-label">Avg Sale Price:</span>
              <span className="metric-value">{formatCurrency(clusterStats.avg_latest_sale_price)}</span>
            </div>
            <div className="metric-row">
              <span className="metric-label">Price per Sq Ft:</span>
              <span className="metric-value">
                {clusterStats.avg_latest_sale_price && clusterStats.avg_lot_area_ft 
                  ? formatCurrency(clusterStats.avg_latest_sale_price / clusterStats.avg_lot_area_ft)
                  : '$0'
                }
              </span>
            </div>
          </div>

          {/* 地理分布 */}
          <div className="metric-section">
            <h4>Geographic Distribution</h4>
            <div className="metric-row">
              <span className="metric-label">Center Latitude:</span>
              <span className="metric-value">{clusterStats.center_lat?.toFixed(4) || 'N/A'}</span>
            </div>
            <div className="metric-row">
              <span className="metric-label">Center Longitude:</span>
              <span className="metric-value">{clusterStats.center_lng?.toFixed(4) || 'N/A'}</span>
            </div>
            <div className="metric-row">
              <span className="metric-label">Cluster Radius:</span>
              <span className="metric-value">{clusterStats.radius_miles?.toFixed(2) || 'N/A'} miles</span>
            </div>
          </div>
        </div>

        {/* 投资建议 */}
        <div className="investment-summary">
          <h4>Investment Summary</h4>
          <div className="summary-content">
            <p>
              This cluster contains <strong>{formatNumber(clusterStats.num_parcels)}</strong> industrial parcels 
              with a combined market value of <strong>{formatCurrency(clusterStats.total_latest_sale_price)}</strong> 
              and annual NOI of <strong>{formatCurrency(clusterStats.total_noi)}</strong>.
            </p>
            <p>
              The average building density is <strong>{formatPercentage(clusterStats.building_density_ratio)}</strong>, 
              indicating {clusterStats.building_density_ratio > 0.5 ? 'high' : 'moderate'} development intensity.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClusterInfoPanel;
