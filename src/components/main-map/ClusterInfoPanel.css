/* Cluster信息面板样式 */
.cluster-info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.cluster-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.cluster-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.cluster-color-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cluster-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.cluster-panel-content {
  padding: 20px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.metrics-grid {
  display: grid;
  gap: 20px;
  margin-bottom: 20px;
}

.metric-section {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.metric-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.metric-row:last-child {
  border-bottom: none;
}

.metric-label {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
}

.metric-value {
  font-size: 14px;
  color: #212529;
  font-weight: 600;
  text-align: right;
}

.metric-value.highlight {
  color: #28a745;
  font-size: 15px;
}

.investment-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.investment-summary h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-content p {
  margin: 0 0 12px 0;
  font-size: 13px;
  line-height: 1.5;
  color: #495057;
}

.summary-content p:last-child {
  margin-bottom: 0;
}

.summary-content strong {
  color: #212529;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cluster-info-panel {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
    max-height: 70vh;
  }
  
  .cluster-panel-header {
    padding: 12px 16px;
  }
  
  .cluster-title h3 {
    font-size: 16px;
  }
  
  .cluster-panel-content {
    padding: 16px;
  }
}

/* 滚动条样式 */
.cluster-panel-content::-webkit-scrollbar {
  width: 6px;
}

.cluster-panel-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.cluster-panel-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.cluster-panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 动画效果 */
.cluster-info-panel {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
