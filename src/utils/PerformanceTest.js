/**
 * 性能测试工具 - 用于验证优化效果
 */

class PerformanceTest {
  constructor() {
    this.tests = new Map();
    this.results = new Map();
    this.isRunning = false;
  }
  
  /**
   * 添加性能测试
   */
  addTest(name, testFunction, options = {}) {
    this.tests.set(name, {
      function: testFunction,
      iterations: options.iterations || 1000,
      warmup: options.warmup || 100,
      description: options.description || ''
    });
  }
  
  /**
   * 运行单个测试
   */
  async runTest(name) {
    const test = this.tests.get(name);
    if (!test) {
      throw new Error(`Test "${name}" not found`);
    }
    
    console.log(`Running test: ${name}`);
    
    // 预热
    for (let i = 0; i < test.warmup; i++) {
      await test.function();
    }
    
    // 正式测试
    const startTime = performance.now();
    const startMemory = this.getMemoryUsage();
    
    for (let i = 0; i < test.iterations; i++) {
      await test.function();
    }
    
    const endTime = performance.now();
    const endMemory = this.getMemoryUsage();
    
    const result = {
      name,
      description: test.description,
      iterations: test.iterations,
      totalTime: endTime - startTime,
      averageTime: (endTime - startTime) / test.iterations,
      memoryDelta: endMemory - startMemory,
      timestamp: new Date().toISOString()
    };
    
    this.results.set(name, result);
    console.log(`Test "${name}" completed:`, result);
    
    return result;
  }
  
  /**
   * 运行所有测试
   */
  async runAllTests() {
    if (this.isRunning) {
      console.warn('Tests are already running');
      return;
    }
    
    this.isRunning = true;
    const results = [];
    
    try {
      for (const [name] of this.tests) {
        const result = await this.runTest(name);
        results.push(result);
        
        // 在测试间稍作停顿，让系统恢复
        await this.sleep(100);
      }
    } finally {
      this.isRunning = false;
    }
    
    return results;
  }
  
  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    if ('memory' in performance) {
      return performance.memory.usedJSHeapSize;
    }
    return 0;
  }
  
  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 生成测试报告
   */
  generateReport() {
    const results = Array.from(this.results.values());
    
    if (results.length === 0) {
      return 'No test results available';
    }
    
    let report = '# Performance Test Report\n\n';
    report += `Generated: ${new Date().toISOString()}\n\n`;
    
    results.forEach(result => {
      report += `## ${result.name}\n`;
      if (result.description) {
        report += `Description: ${result.description}\n`;
      }
      report += `Iterations: ${result.iterations}\n`;
      report += `Total Time: ${result.totalTime.toFixed(2)}ms\n`;
      report += `Average Time: ${result.averageTime.toFixed(4)}ms\n`;
      report += `Memory Delta: ${(result.memoryDelta / 1024 / 1024).toFixed(2)}MB\n\n`;
    });
    
    return report;
  }
  
  /**
   * 比较两次测试结果
   */
  compareResults(name1, name2) {
    const result1 = this.results.get(name1);
    const result2 = this.results.get(name2);
    
    if (!result1 || !result2) {
      throw new Error('One or both test results not found');
    }
    
    const timeDiff = ((result2.averageTime - result1.averageTime) / result1.averageTime) * 100;
    const memoryDiff = result2.memoryDelta - result1.memoryDelta;
    
    return {
      timeImprovement: -timeDiff, // 负值表示改善
      memoryImprovement: -memoryDiff,
      summary: timeDiff < 0 ? 'Performance improved' : 'Performance degraded'
    };
  }
  
  /**
   * 清除测试结果
   */
  clearResults() {
    this.results.clear();
  }
}

// 创建全局测试实例
const performanceTest = new PerformanceTest();

// 添加预定义的测试
performanceTest.addTest('particleCreation', () => {
  // 测试粒子创建性能
  const particles = [];
  for (let i = 0; i < 100; i++) {
    particles.push({
      x: Math.random() * 1000,
      y: Math.random() * 1000,
      z: Math.random() * 1000,
      vx: Math.random() * 2 - 1,
      vy: Math.random() * 2 - 1,
      vz: Math.random() * 2 - 1
    });
  }
  return particles;
}, {
  iterations: 1000,
  description: 'Test particle creation performance'
});

performanceTest.addTest('mathOperations', () => {
  // 测试数学运算性能
  let result = 0;
  for (let i = 0; i < 1000; i++) {
    result += Math.sin(i) * Math.cos(i) + Math.sqrt(i);
  }
  return result;
}, {
  iterations: 100,
  description: 'Test mathematical operations performance'
});

performanceTest.addTest('domManipulation', () => {
  // 测试DOM操作性能
  const div = document.createElement('div');
  div.style.transform = `translate3d(${Math.random() * 100}px, ${Math.random() * 100}px, 0)`;
  div.style.opacity = Math.random();
  document.body.appendChild(div);
  document.body.removeChild(div);
}, {
  iterations: 100,
  description: 'Test DOM manipulation performance'
});

performanceTest.addTest('arrayOperations', () => {
  // 测试数组操作性能
  const arr = new Array(1000).fill(0).map(() => Math.random());
  const filtered = arr.filter(x => x > 0.5);
  const mapped = filtered.map(x => x * 2);
  return mapped.reduce((a, b) => a + b, 0);
}, {
  iterations: 1000,
  description: 'Test array operations performance'
});

export default performanceTest;

/**
 * React Hook for performance testing
 */
export const usePerformanceTest = () => {
  const [isRunning, setIsRunning] = React.useState(false);
  const [results, setResults] = React.useState([]);
  
  const runTests = React.useCallback(async () => {
    setIsRunning(true);
    try {
      const testResults = await performanceTest.runAllTests();
      setResults(testResults);
    } finally {
      setIsRunning(false);
    }
  }, []);
  
  const runSingleTest = React.useCallback(async (testName) => {
    setIsRunning(true);
    try {
      const result = await performanceTest.runTest(testName);
      setResults(prev => [...prev.filter(r => r.name !== testName), result]);
      return result;
    } finally {
      setIsRunning(false);
    }
  }, []);
  
  const clearResults = React.useCallback(() => {
    performanceTest.clearResults();
    setResults([]);
  }, []);
  
  return {
    isRunning,
    results,
    runTests,
    runSingleTest,
    clearResults,
    generateReport: performanceTest.generateReport.bind(performanceTest),
    compareResults: performanceTest.compareResults.bind(performanceTest)
  };
};
