import * as L from 'leaflet';

/**
 * 创建稳定的地图标记图标
 * @param {string} color - 标记颜色 ('red', 'blue' 或十六进制颜色值如 '#FF6B6B')
 * @returns {L.DivIcon} - Leaflet 图标对象
 */
export const createStableMarker = (color) => {
  // 检查是否为十六进制颜色值
  const isHexColor = color.startsWith('#');

  let iconHtml;
  if (isHexColor) {
    // 使用内联样式为十六进制颜色
    iconHtml = `
      <div class="animated-marker custom-color" style="--marker-color: ${color}">
        <div class="marker-outer-glow"></div>
        <div class="marker-inner"></div>
      </div>
    `;
  } else {
    // 使用CSS类为预定义颜色
    iconHtml = `
      <div class="animated-marker ${color}">
        <div class="marker-outer-glow"></div>
        <div class="marker-inner"></div>
      </div>
    `;
  }

  return L.divIcon({
    className: '',
    html: iconHtml,
    iconSize: [32, 32],
    iconAnchor: [16, 16],
    popupAnchor: [0, -16]
  });
};

/**
 * 创建用户自定义标记图标
 * @param {string} color - 标记颜色 ('green', 'purple', 'orange', 'blue', 'red' 等)
 * @returns {L.DivIcon} - Leaflet 图标对象
 */
export const createCustomMarker = (color = 'green') => {
  // 确保颜色有效，如果无效则使用默认绿色
  const validColors = ['green', 'purple', 'orange', 'blue', 'red'];
  const safeColor = validColors.includes(color) ? color : 'green';

  const iconHtml = `
    <div class="custom-user-marker ${safeColor}">
      <div class="custom-marker-outer"></div>
      <div class="custom-marker-inner"></div>
      <div class="custom-marker-center"></div>
    </div>
  `;
  return L.divIcon({
    className: '',
    html: iconHtml,
    iconSize: [36, 36],
    iconAnchor: [18, 18],
    popupAnchor: [0, -18]
  });
};
