/**
 * 内存管理器 - 优化内存使用和防止内存泄漏
 */

class MemoryManager {
  constructor() {
    this.textureCache = new Map();
    this.geometryCache = new Map();
    this.materialCache = new Map();
    this.eventListeners = new Map();
    this.timers = new Set();
    this.observers = new Set();
    this.weakRefs = new WeakMap();
    this.maxCacheSize = 50;
    this.cleanupInterval = 30000; // 30秒清理一次
    this.lastCleanup = Date.now();
    
    this.startCleanupTimer();
    this.setupMemoryMonitoring();
  }
  
  /**
   * 纹理缓存管理
   */
  cacheTexture(key, texture) {
    if (this.textureCache.size >= this.maxCacheSize) {
      this.cleanOldestTextures();
    }
    
    this.textureCache.set(key, {
      texture,
      lastUsed: Date.now(),
      useCount: 1
    });
    
    return texture;
  }
  
  getTexture(key) {
    const cached = this.textureCache.get(key);
    if (cached) {
      cached.lastUsed = Date.now();
      cached.useCount++;
      return cached.texture;
    }
    return null;
  }
  
  cleanOldestTextures() {
    const entries = Array.from(this.textureCache.entries());
    entries.sort((a, b) => a[1].lastUsed - b[1].lastUsed);
    
    // 删除最旧的25%
    const deleteCount = Math.floor(entries.length * 0.25);
    for (let i = 0; i < deleteCount; i++) {
      const [key, data] = entries[i];
      this.disposeTexture(data.texture);
      this.textureCache.delete(key);
    }
  }
  
  disposeTexture(texture) {
    if (texture && texture.dispose) {
      texture.dispose();
    }
  }
  
  /**
   * 几何体缓存管理
   */
  cacheGeometry(key, geometry) {
    if (this.geometryCache.size >= this.maxCacheSize) {
      this.cleanOldestGeometries();
    }
    
    this.geometryCache.set(key, {
      geometry,
      lastUsed: Date.now(),
      useCount: 1
    });
    
    return geometry;
  }
  
  getGeometry(key) {
    const cached = this.geometryCache.get(key);
    if (cached) {
      cached.lastUsed = Date.now();
      cached.useCount++;
      return cached.geometry;
    }
    return null;
  }
  
  cleanOldestGeometries() {
    const entries = Array.from(this.geometryCache.entries());
    entries.sort((a, b) => a[1].lastUsed - b[1].lastUsed);
    
    const deleteCount = Math.floor(entries.length * 0.25);
    for (let i = 0; i < deleteCount; i++) {
      const [key, data] = entries[i];
      this.disposeGeometry(data.geometry);
      this.geometryCache.delete(key);
    }
  }
  
  disposeGeometry(geometry) {
    if (geometry && geometry.dispose) {
      geometry.dispose();
    }
  }
  
  /**
   * 材质缓存管理
   */
  cacheMaterial(key, material) {
    if (this.materialCache.size >= this.maxCacheSize) {
      this.cleanOldestMaterials();
    }
    
    this.materialCache.set(key, {
      material,
      lastUsed: Date.now(),
      useCount: 1
    });
    
    return material;
  }
  
  getMaterial(key) {
    const cached = this.materialCache.get(key);
    if (cached) {
      cached.lastUsed = Date.now();
      cached.useCount++;
      return cached.material;
    }
    return null;
  }
  
  cleanOldestMaterials() {
    const entries = Array.from(this.materialCache.entries());
    entries.sort((a, b) => a[1].lastUsed - b[1].lastUsed);
    
    const deleteCount = Math.floor(entries.length * 0.25);
    for (let i = 0; i < deleteCount; i++) {
      const [key, data] = entries[i];
      this.disposeMaterial(data.material);
      this.materialCache.delete(key);
    }
  }
  
  disposeMaterial(material) {
    if (material && material.dispose) {
      material.dispose();
    }
  }
  
  /**
   * 事件监听器管理
   */
  addEventListenerTracked(element, event, handler, options) {
    const key = `${element.constructor.name}-${event}-${Date.now()}`;
    
    element.addEventListener(event, handler, options);
    
    this.eventListeners.set(key, {
      element,
      event,
      handler,
      options
    });
    
    return key;
  }
  
  removeEventListenerTracked(key) {
    const listener = this.eventListeners.get(key);
    if (listener) {
      listener.element.removeEventListener(listener.event, listener.handler, listener.options);
      this.eventListeners.delete(key);
    }
  }
  
  /**
   * 定时器管理
   */
  setTimeoutTracked(callback, delay) {
    const id = setTimeout(() => {
      this.timers.delete(id);
      callback();
    }, delay);
    
    this.timers.add(id);
    return id;
  }
  
  setIntervalTracked(callback, interval) {
    const id = setInterval(callback, interval);
    this.timers.add(id);
    return id;
  }
  
  clearTimeoutTracked(id) {
    clearTimeout(id);
    this.timers.delete(id);
  }
  
  clearIntervalTracked(id) {
    clearInterval(id);
    this.timers.delete(id);
  }
  
  /**
   * 观察者管理
   */
  addObserverTracked(observer) {
    this.observers.add(observer);
    return observer;
  }
  
  removeObserverTracked(observer) {
    if (observer && observer.disconnect) {
      observer.disconnect();
    }
    this.observers.delete(observer);
  }
  
  /**
   * 内存监控
   */
  setupMemoryMonitoring() {
    if ('memory' in performance) {
      this.memoryCheckInterval = this.setIntervalTracked(() => {
        const memory = performance.memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;
        
        // 如果内存使用超过80%，触发清理
        if (usedMB / limitMB > 0.8) {
          this.forceCleanup();
        }
      }, 10000); // 每10秒检查一次
    }
  }
  
  /**
   * 强制清理
   */
  forceCleanup() {
    console.log('MemoryManager: Performing force cleanup');
    
    // 清理缓存
    this.cleanOldestTextures();
    this.cleanOldestGeometries();
    this.cleanOldestMaterials();
    
    // 触发垃圾回收（如果可用）
    if (window.gc) {
      window.gc();
    }
    
    this.lastCleanup = Date.now();
  }
  
  /**
   * 定期清理
   */
  startCleanupTimer() {
    this.cleanupTimer = this.setIntervalTracked(() => {
      const now = Date.now();
      
      if (now - this.lastCleanup > this.cleanupInterval) {
        this.performRoutineCleanup();
        this.lastCleanup = now;
      }
    }, 5000); // 每5秒检查一次
  }
  
  performRoutineCleanup() {
    // 清理长时间未使用的缓存项
    const cutoffTime = Date.now() - 60000; // 1分钟前
    
    // 清理纹理
    for (const [key, data] of this.textureCache.entries()) {
      if (data.lastUsed < cutoffTime && data.useCount < 3) {
        this.disposeTexture(data.texture);
        this.textureCache.delete(key);
      }
    }
    
    // 清理几何体
    for (const [key, data] of this.geometryCache.entries()) {
      if (data.lastUsed < cutoffTime && data.useCount < 3) {
        this.disposeGeometry(data.geometry);
        this.geometryCache.delete(key);
      }
    }
    
    // 清理材质
    for (const [key, data] of this.materialCache.entries()) {
      if (data.lastUsed < cutoffTime && data.useCount < 3) {
        this.disposeMaterial(data.material);
        this.materialCache.delete(key);
      }
    }
  }
  
  /**
   * 获取内存使用统计
   */
  getMemoryStats() {
    const stats = {
      textureCache: this.textureCache.size,
      geometryCache: this.geometryCache.size,
      materialCache: this.materialCache.size,
      eventListeners: this.eventListeners.size,
      timers: this.timers.size,
      observers: this.observers.size
    };
    
    if ('memory' in performance) {
      const memory = performance.memory;
      stats.jsHeapSize = {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      };
    }
    
    return stats;
  }
  
  /**
   * 销毁内存管理器
   */
  destroy() {
    // 清理所有缓存
    this.textureCache.forEach(data => this.disposeTexture(data.texture));
    this.geometryCache.forEach(data => this.disposeGeometry(data.geometry));
    this.materialCache.forEach(data => this.disposeMaterial(data.material));
    
    // 清理事件监听器
    this.eventListeners.forEach((listener, key) => {
      this.removeEventListenerTracked(key);
    });
    
    // 清理定时器
    this.timers.forEach(id => {
      clearTimeout(id);
      clearInterval(id);
    });
    
    // 清理观察者
    this.observers.forEach(observer => {
      if (observer && observer.disconnect) {
        observer.disconnect();
      }
    });
    
    // 清空所有集合
    this.textureCache.clear();
    this.geometryCache.clear();
    this.materialCache.clear();
    this.eventListeners.clear();
    this.timers.clear();
    this.observers.clear();
  }
}

// 创建全局单例
const memoryManager = new MemoryManager();

export default memoryManager;

/**
 * React Hook for memory management
 */
export const useMemoryManager = () => {
  const [stats, setStats] = React.useState(memoryManager.getMemoryStats());
  
  React.useEffect(() => {
    const interval = setInterval(() => {
      setStats(memoryManager.getMemoryStats());
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);
  
  return {
    stats,
    forceCleanup: memoryManager.forceCleanup.bind(memoryManager),
    cacheTexture: memoryManager.cacheTexture.bind(memoryManager),
    getTexture: memoryManager.getTexture.bind(memoryManager),
    cacheGeometry: memoryManager.cacheGeometry.bind(memoryManager),
    getGeometry: memoryManager.getGeometry.bind(memoryManager),
    cacheMaterial: memoryManager.cacheMaterial.bind(memoryManager),
    getMaterial: memoryManager.getMaterial.bind(memoryManager)
  };
};
