/**
 * 性能管理器 - 统一控制页面性能优化
 * 实现自适应渲染、帧率监控、资源管理等功能
 */

class PerformanceManager {
  constructor() {
    this.frameRate = 60;
    this.targetFrameRate = 60;
    this.frameHistory = [];
    this.maxFrameHistory = 60; // 保存1秒的帧率历史
    this.performanceLevel = 'high'; // high, medium, low
    this.isMonitoring = false;
    this.callbacks = new Map();
    this.rafId = null;
    this.lastFrameTime = 0;
    this.frameCount = 0;
    
    // 性能阈值配置
    this.thresholds = {
      high: { minFps: 55, particleCount: 1.0, animationSpeed: 1.0 },
      medium: { minFps: 30, particleCount: 0.6, animationSpeed: 0.7 },
      low: { minFps: 20, particleCount: 0.3, animationSpeed: 0.5 }
    };
    
    // 自适应配置
    this.adaptiveConfig = {
      particleMultiplier: 1.0,
      animationSpeedMultiplier: 1.0,
      renderQuality: 1.0,
      enableComplexEffects: true,
      maxParticles: 1000,
      updateFrequency: 1 // 每帧更新
    };
    
    this.init();
  }
  
  init() {
    this.detectDeviceCapabilities();
    this.startMonitoring();
  }
  
  /**
   * 检测设备性能能力
   */
  detectDeviceCapabilities() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!gl) {
      this.performanceLevel = 'low';
      return;
    }
    
    // 检测GPU性能
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    const renderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : '';
    
    // 检测内存
    const memory = navigator.deviceMemory || 4; // 默认4GB
    
    // 检测CPU核心数
    const cores = navigator.hardwareConcurrency || 4;
    
    // 基于硬件信息设置初始性能级别
    if (memory >= 8 && cores >= 8) {
      this.performanceLevel = 'high';
    } else if (memory >= 4 && cores >= 4) {
      this.performanceLevel = 'medium';
    } else {
      this.performanceLevel = 'low';
    }
    
    this.updateAdaptiveConfig();
  }
  
  /**
   * 开始性能监控
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.lastFrameTime = performance.now();
    this.monitorFrame();
  }
  
  /**
   * 停止性能监控
   */
  stopMonitoring() {
    this.isMonitoring = false;
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
  }
  
  /**
   * 监控帧率
   */
  monitorFrame() {
    if (!this.isMonitoring) return;
    
    const currentTime = performance.now();
    const deltaTime = currentTime - this.lastFrameTime;
    
    if (deltaTime > 0) {
      const currentFps = 1000 / deltaTime;
      this.frameHistory.push(currentFps);
      
      // 保持历史记录在限制范围内
      if (this.frameHistory.length > this.maxFrameHistory) {
        this.frameHistory.shift();
      }
      
      // 计算平均帧率
      this.frameRate = this.frameHistory.reduce((a, b) => a + b, 0) / this.frameHistory.length;
      
      // 每30帧检查一次性能并调整
      if (this.frameCount % 30 === 0) {
        this.adjustPerformance();
      }
      
      this.frameCount++;
    }
    
    this.lastFrameTime = currentTime;
    this.rafId = requestAnimationFrame(() => this.monitorFrame());
  }
  
  /**
   * 根据帧率自动调整性能
   */
  adjustPerformance() {
    const currentThreshold = this.thresholds[this.performanceLevel];
    
    if (this.frameRate < currentThreshold.minFps) {
      // 性能不足，降级
      this.downgradePerformance();
    } else if (this.frameRate > this.targetFrameRate * 0.9 && this.performanceLevel !== 'high') {
      // 性能充足，可以升级
      this.upgradePerformance();
    }
  }
  
  /**
   * 降级性能设置
   */
  downgradePerformance() {
    if (this.performanceLevel === 'high') {
      this.performanceLevel = 'medium';
    } else if (this.performanceLevel === 'medium') {
      this.performanceLevel = 'low';
    }
    
    this.updateAdaptiveConfig();
    this.notifyCallbacks('performanceDowngrade', this.performanceLevel);
  }
  
  /**
   * 升级性能设置
   */
  upgradePerformance() {
    if (this.performanceLevel === 'low') {
      this.performanceLevel = 'medium';
    } else if (this.performanceLevel === 'medium') {
      this.performanceLevel = 'high';
    }
    
    this.updateAdaptiveConfig();
    this.notifyCallbacks('performanceUpgrade', this.performanceLevel);
  }
  
  /**
   * 更新自适应配置
   */
  updateAdaptiveConfig() {
    const threshold = this.thresholds[this.performanceLevel];
    
    this.adaptiveConfig = {
      particleMultiplier: threshold.particleCount,
      animationSpeedMultiplier: threshold.animationSpeed,
      renderQuality: threshold.particleCount,
      enableComplexEffects: this.performanceLevel !== 'low',
      maxParticles: Math.floor(1000 * threshold.particleCount),
      updateFrequency: this.performanceLevel === 'high' ? 1 : (this.performanceLevel === 'medium' ? 2 : 3)
    };
  }
  
  /**
   * 注册性能变化回调
   */
  onPerformanceChange(id, callback) {
    this.callbacks.set(id, callback);
  }
  
  /**
   * 移除性能变化回调
   */
  offPerformanceChange(id) {
    this.callbacks.delete(id);
  }
  
  /**
   * 通知所有回调
   */
  notifyCallbacks(event, data) {
    this.callbacks.forEach(callback => {
      try {
        callback(event, data, this.adaptiveConfig);
      } catch (error) {
        console.warn('Performance callback error:', error);
      }
    });
  }
  
  /**
   * 获取当前性能配置
   */
  getConfig() {
    return {
      ...this.adaptiveConfig,
      performanceLevel: this.performanceLevel,
      frameRate: this.frameRate,
      targetFrameRate: this.targetFrameRate
    };
  }
  
  /**
   * 手动设置性能级别
   */
  setPerformanceLevel(level) {
    if (['high', 'medium', 'low'].includes(level)) {
      this.performanceLevel = level;
      this.updateAdaptiveConfig();
      this.notifyCallbacks('manualPerformanceChange', level);
    }
  }
  
  /**
   * 获取性能统计信息
   */
  getStats() {
    return {
      frameRate: this.frameRate,
      performanceLevel: this.performanceLevel,
      frameHistory: [...this.frameHistory],
      adaptiveConfig: { ...this.adaptiveConfig }
    };
  }
  
  /**
   * 销毁性能管理器
   */
  destroy() {
    this.stopMonitoring();
    this.callbacks.clear();
    this.frameHistory = [];
  }
}

// 创建全局单例
const performanceManager = new PerformanceManager();

export default performanceManager;

/**
 * React Hook for using performance manager
 */
export const usePerformanceManager = () => {
  const [config, setConfig] = React.useState(performanceManager.getConfig());
  
  React.useEffect(() => {
    const handlePerformanceChange = (event, level, newConfig) => {
      setConfig(performanceManager.getConfig());
    };
    
    const id = `hook-${Date.now()}-${Math.random()}`;
    performanceManager.onPerformanceChange(id, handlePerformanceChange);
    
    return () => {
      performanceManager.offPerformanceChange(id);
    };
  }, []);
  
  return {
    config,
    stats: performanceManager.getStats(),
    setPerformanceLevel: performanceManager.setPerformanceLevel.bind(performanceManager)
  };
};
