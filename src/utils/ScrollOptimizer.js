/**
 * 滚动优化器 - 提升页面滚动性能和流畅度
 */

class ScrollOptimizer {
  constructor() {
    this.isScrolling = false;
    this.scrollTimeout = null;
    this.lastScrollTime = 0;
    this.scrollVelocity = 0;
    this.lastScrollTop = 0;
    this.rafId = null;
    this.observers = new Map();
    this.throttledCallbacks = new Map();
    this.debouncedCallbacks = new Map();
    
    // 配置参数
    this.config = {
      throttleDelay: 16, // ~60fps
      debounceDelay: 150,
      velocityThreshold: 5,
      momentumDuration: 300,
      enableGPUAcceleration: true,
      enablePassiveListeners: true
    };
    
    this.init();
  }
  
  init() {
    this.setupScrollOptimization();
    this.setupIntersectionObserver();
    this.detectScrollCapabilities();
  }
  
  /**
   * 检测滚动能力
   */
  detectScrollCapabilities() {
    // 检测是否支持passive listeners
    let supportsPassive = false;
    try {
      const opts = Object.defineProperty({}, 'passive', {
        get() {
          supportsPassive = true;
          return false;
        }
      });
      window.addEventListener('testPassive', null, opts);
      window.removeEventListener('testPassive', null, opts);
    } catch (e) {}
    
    this.config.enablePassiveListeners = supportsPassive;
    
    // 检测是否支持smooth scrolling
    this.config.supportsSmoothScrolling = 'scrollBehavior' in document.documentElement.style;
    
    // 检测设备类型
    this.config.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    this.config.isTouch = 'ontouchstart' in window;
  }
  
  /**
   * 设置滚动优化
   */
  setupScrollOptimization() {
    // 优化CSS
    this.applyCSSOptimizations();
    
    // 设置全局滚动监听
    this.setupGlobalScrollListener();
    
    // 设置resize监听
    this.setupResizeListener();
  }
  
  /**
   * 应用CSS优化
   */
  applyCSSOptimizations() {
    const style = document.createElement('style');
    style.textContent = `
      /* 滚动优化 */
      html {
        scroll-behavior: ${this.config.supportsSmoothScrolling ? 'smooth' : 'auto'};
        -webkit-overflow-scrolling: touch;
        overflow-x: hidden;
      }
      
      body {
        -webkit-overflow-scrolling: touch;
        overflow-x: hidden;
      }
      
      /* 减少重绘和重排 */
      .scroll-optimized {
        will-change: transform;
        transform: translateZ(0);
        backface-visibility: hidden;
        perspective: 1000px;
      }
      
      /* 移动端优化 */
      @media (max-width: 768px) {
        * {
          -webkit-tap-highlight-color: transparent;
          -webkit-touch-callout: none;
          -webkit-user-select: none;
          user-select: none;
        }
        
        input, textarea, select {
          -webkit-user-select: auto;
          user-select: auto;
        }
      }
      
      /* 高DPI屏幕优化 */
      @media (-webkit-min-device-pixel-ratio: 2) {
        .scroll-optimized {
          transform: translate3d(0, 0, 0);
        }
      }
    `;
    
    document.head.appendChild(style);
  }
  
  /**
   * 设置全局滚动监听
   */
  setupGlobalScrollListener() {
    const options = this.config.enablePassiveListeners ? { passive: true } : false;
    
    // 节流的滚动处理
    const throttledScrollHandler = this.throttle((event) => {
      this.handleScroll(event);
    }, this.config.throttleDelay);
    
    // 防抖的滚动结束处理
    const debouncedScrollEndHandler = this.debounce(() => {
      this.handleScrollEnd();
    }, this.config.debounceDelay);
    
    window.addEventListener('scroll', (event) => {
      this.isScrolling = true;
      throttledScrollHandler(event);
      debouncedScrollEndHandler();
    }, options);
  }
  
  /**
   * 处理滚动事件
   */
  handleScroll(event) {
    const currentTime = performance.now();
    const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    // 计算滚动速度
    const deltaTime = currentTime - this.lastScrollTime;
    const deltaScroll = currentScrollTop - this.lastScrollTop;
    
    if (deltaTime > 0) {
      this.scrollVelocity = Math.abs(deltaScroll / deltaTime);
    }
    
    // 根据滚动速度调整优化策略
    this.adjustOptimizationLevel();
    
    // 更新记录
    this.lastScrollTime = currentTime;
    this.lastScrollTop = currentScrollTop;
    
    // 通知观察者
    this.notifyScrollObservers({
      scrollTop: currentScrollTop,
      velocity: this.scrollVelocity,
      direction: deltaScroll > 0 ? 'down' : 'up',
      isScrolling: this.isScrolling
    });
  }
  
  /**
   * 处理滚动结束
   */
  handleScrollEnd() {
    this.isScrolling = false;
    this.scrollVelocity = 0;
    
    // 恢复正常优化级别
    this.restoreOptimizationLevel();
    
    // 通知观察者
    this.notifyScrollObservers({
      scrollTop: this.lastScrollTop,
      velocity: 0,
      direction: null,
      isScrolling: false
    });
  }
  
  /**
   * 根据滚动速度调整优化级别
   */
  adjustOptimizationLevel() {
    if (this.scrollVelocity > this.config.velocityThreshold) {
      // 高速滚动时的优化
      this.enableHighSpeedOptimizations();
    } else {
      // 正常滚动时的优化
      this.enableNormalOptimizations();
    }
  }
  
  /**
   * 启用高速滚动优化
   */
  enableHighSpeedOptimizations() {
    // 暂停非关键动画
    document.body.classList.add('high-speed-scroll');
    
    // 降低复杂元素的渲染质量
    this.notifyScrollObservers({
      type: 'optimizationLevel',
      level: 'high-speed'
    });
  }
  
  /**
   * 启用正常滚动优化
   */
  enableNormalOptimizations() {
    document.body.classList.remove('high-speed-scroll');
    
    this.notifyScrollObservers({
      type: 'optimizationLevel',
      level: 'normal'
    });
  }
  
  /**
   * 恢复正常优化级别
   */
  restoreOptimizationLevel() {
    document.body.classList.remove('high-speed-scroll');
    
    this.notifyScrollObservers({
      type: 'optimizationLevel',
      level: 'idle'
    });
  }
  
  /**
   * 设置Intersection Observer
   */
  setupIntersectionObserver() {
    if (!('IntersectionObserver' in window)) {
      return;
    }
    
    // 创建多个观察者用于不同的优化策略
    this.createViewportObserver();
    this.createPreloadObserver();
  }
  
  /**
   * 创建视口观察者
   */
  createViewportObserver() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const element = entry.target;
        
        if (entry.isIntersecting) {
          // 元素进入视口
          element.classList.add('in-viewport');
          this.enableElementOptimizations(element);
        } else {
          // 元素离开视口
          element.classList.remove('in-viewport');
          this.disableElementOptimizations(element);
        }
      });
    }, {
      rootMargin: '50px',
      threshold: [0, 0.1, 0.5, 1]
    });
    
    this.observers.set('viewport', observer);
  }
  
  /**
   * 创建预加载观察者
   */
  createPreloadObserver() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // 预加载即将进入视口的内容
          this.preloadElement(entry.target);
        }
      });
    }, {
      rootMargin: '200px'
    });
    
    this.observers.set('preload', observer);
  }
  
  /**
   * 启用元素优化
   */
  enableElementOptimizations(element) {
    // 启用GPU加速
    if (this.config.enableGPUAcceleration) {
      element.style.willChange = 'transform';
      element.style.transform = 'translateZ(0)';
    }
    
    // 启用动画
    element.classList.add('animations-enabled');
  }
  
  /**
   * 禁用元素优化
   */
  disableElementOptimizations(element) {
    // 移除GPU加速
    element.style.willChange = 'auto';
    element.style.transform = '';
    
    // 禁用动画
    element.classList.remove('animations-enabled');
  }
  
  /**
   * 预加载元素
   */
  preloadElement(element) {
    // 预加载图片
    const images = element.querySelectorAll('img[data-src]');
    images.forEach(img => {
      if (img.dataset.src) {
        img.src = img.dataset.src;
        img.removeAttribute('data-src');
      }
    });
    
    // 预加载其他资源
    const lazyElements = element.querySelectorAll('[data-lazy]');
    lazyElements.forEach(el => {
      el.classList.add('lazy-loading');
    });
  }
  
  /**
   * 观察元素
   */
  observeElement(element, type = 'viewport') {
    const observer = this.observers.get(type);
    if (observer) {
      observer.observe(element);
    }
  }
  
  /**
   * 停止观察元素
   */
  unobserveElement(element, type = 'viewport') {
    const observer = this.observers.get(type);
    if (observer) {
      observer.unobserve(element);
    }
  }
  
  /**
   * 添加滚动观察者
   */
  addScrollObserver(id, callback) {
    this.observers.set(`scroll-${id}`, callback);
  }
  
  /**
   * 移除滚动观察者
   */
  removeScrollObserver(id) {
    this.observers.delete(`scroll-${id}`);
  }
  
  /**
   * 通知滚动观察者
   */
  notifyScrollObservers(data) {
    this.observers.forEach((callback, key) => {
      if (key.startsWith('scroll-') && typeof callback === 'function') {
        try {
          callback(data);
        } catch (error) {
          console.warn('Scroll observer error:', error);
        }
      }
    });
  }
  
  /**
   * 节流函数
   */
  throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
  
  /**
   * 防抖函数
   */
  debounce(func, wait) {
    let timeout;
    return function(...args) {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }
  
  /**
   * 设置resize监听
   */
  setupResizeListener() {
    const debouncedResize = this.debounce(() => {
      this.handleResize();
    }, 250);
    
    window.addEventListener('resize', debouncedResize, 
      this.config.enablePassiveListeners ? { passive: true } : false);
  }
  
  /**
   * 处理窗口大小变化
   */
  handleResize() {
    // 重新计算视口相关的优化
    this.notifyScrollObservers({
      type: 'resize',
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    });
  }
  
  /**
   * 销毁滚动优化器
   */
  destroy() {
    // 清理观察者
    this.observers.forEach(observer => {
      if (observer && observer.disconnect) {
        observer.disconnect();
      }
    });
    
    // 清理RAF
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
    }
    
    // 清理定时器
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    
    this.observers.clear();
  }
}

// 创建全局单例
const scrollOptimizer = new ScrollOptimizer();

export default scrollOptimizer;

/**
 * React Hook for scroll optimization
 */
export const useScrollOptimizer = () => {
  const [scrollData, setScrollData] = React.useState({
    scrollTop: 0,
    velocity: 0,
    direction: null,
    isScrolling: false
  });
  
  React.useEffect(() => {
    const handleScrollData = (data) => {
      setScrollData(prev => ({ ...prev, ...data }));
    };
    
    const id = `hook-${Date.now()}`;
    scrollOptimizer.addScrollObserver(id, handleScrollData);
    
    return () => {
      scrollOptimizer.removeScrollObserver(id);
    };
  }, []);
  
  return {
    scrollData,
    observeElement: scrollOptimizer.observeElement.bind(scrollOptimizer),
    unobserveElement: scrollOptimizer.unobserveElement.bind(scrollOptimizer)
  };
};
