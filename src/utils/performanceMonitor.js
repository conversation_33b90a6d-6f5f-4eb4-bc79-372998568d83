// 性能监控工具
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = [];
    this.isEnabled = process.env.NODE_ENV === 'development';
  }

  // 开始计时
  startTiming(label) {
    if (!this.isEnabled) return;
    
    this.metrics.set(label, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    });
  }

  // 结束计时
  endTiming(label) {
    if (!this.isEnabled) return;
    
    const metric = this.metrics.get(label);
    if (metric) {
      metric.endTime = performance.now();
      metric.duration = metric.endTime - metric.startTime;
      
      console.log(`⏱️ ${label}: ${metric.duration.toFixed(2)}ms`);
      
      // 通知观察者
      this.notifyObservers(label, metric);
    }
  }

  // 记录资源加载时间
  measureResourceLoad(url, type = 'unknown') {
    if (!this.isEnabled) return;
    
    const startTime = performance.now();
    
    return {
      finish: () => {
        const duration = performance.now() - startTime;
        const label = `Resource Load: ${type} - ${url.split('/').pop()}`;
        console.log(`📦 ${label}: ${duration.toFixed(2)}ms`);
        
        this.metrics.set(label, {
          startTime,
          endTime: performance.now(),
          duration,
          url,
          type
        });
      }
    };
  }

  // 监控组件渲染性能
  measureComponentRender(componentName) {
    if (!this.isEnabled) return () => {};
    
    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      const label = `Component Render: ${componentName}`;
      console.log(`🎨 ${label}: ${duration.toFixed(2)}ms`);
      
      this.metrics.set(label, {
        startTime,
        endTime: performance.now(),
        duration,
        componentName
      });
    };
  }

  // 监控内存使用
  measureMemoryUsage(label) {
    if (!this.isEnabled || !performance.memory) return;
    
    const memory = {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit
    };
    
    console.log(`🧠 Memory Usage (${label}):`, {
      used: `${(memory.used / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memory.total / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memory.limit / 1024 / 1024).toFixed(2)} MB`
    });
    
    this.metrics.set(`Memory: ${label}`, memory);
  }

  // 监控网络请求
  measureNetworkRequest(url, options = {}) {
    if (!this.isEnabled) return fetch(url, options);
    
    const startTime = performance.now();
    const label = `Network: ${url.split('/').pop()}`;
    
    console.log(`🌐 Starting request: ${url}`);
    
    return fetch(url, options)
      .then(response => {
        const duration = performance.now() - startTime;
        console.log(`✅ ${label}: ${duration.toFixed(2)}ms (${response.status})`);
        
        this.metrics.set(label, {
          startTime,
          endTime: performance.now(),
          duration,
          url,
          status: response.status,
          size: response.headers.get('content-length')
        });
        
        return response;
      })
      .catch(error => {
        const duration = performance.now() - startTime;
        console.error(`❌ ${label}: ${duration.toFixed(2)}ms (Error)`, error);
        
        this.metrics.set(label, {
          startTime,
          endTime: performance.now(),
          duration,
          url,
          error: error.message
        });
        
        throw error;
      });
  }

  // 添加观察者
  addObserver(callback) {
    this.observers.push(callback);
  }

  // 移除观察者
  removeObserver(callback) {
    const index = this.observers.indexOf(callback);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  // 通知观察者
  notifyObservers(label, metric) {
    this.observers.forEach(callback => {
      try {
        callback(label, metric);
      } catch (error) {
        console.error('Observer error:', error);
      }
    });
  }

  // 获取所有指标
  getAllMetrics() {
    return Object.fromEntries(this.metrics);
  }

  // 获取性能报告
  getPerformanceReport() {
    const metrics = this.getAllMetrics();
    const report = {
      totalMetrics: this.metrics.size,
      slowestOperations: [],
      resourceLoads: [],
      componentRenders: [],
      networkRequests: [],
      memorySnapshots: []
    };

    // 分类指标
    Object.entries(metrics).forEach(([label, metric]) => {
      if (label.startsWith('Resource Load:')) {
        report.resourceLoads.push({ label, ...metric });
      } else if (label.startsWith('Component Render:')) {
        report.componentRenders.push({ label, ...metric });
      } else if (label.startsWith('Network:')) {
        report.networkRequests.push({ label, ...metric });
      } else if (label.startsWith('Memory:')) {
        report.memorySnapshots.push({ label, ...metric });
      }
    });

    // 找出最慢的操作
    const allTimedOperations = [
      ...report.resourceLoads,
      ...report.componentRenders,
      ...report.networkRequests
    ].filter(op => op.duration);

    report.slowestOperations = allTimedOperations
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);

    return report;
  }

  // 清除所有指标
  clear() {
    this.metrics.clear();
  }

  // 启用/禁用监控
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor();

// 导出便捷方法
export const startTiming = (label) => performanceMonitor.startTiming(label);
export const endTiming = (label) => performanceMonitor.endTiming(label);
export const measureResourceLoad = (url, type) => performanceMonitor.measureResourceLoad(url, type);
export const measureComponentRender = (componentName) => performanceMonitor.measureComponentRender(componentName);
export const measureMemoryUsage = (label) => performanceMonitor.measureMemoryUsage(label);
export const measureNetworkRequest = (url, options) => performanceMonitor.measureNetworkRequest(url, options);
export const getPerformanceReport = () => performanceMonitor.getPerformanceReport();
export const clearMetrics = () => performanceMonitor.clear();

export default performanceMonitor;
