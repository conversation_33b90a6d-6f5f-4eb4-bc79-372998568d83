/**
 * Test file for ROI calculation functionality
 * This file tests the ROI calculation logic used in ParcelDetailPage
 */

// Mock parcel data for testing
const mockParcelData = {
  apn: "5591-001-001",
  building_area: 75000,
  noi: 950000,
  latest_sale_price: 15000000,
  annual_taxes: 150000,
  latitude: 34.052200,
  longitude: -118.243700
}

// ROI Calculation Function (copied from ParcelDetailPage.jsx for testing)
const calculateROI = (parcelData) => {
  if (!parcelData) return null

  // Basic assumptions for ROI calculation
  const buildingArea = parcelData.building_area || 50000
  const noi = parcelData.noi || 800000
  const annualTaxes = parcelData.annual_taxes || 120000
  
  // Estimated values for calculation
  const estimatedRentPerSqft = 8.50 // $8.50 per sq ft annually
  const operatingExpenseRatio = 0.25 // 25% of gross income
  const capRate = 0.065 // 6.5% cap rate
  const holdPeriod = 10 // 10 years
  const exitCapRate = 0.07 // 7% exit cap rate
  
  // Calculate annual rental income
  const annualRentalIncome = buildingArea * estimatedRentPerSqft
  
  // Calculate operating expenses
  const operatingExpenses = annualRentalIncome * operatingExpenseRatio + annualTaxes
  
  // Calculate NOI (use provided NOI or calculate)
  const calculatedNOI = noi || (annualRentalIncome - operatingExpenses)
  
  // Calculate property value based on NOI and cap rate
  const propertyValue = calculatedNOI / capRate
  
  // Calculate cash-on-cash return (assuming 75% LTV)
  const loanAmount = propertyValue * 0.75
  const equityInvestment = propertyValue - loanAmount
  const interestRate = 0.045 // 4.5% interest rate
  const loanTerm = 25 // 25 years
  
  // Calculate annual debt service (simplified)
  const monthlyPayment = loanAmount * (interestRate / 12) / (1 - Math.pow(1 + interestRate / 12, -loanTerm * 12))
  const annualDebtService = monthlyPayment * 12
  
  // Calculate annual cash flow
  const annualCashFlow = calculatedNOI - annualDebtService
  
  // Calculate cash-on-cash return
  const cashOnCashReturn = annualCashFlow / equityInvestment
  
  // Calculate exit value and total return
  const exitNOI = calculatedNOI * Math.pow(1.025, holdPeriod) // 2.5% annual NOI growth
  const exitValue = exitNOI / exitCapRate
  const remainingLoanBalance = loanAmount * Math.pow(1 + interestRate, holdPeriod) - 
    (monthlyPayment * 12 * (Math.pow(1 + interestRate, holdPeriod) - 1) / interestRate)
  const netExitProceeds = exitValue - Math.max(0, remainingLoanBalance)
  
  // Calculate total return and IRR (simplified)
  const totalCashFlow = annualCashFlow * holdPeriod + netExitProceeds
  const totalReturn = (totalCashFlow - equityInvestment) / equityInvestment
  const annualizedReturn = Math.pow(1 + totalReturn, 1 / holdPeriod) - 1
  
  return {
    propertyValue: Math.round(propertyValue),
    equityInvestment: Math.round(equityInvestment),
    annualNOI: Math.round(calculatedNOI),
    annualCashFlow: Math.round(annualCashFlow),
    cashOnCashReturn: (cashOnCashReturn * 100).toFixed(2),
    capRate: (capRate * 100).toFixed(2),
    totalReturn: (totalReturn * 100).toFixed(2),
    annualizedReturn: (annualizedReturn * 100).toFixed(2),
    exitValue: Math.round(exitValue),
    netExitProceeds: Math.round(netExitProceeds),
    annualRentalIncome: Math.round(annualRentalIncome),
    operatingExpenses: Math.round(operatingExpenses),
    annualDebtService: Math.round(annualDebtService)
  }
}

// Test function
function testROICalculation() {
  console.log("Testing ROI Calculation with mock data...")
  console.log("Mock Parcel Data:", mockParcelData)
  
  const result = calculateROI(mockParcelData)
  console.log("ROI Calculation Result:", result)
  
  // Basic validation
  if (result) {
    console.log("✅ ROI calculation successful")
    console.log(`Property Value: $${result.propertyValue.toLocaleString()}`)
    console.log(`Equity Investment: $${result.equityInvestment.toLocaleString()}`)
    console.log(`Annual NOI: $${result.annualNOI.toLocaleString()}`)
    console.log(`Cash-on-Cash Return: ${result.cashOnCashReturn}%`)
    console.log(`Total Return: ${result.totalReturn}%`)
    console.log(`Annualized Return: ${result.annualizedReturn}%`)
  } else {
    console.log("❌ ROI calculation failed")
  }
  
  return result
}

// Test with null data
function testNullData() {
  console.log("\nTesting with null data...")
  const result = calculateROI(null)
  console.log("Result with null data:", result)
  
  if (result === null) {
    console.log("✅ Null data handling correct")
  } else {
    console.log("❌ Null data handling failed")
  }
}

// Test with minimal data
function testMinimalData() {
  console.log("\nTesting with minimal data...")
  const minimalData = { apn: "TEST-001" }
  const result = calculateROI(minimalData)
  console.log("Result with minimal data:", result)
  
  if (result && result.propertyValue > 0) {
    console.log("✅ Minimal data handling correct - using defaults")
  } else {
    console.log("❌ Minimal data handling failed")
  }
}

// Run tests
if (typeof window !== 'undefined') {
  // Browser environment
  window.testROI = {
    testROICalculation,
    testNullData,
    testMinimalData,
    calculateROI
  }
  console.log("ROI test functions available as window.testROI")
} else {
  // Node environment
  testROICalculation()
  testNullData()
  testMinimalData()
}

export { calculateROI, testROICalculation, testNullData, testMinimalData }
