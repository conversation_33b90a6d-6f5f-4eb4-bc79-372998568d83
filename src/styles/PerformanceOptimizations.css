/* 性能优化CSS - 提升页面滑动流畅度和渲染性能 */

/* 全局性能优化 */
* {
  /* 减少重绘和重排 */
  box-sizing: border-box;
}

html {
  /* 优化滚动性能 */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  
  /* 减少字体渲染开销 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
}

body {
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  
  /* 减少重绘 */
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 容器级别优化 */
.welcome-page {
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: scroll-position;
  
  /* 优化滚动 */
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  
  /* 减少重排 */
  contain: layout style paint;
}

/* 3D场景容器优化 */
.earth-container,
.global-network-section,
.team-section {
  /* 启用GPU加速 */
  transform: translateZ(0);
  will-change: transform;
  
  /* 减少重绘 */
  backface-visibility: hidden;
  perspective: 1000px;
  
  /* 优化渲染层 */
  isolation: isolate;
}

/* Canvas优化 */
canvas {
  /* 启用硬件加速 */
  transform: translateZ(0);
  
  /* 减少重绘 */
  backface-visibility: hidden;
  
  /* 优化图像渲染 */
  image-rendering: optimizeSpeed;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
}

/* 粒子系统优化 */
.particles-container {
  /* 启用GPU加速 */
  transform: translateZ(0);
  will-change: transform;
  
  /* 减少重绘 */
  backface-visibility: hidden;
  
  /* 优化渲染层 */
  isolation: isolate;
  contain: layout style paint;
}

/* 动画优化 */
.scroll-optimized {
  /* 启用GPU加速 */
  transform: translateZ(0);
  will-change: transform;
  
  /* 减少重绘 */
  backface-visibility: hidden;
}

/* 高速滚动时的优化 */
.high-speed-scroll .feature-card,
.high-speed-scroll .team-photo-card,
.high-speed-scroll .particle {
  /* 暂停复杂动画 */
  animation-play-state: paused;
  
  /* 减少GPU负载 */
  will-change: auto;
}

.high-speed-scroll canvas {
  /* 降低渲染质量 */
  image-rendering: pixelated;
}

/* 视口外元素优化 */
.out-of-viewport {
  /* 暂停动画 */
  animation-play-state: paused;
  
  /* 减少GPU负载 */
  will-change: auto;
  
  /* 隐藏不可见元素 */
  visibility: hidden;
}

/* 进入视口的元素 */
.in-viewport {
  /* 恢复动画 */
  animation-play-state: running;
  
  /* 启用GPU加速 */
  will-change: transform;
  
  /* 显示元素 */
  visibility: visible;
}

/* 动画启用状态 */
.animations-enabled {
  /* 启用所有动画 */
  animation-play-state: running;
  transition: all 0.3s ease;
}

/* 懒加载优化 */
.lazy-loading {
  /* 启用GPU加速 */
  transform: translateZ(0);
  
  /* 淡入动画 */
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-loading.loaded {
  opacity: 1;
}

/* 内存优化 - 减少DOM复杂度 */
.simplified-rendering {
  /* 简化渲染 */
  contain: layout style paint;
  
  /* 减少重绘 */
  backface-visibility: hidden;
}

/* 低性能设备优化 */
.performance-low .feature-card {
  /* 禁用复杂效果 */
  box-shadow: none;
  backdrop-filter: none;
  
  /* 简化动画 */
  transition: opacity 0.2s ease;
}

.performance-low .particle,
.performance-low .floating-digit {
  /* 禁用粒子动画 */
  animation: none;
  display: none;
}

.performance-low canvas {
  /* 降低渲染质量 */
  image-rendering: pixelated;
}

/* 中等性能设备优化 */
.performance-medium .feature-card {
  /* 减少复杂效果 */
  backdrop-filter: blur(5px);
  
  /* 简化动画 */
  transition: all 0.2s ease;
}

.performance-medium .particle {
  /* 减少粒子数量 */
  opacity: 0.6;
}

/* 高性能设备优化 */
.performance-high .feature-card {
  /* 启用所有效果 */
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  
  /* 流畅动画 */
  transition: all 0.3s ease;
}

/* 移动端特殊优化 */
@media (max-width: 768px) {
  .welcome-page {
    /* 移动端滚动优化 */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: auto;
  }
  
  .earth-container,
  .global-network-section,
  .team-section {
    /* 减少GPU负载 */
    transform: none;
    will-change: auto;
  }
  
  canvas {
    /* 降低移动端渲染质量 */
    image-rendering: optimizeSpeed;
  }
  
  .particle,
  .floating-digit {
    /* 移动端减少动画 */
    animation-duration: 2s;
    animation-iteration-count: 1;
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2) {
  canvas {
    /* 高DPI屏幕优化 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
  }
  
  .scroll-optimized {
    /* 高DPI硬件加速 */
    transform: translate3d(0, 0, 0);
  }
}

/* 减少动画偏好用户的优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    /* 禁用所有动画 */
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .particle,
  .floating-digit,
  .meteor {
    /* 隐藏动画元素 */
    display: none !important;
  }
}

/* 内存压力下的优化 */
.memory-pressure .feature-card {
  /* 减少内存使用 */
  backdrop-filter: none;
  box-shadow: none;
}

.memory-pressure canvas {
  /* 降低渲染质量 */
  image-rendering: pixelated;
}

.memory-pressure .particle {
  /* 隐藏粒子 */
  display: none;
}

/* 电池优化模式 */
.battery-saver .earth-container,
.battery-saver .global-network-section,
.battery-saver .team-section {
  /* 禁用GPU加速 */
  transform: none;
  will-change: auto;
}

.battery-saver canvas {
  /* 最低渲染质量 */
  image-rendering: pixelated;
}

.battery-saver .particle,
.battery-saver .floating-digit,
.battery-saver .meteor {
  /* 禁用所有动画 */
  display: none;
}

/* 调试模式样式 */
.performance-debug {
  position: relative;
}

.performance-debug::after {
  content: attr(data-performance-level);
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  font-size: 10px;
  font-family: monospace;
  z-index: 10000;
  pointer-events: none;
}
