/* 可根据需要调整全局CSS变量 */
:root {
  --sidebar-bg: #ffffff;          /* 侧边栏背景 */
  --sidebar-text: #333333;        /* 侧边栏文字颜色 */
  --sidebar-border: rgba(0, 0, 0, 0.08);
  --sidebar-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  --sidebar-radius: 8px;
  --primary-color: #3498db;       /* 主色调 */
  --hover-bg: rgba(52, 152, 219, 0.08);
  --active-bg: rgba(52, 152, 219, 0.15);
  --transition: all 0.25s ease;
}

/* 页面主容器：左右结构 */
.map-page-container {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden; /* 保证侧边栏可垂直滚动，整体无外部滚动条 */
}

/* ============== 主侧边栏 ============== */
.main-sidebar {
  /* 宽度可拖拽 */
  width: 25vw;                /* 初始宽度，可按需调整 */
  min-width: 280px;          /* 最小宽度 */
  max-width: 75vw;           /* 最大宽度（相对视口宽度） */
  resize: horizontal;         /* 允许水平拖拽 */
  overflow: auto;            /* 当拖动变窄或内容超出时，可滚动 */

  height: 100%;              /* 占满可用高度 */
  background: var(--sidebar-bg);
  color: var(--sidebar-text);
  box-shadow: var(--sidebar-shadow);
  border-right: 1px solid var(--sidebar-border);

  display: flex;
  flex-direction: column;
}

/* 侧边栏顶部：标题 + 统计 */
.main-sidebar-header {
  padding: 20px;
  flex-shrink: 0; /* 保证此区不随滚动缩放 */
}

/* 顶部行：标题和语言切换按钮 */
.header-top-row {
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-sidebar-header h2 {
  margin: 0 auto;
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--sidebar-text);
  border-bottom: 2px solid var(--primary-color); /* 保留蓝色下划线 */
  padding-bottom: 8px; /* 为下划线留出空间 */
  text-align: center;
  width: fit-content;
}

/* 语言切换按钮 */
.language-toggle-button {
  background-color: rgba(0, 0, 0, 0.03);
  color: var(--sidebar-text);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 20px;
  padding: 6px 12px;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.language-toggle-button:hover {
  background-color: rgba(0, 0, 0, 0.06);
  border-color: rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
}

.language-toggle-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 完全移除灰线样式 */
.title-separator {
  display: none; /* 隐藏元素 */
  margin: 0;
  border: none;
}

/* 简化版国家选择器 */
.country-selector-simple {
  margin: 15px auto 10px;
  text-align: center;
  width: 50%;
  max-width: 150px;
}

.country-select-dropdown {
  width: 100%;
  padding: 8px 12px;
  border-radius: 20px;
  border: 1px solid var(--sidebar-border);
  background-color: white;
  color: var(--sidebar-text);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.25s ease;
  text-align: center;
  text-align-last: center;
  padding-right: 30px;
}

.country-select-dropdown:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.country-select-dropdown:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.locations-count {
  font-size: 0.9rem;
  color: #666;
  margin-top: 12px; /* 稍微调整与标题的间距 */
  text-align: center;
}



/* ============== 国家选择区 ============== */
.country-selector {
  padding: 20px;
  background: white;
  border-radius: var(--sidebar-radius);
  margin: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  border: 1px solid var(--sidebar-border);
}

.country-selector h3 {
  margin: 0 0 15px;
  font-size: 1.05rem;
  font-weight: 600;
  color: var(--sidebar-text);
  position: relative;
  padding-bottom: 10px;
}

.country-selector h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--primary-color);
}

.country-select {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8f9fa;
  padding: 12px 15px;
  border-radius: 8px;
  border: 1px solid var(--sidebar-border);
  transition: var(--transition);
  position: relative;
}

.country-select:hover {
  border-color: var(--primary-color);
  box-shadow: 0 3px 10px rgba(52, 152, 219, 0.1);
}

.country-label {
  font-weight: 500;
  color: #555;
  min-width: 60px;
}

.country-dropdown {
  flex: 1;
  padding: 8px 25px 8px 8px;
  border: none;
  background: none;
  font-size: 0.95rem;
  color: var(--sidebar-text);
  appearance: none;
  cursor: pointer;
  position: relative;
}

.country-dropdown:focus {
  outline: none;
}

/* 添加下拉箭头 */
.country-select::after {
  content: '\25BC';
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.7rem;
  color: #777;
  pointer-events: none;
  transition: transform 0.2s ease;
}

.country-select:hover::after {
  color: var(--primary-color);
  transform: translateY(-50%) rotate(180deg);
}

/* ============== 中间滚动内容区 ============== */
.main-sidebar-nav {
  flex: 1;              /* 填充剩余高度 */
  overflow-y: auto;     /* 垂直滚动 */
  padding: 0 20px 20px; /* 减少顶部内间距，保留左右和底部内间距 */
}

/* ============== 筛选区 ============== */
.filter-section {
  margin-bottom: 20px;
  background: white;
  border-radius: var(--sidebar-radius);
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  border: 1px solid var(--sidebar-border);
}

.filter-group {
  margin-bottom: 20px;
}

.filter-group h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--sidebar-text);
  border-left: 2px solid var(--primary-color);
  padding-left: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-icon {
  cursor: help;
  position: relative;
  font-size: 0.9rem;
}

.tooltip {
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  padding: 8px 12px;
  border-radius: var(--sidebar-radius);
  box-shadow: var(--sidebar-shadow);
  width: 200px;
  font-size: 0.8rem;
  color: var(--sidebar-text);
  z-index: 10;
  font-weight: normal;
}

/* 价格滑块 */
.price-slider-container {
  margin-top: 12px;
}

.price-slider {
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  appearance: none; /* 添加标准属性 */
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  outline: none;
  margin-bottom: 12px;
  cursor: pointer;
}

.price-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  box-shadow: 0 0 8px rgba(52, 152, 219, 0.6);
  cursor: pointer;
  transition: var(--transition);
}

.price-slider::-webkit-slider-thumb:hover {
  background: #2980b9;
  transform: scale(1.1);
}

.price-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.price-label {
  color: #7f8c8d;
}

.price-value {
  font-weight: 600;
  color: var(--primary-color);
  background: var(--active-bg);
  padding: 4px 12px;
  border-radius: 12px;
}

/* ============== 多选列表（州、城市等） ============== */
.multi-select,
.state-select,
.city-select {
  max-height: 180px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--sidebar-radius);
  border: 1px solid var(--sidebar-border);
  padding: 10px;
  margin-top: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 泰国省份选择器 - 新设计 */
.province-select {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  background: transparent;
  border: none;
  box-shadow: none;
  padding: 0;
  margin-top: 15px;
  max-height: none;
  overflow: visible;
}

/* 自定义滚动条 */
.multi-select::-webkit-scrollbar,
.state-select::-webkit-scrollbar,
.city-select::-webkit-scrollbar {
  width: 5px;
}

.multi-select::-webkit-scrollbar-thumb,
.state-select::-webkit-scrollbar-thumb,
.city-select::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}

/* 泰国省份项目和按钮样式 */
.province-item {
  position: relative;
}

.province-button {
  width: 100%;
  padding: 14px 15px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--sidebar-text);
  text-align: center;
  cursor: pointer;
  transition: all 0.25s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.province-button:hover {
  background-color: #f8f9fa;
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.province-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 添加装饰效果 */
.province-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), transparent);
  opacity: 0;
  transition: opacity 0.25s ease;
}

.province-button:hover::before {
  opacity: 1;
}

/* 省份图标和名称样式 */
.province-icon {
  font-size: 1.2rem;
  margin-bottom: 5px;
  color: var(--primary-color);
  transition: transform 0.3s ease;
}

.province-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--sidebar-text);
}

.province-button:hover .province-icon {
  transform: scale(1.2);
}

/* 复选框样式 */
.checkbox-item,
.checkbox-item.enhanced {
  margin-bottom: 8px;
  transition: background 0.2s ease;
}

.checkbox-item:hover,
.checkbox-item.enhanced:hover {
  background: var(--hover-bg);
  border-radius: 4px;
}

.checkbox-container,
.checkbox-container.enhanced {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  font-size: 0.95rem;
  color: var(--sidebar-text);
  font-weight: 500;
  user-select: none;
  line-height: 1.5;
}

.checkbox-container input,
.checkbox-container.enhanced input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark,
.checkmark.enhanced {
  position: absolute;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: var(--transition);
}

.checkbox-container:hover input ~ .checkmark,
.checkbox-container.enhanced:hover input ~ .checkmark.enhanced {
  border-color: var(--primary-color);
}

.checkbox-container input:checked ~ .checkmark,
.checkbox-container.enhanced input:checked ~ .checkmark.enhanced {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.checkmark:after,
.checkmark.enhanced:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-container input:checked ~ .checkmark:after,
.checkbox-container.enhanced input:checked ~ .checkmark.enhanced:after {
  display: block;
}

.checkmark:after,
.checkmark.enhanced:after {
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid #ffffff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* 搜索框 */
.search-filter {
  margin-bottom: 10px;
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--sidebar-border);
  border-radius: var(--sidebar-radius);
  font-size: 0.9rem;
  color: var(--sidebar-text);
  background: rgba(255, 255, 255, 0.6);
  box-sizing: border-box;
  transition: var(--transition);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 按钮链接 */
.btn-link {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0;
  text-decoration: none;
  transition: var(--transition);
  position: relative;
}

.btn-link:after {
  content: '';
  position: absolute;
  width: 0;
  height: 1px;
  bottom: -2px;
  left: 0;
  background-color: var(--primary-color);
  transition: width 0.25s ease;
}

.btn-link:hover {
  color: #2980b9;
}

.btn-link:hover:after {
  width: 100%;
}

.show-more-btn {
  background: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--sidebar-border);
  border-radius: var(--sidebar-radius);
  color: var(--sidebar-text);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 0;
  width: 100%;
  text-align: center;
  transition: var(--transition);
  margin-top: 8px;
}

.show-more-btn:hover {
  background: var(--hover-bg);
  border-color: rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

/* ============== 结果区 ============== */
.results-section {
  margin-top: 0; /* 移除顶部边距 */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--sidebar-text);
  border-left: 2px solid var(--primary-color);
  padding-left: 10px;
  margin: 0;
}

.btn-sort {
  background: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--sidebar-border);
  border-radius: var(--sidebar-radius);
  padding: 6px 12px;
  font-size: 0.85rem;
  color: var(--sidebar-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: var(--transition);
}

.btn-sort:hover {
  background: var(--hover-bg);
  border-color: rgba(0, 0, 0, 0.12);
}

.sort-icon {
  margin-right: 5px;
  font-style: normal;
  display: inline-block;
  transition: transform 0.2s ease;
}

/* 列表 - 简洁清爱设计 */
.park-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.park-item {
  margin-bottom: 8px;
}

.park-button {
  padding: 10px 12px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;
  text-align: left;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  transition: var(--transition);
  cursor: pointer;
}

.park-button:hover {
  background-color: #f8f9fa;
  border-color: rgba(52, 152, 219, 0.3);
}

.park-button.selected {
  background-color: var(--active-bg);
  border-color: var(--primary-color);
  border-left: 3px solid var(--primary-color);
}

.park-name {
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--sidebar-text);
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.park-details {
  font-size: 0.8rem;
  color: #7f8c8d;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.park-cost {
  color: var(--primary-color);
  font-weight: 500;
}

.no-results {
  color: #999;
  font-size: 0.9rem;
  padding: 10px;
  text-align: center;
  font-style: italic;
  background: rgba(0, 0, 0, 0.02);
  border-radius: var(--sidebar-radius);
  margin-top: 10px;
}

/* ============== 地图容器 ============== */
.map-container {
  flex: 1;
  position: relative;
}

/* ============== 地图样式切换按钮 ============== */
.map-style-toggle {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 999;
}

.style-toggle-btn,
.display-mode-toggle-btn {
  background: white;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 6px 10px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* ============== Popup 内查看详情按钮 ============== */
.popup-button {
  background: var(--primary-color);
  border: none;
  border-radius: 4px;
  color: #fff;
  font-size: 0.85rem;
  font-weight: 600;
  padding: 6px 10px;
  cursor: pointer;
  transition: var(--transition);
}

.popup-button:hover {
  background: #2980b9;
}

/* ============== 发光Marker样式 ============== */
.animated-marker {
  position: relative;
  width: 20px;
  height: 20px;
}

.animated-marker.blue .marker-outer-glow {
  background: rgba(52, 152, 219, 0.3);
}

.animated-marker.red .marker-outer-glow {
  background: rgba(231, 76, 60, 0.3);
}

.marker-outer-glow,
.marker-inner,
.marker-pulse {
  position: absolute;
  border-radius: 50%;
  top: 0;
  left: 0;
}

.marker-outer-glow {
  width: 20px;
  height: 20px;
  animation: outer-glow 2s infinite;
}

.marker-inner {
  width: 14px;
  height: 14px;
  top: 3px;
  left: 3px;
  background: #fff;
  z-index: 1;
}

.marker-pulse {
  width: 20px;
  height: 20px;
  animation: pulse 2s infinite;
  opacity: 0.7;
  border: 2px solid #fff;
}

@keyframes outer-glow {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.7);
    opacity: 0.8;
  }
  70% {
    transform: scale(1.3);
    opacity: 0;
  }
  100% {
    transform: scale(0.7);
    opacity: 0;
  }
}

/* 地图夜间模式样式 - 只在地图样式为night时生效 */
.map-night-mode {
  --sidebar-bg: #1a1a1a;
  --sidebar-text: #f0f0f0;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
  --hover-bg: rgba(52, 152, 219, 0.15);
  --active-bg: rgba(52, 152, 219, 0.25);
}

/* 地图夜间模式下的国家选择器 */
.map-night-mode .country-select-dropdown {
  background-color: #2d3748;
  color: #f0f0f0;
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f0f0f0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
}

.map-night-mode .country-select-dropdown:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.map-night-mode .country-select-dropdown:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

/* ============== 加载状态样式 ============== */
.loading-section {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-progress {
  width: 100%;
  max-width: 250px;
}

.progress-bar {
  width: 100%;
  height: 3px;
  background-color: rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 6px;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 11px;
  color: #888;
  text-align: center;
  font-weight: 400;
}

.quick-data-notice {
  background: rgba(52, 152, 219, 0.05);
  border: 1px solid rgba(52, 152, 219, 0.15);
  border-radius: 4px;
  padding: 6px 10px;
  margin-bottom: 10px;
  font-size: 11px;
  color: #3498db;
  text-align: center;
}

.quick-data-notice span {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* 夜间模式下的加载状态 */
.map-night-mode .progress-bar {
  background-color: rgba(255, 255, 255, 0.08);
}

.map-night-mode .progress-text {
  color: #aaa;
}

.map-night-mode .quick-data-notice {
  background: rgba(52, 152, 219, 0.1);
  border-color: rgba(52, 152, 219, 0.2);
  color: #5dade2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-sidebar {
    width: 100%;
    max-width: none;
    resize: none;
  }

  .map-container {
    display: none; /* 在小屏幕上隐藏地图，只显示侧边栏 */
  }
}
