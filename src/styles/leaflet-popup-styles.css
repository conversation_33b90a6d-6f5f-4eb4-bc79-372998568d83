/* Override default Leaflet popup styles */
.leaflet-popup-content-wrapper {
  /* Remove default padding and background/shadow - controlled by our content */
  padding: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 16px !important; /* Match our content's border radius */
  overflow: hidden; /* Ensure content clipping matches border radius */
  z-index: 2500 !important; /* 确保弹窗悬浮在其他坐标的上层 */
}

.leaflet-popup-content {
  /* Remove default margin */
  margin: 0 !important;
  width: auto !important; /* Let content define width */
  min-width: 320px !important; /* Ensure minimum width from our styles */
  max-width: 380px !important; /* Ensure maximum width */
  pointer-events: auto !important; /* 确保内容可以点击 */
  user-select: none !important;
  z-index: 2500 !important; /* 确保弹窗内容悬浮在其他坐标的上层 */
}

/* Remove default tip (arrow) - our component has its own */
.leaflet-popup-tip-container {
  display: none !important;
}

/* Ensure popup pane has proper z-index */
.leaflet-popup-pane {
  z-index: 2500 !important; /* 确保弹窗面板悬浮在其他坐标的上层 */
}

/* Style the close button to ensure it's clickable */
.leaflet-popup-close-button {
  /* Position it similarly to our design */
  top: 10px !important;
  right: 10px !important;
  width: 30px !important;
  height: 30px !important;
  font-size: 24px !important; /* Adjust size */
  color: #777 !important;
  background: rgba(248, 248, 248, 0.8) !important; /* Match header background */
  border-radius: 50% !important;
  transition: all 0.2s ease !important;
  text-decoration: none !important;
  line-height: 30px !important; /* Center the 'x' */
  text-align: center;
  font-weight: 300 !important;
  z-index: 2600 !important; /* Ensure close button is on top of all other elements */
  pointer-events: auto !important; /* Ensure clicks are captured */
  cursor: pointer !important; /* Show pointer cursor */
  display: block !important; /* Ensure it's visible */
  position: absolute !important; /* Ensure proper positioning */
}

.leaflet-popup-close-button:hover {
  color: #ff3b30 !important;
  background-color: rgba(0, 0, 0, 0.05) !important;
  transform: scale(1.1) rotate(90deg) !important; /* Add rotation for better visual feedback */
}

/* Fix for any elements that might overlap the close button */
.leaflet-popup-content-wrapper * {
  pointer-events: auto !important;
}

/* Ensure the close button is not covered by other elements */
.leaflet-popup-close-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

/* Apply styles specifically to our migration popup via the added className */
.migration-leaflet-popup .leaflet-popup-content-wrapper {
  border-radius: 10px !important;
}

.migration-leaflet-popup .leaflet-popup-content {
  width: 320px !important;
}

/* Override nationwide styles for popup context */
.migration-leaflet-popup .nationwide-migration-summary {
  position: static !important;
  top: auto !important;
  left: auto !important;
  animation: none !important;
  width: 100% !important;
  max-height: none !important;
  box-shadow: none !important;
  border: none !important;
  pointer-events: auto !important; /* 确保内容可以点击 */
}

/* Ensure chart container has proper dimensions */
.migration-leaflet-popup .nationwide-pie-chart-wrapper {
  width: 180px !important;
  height: 180px !important;
}

/* Ensure toggle buttons work properly */
.migration-leaflet-popup .toggle-chart-btn {
  cursor: pointer !important;
  pointer-events: auto !important;
}