/* USStatesLayer.css */

/* 默认状态下的州边界样式 */
.us-state-boundary {
  fill: transparent;
  stroke: #666;
  stroke-width: 1;
  stroke-opacity: 0.5;
  transition: all 0.3s ease;
  pointer-events: auto !important; /* 确保即使在标记下方也能接收鼠标事件 */
}

/* 鼠标悬停时的州边界样式 */
.us-state-boundary:hover,
.us-state-boundary.highlighted {
  fill: rgba(51, 136, 255, 0.3);
  stroke: #333;
  stroke-width: 2;
  stroke-opacity: 0.8;
  cursor: pointer;
}

/* 夜间模式下的州边界样式 */
.dark-mode .us-state-boundary {
  stroke: #999;
}

.dark-mode .us-state-boundary:hover,
.dark-mode .us-state-boundary.highlighted {
  fill: rgba(51, 136, 255, 0.4);
  stroke: #ccc;
}

/* 简洁的州名称工具提示样式 */
.compact-state-tooltip {
  background-color: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  z-index: 1000 !important;
  white-space: nowrap !important;
  pointer-events: none !important; /* 确保工具提示不会阻止鼠标事件 */
}

/* 夜间模式下的简洁工具提示样式 */
.compact-state-tooltip.dark-mode {
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2) !important;
}

/* 确保工具提示容器也不会阻止鼠标事件 */
.leaflet-tooltip.compact-state-tooltip {
  pointer-events: none !important;
}

/* 防止工具提示变成持久的 */
.leaflet-tooltip-pane .leaflet-tooltip.compact-state-tooltip {
  pointer-events: none !important;
  transition: opacity 0.2s ease !important;
}

/* 确保GeoJSON州边界层能够接收鼠标事件 */
.geojson-states-pane {
  z-index: 200 !important;
  pointer-events: auto !important;
}

/* 确保州边界路径能够接收鼠标事件 */
.us-state-path {
  pointer-events: auto !important;
  cursor: pointer;
}

/* 确保州边界层在标记下方但仍能接收鼠标事件 */
.leaflet-overlay-pane path {
  pointer-events: auto !important;
}


