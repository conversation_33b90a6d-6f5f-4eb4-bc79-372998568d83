/* 地图按钮样式 - 现代化设计 */
:root {
  --primary-color: #1890ff;
  --primary-hover: #40a9ff;
  --primary-active: #096dd9;
  --light-bg: rgba(255, 255, 255, 0.95);
  --dark-bg: rgba(30, 35, 45, 0.95);
  --light-border: rgba(0, 0, 0, 0.1);
  --dark-border: rgba(255, 255, 255, 0.15);
  --light-text: #333;
  --dark-text: #f9fafb;
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-full: 50%;
  --tooltip-bg-light: rgba(40, 44, 52, 0.85);
  --tooltip-bg-dark: rgba(15, 20, 30, 0.9);
  --tooltip-border-light: #1890ff;
  --tooltip-border-dark: #38bdf8;
}

/* 地图控制按钮容器 */
.map-buttons-container {
  position: absolute;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 左侧垂直工具栏 - 移动到左上角 */
.map-buttons-left {
  top: 15px;
  left: 15px;
  align-items: center;
}

/* 右侧按钮组容器 */
.map-buttons-right {
  position: fixed;  /* 使用 fixed 定位确保位置固定 */
  top: 20px;      /* 距离顶部距离 */
  right: 20px;    /* 距离右侧距离 */
  display: flex;
  flex-direction: column;
  gap: 12px;      /* 按钮间距 */
  z-index: 1000;  /* 确保显示在地图上方 */
  pointer-events: auto;  /* 确保按钮可以点击 */
}

/* 确保按钮在暗色模式下可见 */
.map-buttons-right .map-button {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.map-buttons-right .map-button:hover {
  background-color: #f5f5f5;
}

.map-buttons-right .map-button.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

/* 暗色模式适配 */
.dark-mode .map-buttons-right .map-button {
  background-color: #1f1f1f;
  border-color: #333;
  color: #f0f0f0;
}

.dark-mode .map-buttons-right .map-button:hover {
  background-color: #2a2a2a;
}

.dark-mode .map-buttons-right .map-button.active {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* 顶部工具栏 */
.map-buttons-top {
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  flex-direction: row;
  gap: 12px;
  z-index: 1000;
}

/* 底部工具栏 - 已移除 */

/* 基础按钮样式 */
.map-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--light-bg);
  border: 1px solid var(--light-border);
  border-radius: var(--radius-md);
  color: var(--light-text);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  padding: 0;
  margin: 0;
  overflow: hidden;
  position: relative;
  /* 硬件加速 */
  transform: translateZ(0);
  will-change: transform, box-shadow;
  /* 事件隔离 */
  isolation: isolate;
  touch-action: manipulation;
  user-select: none;
}

/* 圆形按钮 */
.map-button.circle {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
}

/* 顶部工具栏按钮特殊样式 */
.map-buttons-top .map-button.circle {
  background-color: rgba(255, 255, 255, 0.85);
  border: 1px solid var(--light-border); /* Explicitly set light mode border */
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease; /* Keep specific transition */
}

.map-buttons-top .map-button.circle:hover {
  background-color: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.map-buttons-top .map-button.circle:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 椭圆形按钮 */
.map-button.pill {
  border-radius: 20px;
  padding: 8px 16px;
  min-width: 40px;
  height: 40px;
}

/* 方形按钮 */
.map-button.square {
  width: 40px;
  height: 40px;
}

/* 按钮悬停效果 */
.map-button:hover, .map-button.hover-enabled:hover {
  background-color: #f8f9fa;
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* 确保经济热点按钮的悬停效果 */
.map-button.hover-enabled:hover .map-button-icon {
  color: var(--primary-color);
  transform: scale(1.1);
  transition: all 0.2s ease;
}

/* 按钮激活状态 */
.map-button.active {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: var(--primary-color);
}

/* 按钮图标 */
.map-button-icon {
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 带文本的按钮 */
.map-button.with-text {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  min-width: 100px;
  position: relative;
}

.map-button-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', '微软雅黑', 'PingFang SC', '苹方', 'Heiti SC', '黑体-简', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  /* Ensure proper text rendering */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 带徽章的按钮 - 红点设计 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* 确保红点正确定位在圆角按钮上 */
.map-button.pill .map-button-badge {
  top: 0px;
  right: 10px;
}

.map-button-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #ff4d4f;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite ease-in-out;
  z-index: 10;
}

/* 弹出菜单容器 */
.map-popover {
  position: relative;
}

/* 默认弹出菜单（左侧按钮，菜单出现在右侧） */
.map-popover-content {
  /* 位置现在由JavaScript动态计算 */
  background-color: var(--light-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--light-border);
  padding: 12px;
  min-width: 180px;
  max-width: 260px;
  z-index: 1150;
  transition: background var(--transition-fast), border-color var(--transition-fast), box-shadow var(--transition-fast);
  /* 防止被父容器截断 */
  overflow: visible;
  will-change: transform, opacity;
}

/* 左侧按钮的菜单箭头（指向左侧） */
.map-popover-content::before {
  content: '';
  position: absolute;
  top: 12px;
  left: -10px;
  border-width: 8px;
  border-style: solid;
  border-color: transparent var(--light-border) transparent transparent;
}

.map-popover-content::after {
  content: '';
  position: absolute;
  top: 13px;
  left: -8px;
  border-width: 7px;
  border-style: solid;
  border-color: transparent var(--light-bg) transparent transparent;
}

/* 右侧弹出菜单 - 位置现在由JavaScript动态计算 */
.map-popover.right .map-popover-content {
  /* 位置由JavaScript动态设置 */
}

.map-popover.right .map-popover-content::before {
  left: auto;
  right: -10px;
  border-color: transparent transparent transparent var(--light-border);
}

.map-popover.right .map-popover-content::after {
  left: auto;
  right: -8px;
  border-color: transparent transparent transparent var(--light-bg);
}

/* 底部弹出菜单 */
.map-popover.bottom .map-popover-content {
  top: auto;
  bottom: 50px;
  left: 0;
}

/* 弹出菜单标题 */
.map-popover-header {
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 600;
  color: var(--light-text);
  border-bottom: 1px solid var(--light-border);
  margin-bottom: 4px;
}

/* 弹出菜单项 */
.map-popover-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
  color: var(--light-text);
  font-size: 14px;
  width: 100%;
  border: none;
  background: none;
  text-align: left;
}

.map-popover-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.map-popover-item.active {
  background-color: #e6f7ff;
  color: var(--primary-color);
}

/* 设置弹窗中的分节标题 */
.map-popover-section {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.map-popover-section-title {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-bottom: 4px;
  padding: 0 12px;
}

/* 设置弹窗中的可展开项 */
.settings-expandable {
  overflow: hidden;
}

.popover-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.expand-icon {
  font-size: 10px;
  color: #999;
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.settings-expanded-content {
  overflow: hidden;
  padding-left: 12px;
}

.map-popover-subitem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
  color: var(--light-text);
  font-size: 13px;
  width: 100%;
  border: none;
  background: none;
  text-align: left;
  margin-top: 2px;
}

.map-popover-subitem:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.map-popover-subitem.active {
  background-color: #e6f7ff;
  color: var(--primary-color);
}

/* 过滤器面板 */
.map-filter-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--light-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: 1160;
  width: 320px;
  max-height: 80vh;
  overflow: auto; /* 允许内容滚动，但子元素可以溢出 */
  border: 1px solid var(--light-border);
  pointer-events: auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', '微软雅黑', 'PingFang SC', '苹方', 'Heiti SC', '黑体-简', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: flex;
  flex-direction: column;
  will-change: transform, opacity;
  /* 确保下拉菜单可以溢出面板边界 */
  contain: layout style;
}

/* 面板箭头 - 指向按钮 */
.map-filter-panel-arrow {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: var(--light-bg);
  border-top: 1px solid var(--light-border);
  border-right: 1px solid var(--light-border);
  transform: rotate(-45deg);
  top: -6px;
  right: 25px;
  z-index: 1051;
}

/* 搜索面板箭头 - 指向搜索按钮 */
.map-search-panel-arrow {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: var(--light-bg);
  border-top: 1px solid var(--light-border);
  border-right: 1px solid var(--light-border);
  transform: rotate(-45deg);
  top: -6px;
  right: 25px;
  z-index: 1051;
}

.map-filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid var(--light-border);
  flex-shrink: 0; /* Prevent header from shrinking */
}

.map-filter-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--light-text);
}

.map-filter-content {
  padding: 10px 12px;
  overflow-y: auto;
  max-height: calc(80vh - 60px); /* Adjust based on header height */
  flex: 0 1 auto; /* Changed from flex: 1 to prevent excessive growth */
}

/* Multi-select list */
.multi-select-list {
  max-height: 120px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid var(--light-border);
  border-radius: 6px;
  padding: 3px;
  margin-top: 5px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.multi-select-list::-webkit-scrollbar {
  width: 4px;
}

.multi-select-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}

/* Filter checkbox label */
.filter-checkbox-label {
  display: flex;
  align-items: center;
  padding: 3px 4px;
  margin-bottom: 1px;
  font-size: 12px;
  color: var(--light-text);
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.filter-checkbox-label:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.filter-checkbox-label input {
  position: absolute;
  opacity: 0;
}

.custom-checkbox {
  display: flex;
  margin-right: 8px;
  font-size: 16px;
  color: #aaa;
}

.filter-checkbox-label input:checked + .custom-checkbox {
  color: var(--primary-color);
}

.map-filter-section {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--light-border);
}

.map-filter-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.filter-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.filter-actions {
  display: flex;
  gap: 6px;
}

.filter-actions button {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 12px;
  cursor: pointer;
  padding: 0;
}

.filter-actions button:hover {
  text-decoration: underline;
}

/* Filter input */
.filter-input, .filter-select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid var(--light-border);
  border-radius: 6px;
  font-size: 13px;
  color: #333333 !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.filter-input:focus, .filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: rgba(255, 255, 255, 1) !important;
  color: #222222 !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.filter-input::placeholder {
  color: rgba(0, 0, 0, 0.5) !important;
}

.lease-cost-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.lease-cost-inputs input {
  flex: 1;
  color: #333333 !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
}

.lease-cost-inputs input:focus {
  color: #222222 !important;
  background-color: rgba(255, 255, 255, 1) !important;
}

.lease-cost-inputs input::placeholder {
  color: rgba(0, 0, 0, 0.5) !important;
}

.no-results-text {
  padding: 8px;
  color: #999;
  font-style: italic;
  text-align: center;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-filter-label {
  display: block;
  font-size: 13px;
  font-weight: 600;
  color: var(--light-text);
  margin-bottom: 4px;
}

/* Expand/collapse button */
.expand-collapse-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 100%;
  padding: 4px 0;
  margin-top: 4px;
  background-color: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--light-border);
  border-radius: 6px;
  color: var(--light-text);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.expand-collapse-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.12);
}

.expand-collapse-button span {
  display: inline-block;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .map-buttons-left {
    top: 10px;
  }

  .map-filter-panel {
    width: 280px;
    max-height: 70vh;
  }

  /* 调整面板箭头位置 */
  .map-filter-panel-arrow,
  .map-search-panel-arrow {
    right: 20px;
  }

  /* Bottom toolbar removed */

  .map-buttons-top {
    top: 10px;
  }
}

@media (max-width: 480px) {
  .map-buttons-left {
    top: 8px;
  }

  .map-button.with-text {
    min-width: auto;
    padding: 8px 12px;
  }

  .map-button.pill .map-button-badge {
    top: 0px;
    right: 8px;
  }

  .map-button-badge {
    width: 6px;
    height: 6px;
    top: -3px;
    right: -3px;
    border-width: 1.5px;
  }

  .map-filter-panel {
    width: 260px;
    max-height: 60vh;
  }

  /* 调整面板箭头位置 */
  .map-filter-panel-arrow,
  .map-search-panel-arrow {
    right: 15px;
    width: 10px;
    height: 10px;
    top: -5px;
  }

  /* Bottom toolbar removed */

  .map-buttons-top {
    gap: 8px;
    top: 8px;
  }

  .map-button.circle,
  .map-button.square {
    width: 36px;
    height: 36px;
  }
}

/* 暗色模式 */
.dark-mode .map-button {
  background-color: var(--dark-bg);
  border: 1px solid var(--dark-border); /* Ensure full border property is set */
  color: var(--dark-text);
}

/* 顶部工具栏按钮暗色模式 - 明确设置所有样式属性 */
body .dark-mode .map-buttons-top .map-button.circle {
  background-color: rgba(20, 25, 35, 0.98) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: var(--dark-text) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
}

/* Dark hover state for top buttons - Inherit background, keep specific shadow/transform */
.dark-mode .map-buttons-top .map-button.circle:hover {
  /* Inherits background-color from .dark-mode .map-button:hover */
  /* Inherits border-color from .dark-mode .map-button */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.45); /* Specific hover shadow */
  transform: translateY(-2px); /* Ensure transform is applied */
}

/* Dark active (pressed) state for top buttons - Keep specific shadow/transform */
.dark-mode .map-buttons-top .map-button.circle:active {
  /* Use default active background/border or adjust if needed */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35); /* Specific active shadow */
  transform: translateY(0); /* Ensure transform is reset */
}

/* Keep the .active class style consistent - Explicitly set */
.dark-mode .map-buttons-top .map-button.circle.active {
   background-color: rgba(24, 144, 255, 0.2);
   border-color: rgba(24, 144, 255, 0.4);
   color: #69c0ff;
}

/* Icon color - Explicitly set */
.dark-mode .map-buttons-top .map-button.circle .map-button-icon {
  color: var(--dark-text);
}

.dark-mode .map-button-badge {
  background-color: #ff4d4f;
  border-color: var(--dark-bg);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.dark-mode .map-button:hover, .dark-mode .map-button.hover-enabled:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .map-button.hover-enabled:hover .map-button-icon {
  color: var(--primary-hover);
  transform: scale(1.1);
}

.dark-mode .map-button.active {
  background-color: rgba(24, 144, 255, 0.2);
  border-color: rgba(24, 144, 255, 0.4);
  color: #69c0ff;
}

.dark-mode .map-filter-panel {
  background-color: rgba(15, 20, 30, 0.95);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
}

/* 暗色模式下的面板箭头 */
.dark-mode .map-filter-panel-arrow,
.dark-mode .map-search-panel-arrow {
  background-color: rgba(15, 20, 30, 0.95);
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  border-right: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 2px -2px 6px rgba(0, 0, 0, 0.2);
}

.dark-mode .map-popover-content,
.dark-mode .map-buttons-top {
  background-color: var(--dark-bg);
  border-color: var(--dark-border);
}

.dark-mode .map-filter-header {
  border-color: rgba(255, 255, 255, 0.15);
  background-color: rgba(10, 15, 25, 0.5);
  border-top-left-radius: var(--radius-lg);
  border-top-right-radius: var(--radius-lg);
}

.dark-mode .map-filter-content {
  padding: 12px 15px;
  overflow-y: auto;
  scrollbar-width: thin;
}

.dark-mode .map-filter-content::-webkit-scrollbar {
  width: 5px;
}

.dark-mode .map-filter-content::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
}

.dark-mode .map-filter-content::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}

.dark-mode .map-filter-section {
  border-color: rgba(255, 255, 255, 0.1);
  margin-bottom: 15px;
  padding-bottom: 15px;
}

.dark-mode .map-filter-header h3 {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.dark-mode .map-filter-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-bottom: 6px;
}

.dark-mode .multi-select-list {
  background-color: rgba(10, 15, 25, 0.8);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  padding: 4px;
  margin-top: 6px;
}

.dark-mode .multi-select-list::-webkit-scrollbar {
  width: 5px;
}

.dark-mode .multi-select-list::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.25);
  border-radius: 20px;
}

.dark-mode .multi-select-list::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
}

.dark-mode .filter-checkbox-label {
  color: rgba(255, 255, 255, 0.85);
  padding: 4px 6px;
  border-radius: 4px;
  margin-bottom: 2px;
}

.dark-mode .filter-checkbox-label:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .custom-checkbox {
  color: rgba(255, 255, 255, 0.4);
  margin-right: 10px;
}

.dark-mode .filter-checkbox-label input:checked + .custom-checkbox {
  color: #38bdf8;
  text-shadow: 0 0 5px rgba(56, 189, 248, 0.3);
}

.dark-mode .filter-section-header {
  margin-bottom: 8px;
}

.dark-mode .filter-actions {
  margin-top: 2px;
}

.dark-mode .filter-actions button {
  color: #38bdf8;
  opacity: 0.9;
  font-weight: 500;
  padding: 2px 4px;
  border-radius: 3px;
}

.dark-mode .filter-actions button:hover {
  opacity: 1;
  background-color: rgba(56, 189, 248, 0.1);
  text-decoration: none;
}

/* 只在真正的暗色模式下应用深色输入框样式 */
body.dark-mode .filter-input,
body.dark-mode .filter-select,
html.dark-mode .filter-input,
html.dark-mode .filter-select {
  background-color: rgba(10, 15, 25, 0.8) !important;
  color: rgba(255, 255, 255, 0.95) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 13px;
}

body.dark-mode .filter-input::placeholder,
html.dark-mode .filter-input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

body.dark-mode .filter-input:focus,
body.dark-mode .filter-select:focus,
html.dark-mode .filter-input:focus,
html.dark-mode .filter-select:focus {
  border-color: rgba(56, 189, 248, 0.7) !important;
  background-color: rgba(10, 15, 25, 0.9) !important;
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.25), inset 0 1px 3px rgba(0, 0, 0, 0.3);
  outline: none;
}

body.dark-mode .lease-cost-inputs,
html.dark-mode .lease-cost-inputs {
  gap: 10px;
  margin-top: 6px;
}

body.dark-mode .lease-cost-inputs input,
html.dark-mode .lease-cost-inputs input {
  background-color: rgba(10, 15, 25, 0.8) !important;
  color: rgba(255, 255, 255, 0.95) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

body.dark-mode .lease-cost-inputs input:focus,
html.dark-mode .lease-cost-inputs input:focus {
  background-color: rgba(10, 15, 25, 0.9) !important;
  color: rgba(255, 255, 255, 1) !important;
}

body.dark-mode .lease-cost-inputs input::placeholder,
html.dark-mode .lease-cost-inputs input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

.dark-mode .expand-collapse-button {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 6px 0;
  margin-top: 8px;
  font-weight: 500;
}

.dark-mode .expand-collapse-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.dark-mode .no-results-text {
  color: #a0aec0;
}

/* 搜索面板 */
.map-search-panel {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 320px;
  max-height: 70vh;
  overflow-y: auto;
  z-index: 1160; /* 提高z-index避免被截断 */
  background-color: var(--light-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--light-border);
  pointer-events: auto; /* 确保可以接收点击事件 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', '微软雅黑', 'PingFang SC', '苹方', 'Heiti SC', '黑体-简', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  /* 确保下拉菜单可以溢出面板边界 */
  contain: layout style;
}

.search-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.search-input {
  flex: 1;
}

.search-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.search-button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.search-button:active {
  transform: translateY(0);
  background-color: var(--primary-active);
}

.search-results {
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid var(--light-border);
  border-radius: 6px;
  padding: 8px;
  margin-top: 5px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-results-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-result-item {
  padding: 8px 10px;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.02);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  margin-bottom: 6px;
}

.search-result-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: var(--light-border);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-result-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--light-text);
  margin-bottom: 2px;
  text-align: center;
}

.search-result-location {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  text-align: center;
}

.search-result-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-top: 4px;
  justify-content: center;
}

/* 匹配指示器基础样式 */
.match-indicator {
  font-size: 10px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  white-space: nowrap;
  display: inline-block;
  margin: 0 3px;
}

/* 州匹配指示器 */
.state-match-indicator {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  border: 1px solid rgba(24, 144, 255, 0.2);
}

/* 名称匹配指示器 */
.name-match-indicator {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
  border: 1px solid rgba(245, 34, 45, 0.2);
}

/* 设置面板样式 */
.settings-option {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.settings-option .map-popover-item {
  margin-bottom: 2px;
  border-radius: var(--radius-sm);
}

/* 暗色模式下的搜索样式 */
.dark-mode .search-button {
  background-color: #38bdf8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark-mode .search-button:hover {
  background-color: #0ea5e9;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
}

.dark-mode .search-results {
  background-color: rgba(10, 15, 25, 0.8);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.4);
}

.dark-mode .search-result-item {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .search-result-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.dark-mode .search-result-name {
  color: rgba(255, 255, 255, 0.95);
}

.dark-mode .search-result-location {
  color: rgba(255, 255, 255, 0.7);
}

.dark-mode .state-match-indicator {
  background-color: rgba(56, 189, 248, 0.15);
  color: #38bdf8;
  border-color: rgba(56, 189, 248, 0.3);
}

.dark-mode .name-match-indicator {
  background-color: rgba(255, 77, 79, 0.15);
  color: #ff4d4f;
  border-color: rgba(255, 77, 79, 0.3);
}

.dark-mode .map-popover-item {
  color: var(--dark-text);
}

.dark-mode .map-popover-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .map-popover-item.active {
  background-color: rgba(24, 144, 255, 0.2);
  color: #69c0ff;
}

/* 暗色模式下的设置弹窗分节标题 */
.dark-mode .map-popover-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-mode .map-popover-section-title {
  color: rgba(255, 255, 255, 0.7);
}

/* 暗色模式下的设置弹窗可展开项 */
.dark-mode .expand-icon {
  color: rgba(255, 255, 255, 0.5);
}

.dark-mode .map-popover-subitem {
  color: var(--dark-text);
}

.dark-mode .map-popover-subitem:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.dark-mode .map-popover-subitem.active {
  background-color: rgba(24, 144, 255, 0.2);
  color: #69c0ff;
}

/* Dark mode popover styling */
.map-popover-content.dark-mode {
  background-color: var(--dark-bg);
  border: 1px solid var(--dark-border);
  color: var(--dark-text);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.6);
}

.map-popover-content.dark-mode .map-popover-header {
  color: var(--dark-text);
  border-bottom: 1px solid var(--dark-border);
}

.map-popover-content.dark-mode .map-popover-item {
  color: var(--dark-text);
}

.map-popover-content.dark-mode .map-popover-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.map-popover-content.dark-mode .map-popover-item.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--primary-hover);
}

.map-popover-content.dark-mode::before {
  border-color: transparent var(--dark-border) transparent transparent;
}

.map-popover-content.dark-mode::after {
  border-color: transparent var(--dark-bg) transparent transparent;
}

/* Close button in dark mode */
.dark-mode .map-filter-header button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  transition: all 0.2s ease;
}

/* 新的顶部控制按钮样式 */
.map-top-controls {
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  gap: 12px;
  z-index: 1000;
}

/* 右侧控制按钮容器 */
.map-right-controls {
  position: absolute;
  top: 120px;
  right: 15px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 1000;
}

.map-top-button, .map-right-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  padding: 0;
}

.map-top-button:hover {
  background-color: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.map-top-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 暗色模式下的顶部按钮 */
.map-top-button.dark-mode {
  background-color: rgba(30, 35, 45, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #f0f0f0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.map-top-button.dark-mode:hover {
  background-color: rgba(40, 45, 55, 0.95);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
}

.map-top-button.dark-mode:active {
  background-color: rgba(25, 30, 40, 0.95);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .map-top-controls {
    top: 10px;
  }
}

@media (max-width: 480px) {
  .map-top-controls {
    gap: 8px;
    top: 8px;
  }

  .map-top-button {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
}

.dark-mode .map-filter-header button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

/* 按钮悬停提示 */
.map-button-tooltip {
  position: absolute;
  left: 50px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(40, 44, 52, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
  z-index: 1200;
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

/* 提示框小三角形 */
.map-button-tooltip:before {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 6px 6px 6px 0;
  border-style: solid;
  border-color: transparent rgba(40, 44, 52, 0.9) transparent transparent;
}

/* 显示提示框 - 仅在悬停且菜单关闭时 */
.map-popover:hover .map-button-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 暗色模式下的提示框 */
.dark-mode .map-button-tooltip {
  background-color: rgba(15, 20, 30, 0.95);
}

.dark-mode .map-button-tooltip:before {
  border-color: transparent rgba(15, 20, 30, 0.95) transparent transparent;
}

/* All Clear button in dark mode */
.dark-mode .map-filter-panel .all-clear-button {
  color: #38bdf8;
  background-color: rgba(56, 189, 248, 0.05);
  border: 1px solid rgba(56, 189, 248, 0.2);
  padding: 4px 10px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  float: right;
  margin-top: -2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark-mode .map-filter-panel .all-clear-button:hover {
  background-color: rgba(56, 189, 248, 0.1);
  border-color: rgba(56, 189, 248, 0.3);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transform: translateY(-1px);
  text-decoration: none;
}