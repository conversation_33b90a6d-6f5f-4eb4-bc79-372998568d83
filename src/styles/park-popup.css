/* 工业园区弹出窗口样式 */
.park-popup-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  width: 100%;
  min-width: 280px;
  max-width: 320px;
  font-family: '<PERSON><PERSON>', 'Se<PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
}

.park-popup-header {
  background: #f8f9fa;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.park-popup-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  padding-right: 20px; /* 为关闭按钮留出空间 */
}

.park-popup-content {
  padding: 16px 20px;
}

.park-popup-location {
  margin: 0 0 16px 0;
  font-size: 0.9rem;
  color: #666;
  display: flex;
  align-items: center;
}

.park-popup-location::before {
  content: '📍';
  margin-right: 6px;
  font-size: 1rem;
}

/* 关键数据区域样式 */
.park-popup-key-data {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.key-data-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.85rem;
  line-height: 1.4;
}

.key-data-row:last-child {
  margin-bottom: 0;
}

.key-data-label {
  color: #666;
  font-weight: 500;
}

.key-data-value {
  color: #333;
  font-weight: 600;
}

.park-popup-button {
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.park-popup-button:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.park-popup-button:active {
  transform: translateY(0);
}

.park-popup-button::after {
  content: '→';
  margin-left: 8px;
  font-size: 1.1rem;
  transition: transform 0.2s ease;
}

.park-popup-button:hover::after {
  transform: translateX(3px);
}

/* 详情页面的园区弹窗样式 */
.park-detail-popup .leaflet-popup-content-wrapper {
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  padding: 0;
  overflow: hidden;
  z-index: 2500 !important; /* 确保地块坐标菜单悬浮在其他坐标的上层 */
}

.park-detail-popup .leaflet-popup-content {
  margin: 0;
  width: 300px !important;
  max-width: 300px;
}

.park-detail-popup .park-popup-content {
  background: white;
  padding: 15px 20px;
}

.park-detail-popup h3 {
  margin: 0 0 10px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding-bottom: 8px;
}

.park-detail-popup p {
  margin: 8px 0;
  font-size: 0.95rem;
  color: #555;
  line-height: 1.4;
}

.park-detail-popup strong {
  color: #333;
  font-weight: 600;
}

/* 确保弹窗关闭按钮可见且可点击 */
.park-detail-popup .leaflet-popup-close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  color: #666;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  line-height: 22px;
  z-index: 1000;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.park-detail-popup .leaflet-popup-close-button:hover {
  background: white;
  color: #ff3b30;
  transform: scale(1.1);
}
