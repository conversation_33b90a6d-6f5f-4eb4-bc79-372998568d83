/* Card Design for Filter Groups */
.map-filter-controls.card-design {
  background-color: #ffffff; /* Solid white background */
  box-shadow: 0 4px 12px rgba(0,0,0,0.1); /* More pronounced shadow */
  border: 1px solid rgba(0,0,0,0.05);
}

.filter-group.card-design {
  background-color: #f8f9fa; /* Slightly off-white background for groups */
  padding: 15px;
  border-radius: 6px;
  border: 1px solid rgba(0,0,0,0.08);
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  margin-bottom: 10px; /* Add margin between cards */
  border-bottom: none; /* Remove default border if using cards */
  padding-bottom: 15px; /* Ensure padding */
}

.filter-group.card-design:last-child {
  margin-bottom: 0;
}

/* Lease Cost Filter Styles */
.lease-cost-filter .map-control-heading {
  margin-bottom: 15px; /* More space below heading */
  text-align: left; /* Align heading left */
  border-bottom: 1px solid #eee; /* Separator */
  padding-bottom: 8px;
}

.lease-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.lease-input-wrapper {
  flex: 1; /* Make inputs take equal space */
  position: relative;
  display: flex;
  flex-direction: column; /* Stack label and input */
}

.lease-label {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 4px; /* Space between label and input */
  font-weight: 500;
}

.map-control-input.lease-input {
  width: 100%;
  padding: 8px 10px; /* Slightly larger padding */
  border: 1px solid #ced4da; /* Standard Bootstrap-like border */
  border-radius: 4px;
  font-size: 0.9rem;
  box-sizing: border-box;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.map-control-input.lease-input:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.lease-separator {
  color: #6c757d;
  font-weight: 500;
  margin-top: auto; /* Align with bottom of inputs if labels wrap */
  padding-bottom: 8px; /* Match input padding roughly */
}

/* General UI Improvements */
.map-control-heading {
  color: #212529; /* Darker heading */
  font-weight: 600;
}

/* Refine checkbox list appearance */
.map-multi-select {
  background-color: #fff; /* Solid background for scroll list */
  border: 1px solid #dee2e6;
}

.map-checkbox-item label {
  color: #495057;
  padding: 4px 8px; /* Add padding */
  border-radius: 3px;
  transition: background-color 0.1s ease;
}

.map-checkbox-item label:hover {
  background-color: #e9ecef; /* Hover effect */
}

/* Refine Search Input */
.map-search-input {
  border: 1px solid #ced4da;
}

.map-search-input:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Refine Buttons */
.map-control-button {
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  color: #495057;
}

.map-control-button:hover {
  background-color: #dee2e6;
}

/* Night mode styles */
.night-mode .filter-group.card-design {
  background-color: #1e293b; /* Dark background for cards in night mode */
  border-color: rgba(255, 255, 255, 0.1);
}

.night-mode .map-control-heading {
  color: #e2e8f0;
  border-bottom-color: #2d3748;
}

.night-mode .map-control-input.lease-input {
  background-color: #1a202c;
  border-color: #2d3748;
  color: #e2e8f0;
}

.night-mode .map-control-input.lease-input:focus {
  border-color: #4299e1;
  box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
}

.night-mode .lease-label,
.night-mode .lease-separator {
  color: #a0aec0;
}

.night-mode .map-multi-select {
  background-color: #1a202c;
  border-color: #2d3748;
}

.night-mode .map-checkbox-item label {
  color: #e2e8f0;
}

.night-mode .map-checkbox-item label:hover {
  background-color: #2d3748;
}

/* 只在真正的夜间模式下应用深色样式 */
body.night-mode .map-search-input,
html.night-mode .map-search-input {
  background-color: #1a202c !important;
  border-color: #2d3748 !important;
  color: #e2e8f0 !important;
}

body.night-mode .map-search-input:focus,
html.night-mode .map-search-input:focus {
  border-color: #4299e1 !important;
  background-color: #1a202c !important;
  box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
}

/* Responsive styles */
@media (max-width: 768px) {
  .lease-input-container {
    flex-direction: column;
    gap: 10px;
  }
  
  .lease-separator {
    display: none;
  }
  
  .filter-group.card-design {
    padding: 12px;
    margin-bottom: 8px;
  }
}
