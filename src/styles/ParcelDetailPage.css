/* ===== RESET & BASE STYLES ===== */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%) !important;
  background-attachment: fixed;
  min-height: 100vh;
}

/* 确保所有元素都没有默认的白色背景 */
* {
  background-color: transparent;
}

/* 特别针对可能有白色背景的元素 */
div, section, header, main, article, aside, nav, footer {
  background-color: transparent;
}

.parcel-detail-container {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  background-attachment: fixed;
}

/* 3D背景 */
.parcel-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.silk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(15, 15, 35, 0.9) 0%,
    rgba(26, 26, 46, 0.8) 50%,
    rgba(22, 33, 62, 0.9) 100%
  );
  backdrop-filter: blur(2px);
}

/* ===== ENHANCED TOP HEADER ===== */
.top-header {
  position: relative;
  z-index: 50;
  padding: 0;
  background: transparent;
  overflow: hidden;
}

.header-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(26, 26, 46, 0.98) 0%,
    rgba(22, 33, 62, 0.95) 50%,
    rgba(15, 15, 35, 0.98) 100%
  );
  backdrop-filter: blur(24px) saturate(1.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow:
    0 1px 0 0 rgba(255, 255, 255, 0.05) inset,
    0 8px 32px rgba(0, 0, 0, 0.3);
}

.header-content {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  padding: 20px 32px;
  min-height: 80px;
}

/* Header Left Section */
.header-left {
  display: flex;
  justify-content: flex-start;
}

.back-to-map-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 12px;
  padding: 12px 20px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.back-to-map-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.back-to-map-btn:hover::before {
  left: 100%;
}

.back-to-map-btn:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.back-btn-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.back-to-map-btn:hover .back-btn-icon {
  transform: translateX(-2px);
}

.back-btn-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Header Center Section */
.header-center {
  display: flex;
  justify-content: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #ffffff;
}

.title-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-icon {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(99, 102, 241, 0.2));
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.title-icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.header-title:hover .title-icon-glow {
  opacity: 1;
}

.header-title:hover .title-icon {
  transform: scale(1.05);
  border-color: rgba(59, 130, 246, 0.5);
}

.title-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.title-main {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: #ffffff;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.title-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.65);
  margin: 0;
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* Header Right Section */
.header-right {
  display: flex;
  justify-content: flex-end;
}

.language-switcher {
  position: relative;
  display: flex;
  border-radius: 12px;
  padding: 4px;
  gap: 2px;
  overflow: hidden;
}

.lang-switcher-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 12px;
  backdrop-filter: blur(12px);
}

.lang-btn {
  position: relative;
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
}

.lang-btn.active {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: #ffffff;
  box-shadow:
    0 2px 8px rgba(59, 130, 246, 0.3),
    0 1px 0 rgba(255, 255, 255, 0.2) inset;
}

.lang-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transform: translateY(-1px);
}

.lang-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Header Border Effect */
.header-border-effect {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  overflow: hidden;
}

.border-gradient {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.5) 25%,
    rgba(99, 102, 241, 0.5) 50%,
    rgba(139, 92, 246, 0.5) 75%,
    transparent 100%
  );
  opacity: 0.6;
  animation: borderFlow 3s ease-in-out infinite;
}

/* Header Animations */
@keyframes borderFlow {
  0%, 100% {
    transform: translateX(-100%);
    opacity: 0.3;
  }
  50% {
    transform: translateX(0%);
    opacity: 0.8;
  }
}

@keyframes headerFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.top-header {
  animation: headerFadeIn 0.6s ease-out;
}

/* Subtle pulse animation for the title icon */
@keyframes iconPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

.title-icon {
  animation: iconPulse 2s infinite;
}

/* Enhanced hover effects */
.back-to-map-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

.lang-btn.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 8px;
  pointer-events: none;
}

/* ===== UNIFIED HERO SECTION ===== */
.hero-section {
  position: relative;
  z-index: 10;
  padding: 40px 32px;
  overflow: hidden;
}

.hero-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center top,
    rgba(59, 130, 246, 0.08) 0%,
    rgba(99, 102, 241, 0.05) 50%,
    transparent 100%
  );
  pointer-events: none;
}

.hero-content {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
}

/* ===== UNIFIED HERO CARD ===== */
.unified-hero-card {
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.hero-card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(37, 99, 235, 0.1) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.unified-hero-card:hover .hero-card-bg {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.15) 100%);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
}

.hero-card-content {
  position: relative;
  display: flex;
  align-items: stretch;
  z-index: 2;
  min-height: 200px;
}

/* ===== LEFT SECTION - PARCEL INFO ===== */
.parcel-info-section {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 24px;
  background: transparent;
}

.parcel-header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 8px;
}

/* Parcel icon container and icon styles removed since icon is no longer used */

.parcel-main-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.parcel-title {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.parcel-apn {
  font-size: 48px;
  font-weight: 700;
  color: #60a5fa;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace;
  letter-spacing: -0.02em;
  margin: 8px 0 12px 0;
  line-height: 1;
}

.parcel-location {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 16px;
}

.parcel-location svg {
  width: 16px;
  height: 16px;
  color: #60a5fa;
}

.parcel-tags {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.tag {
  padding: 8px 16px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid transparent;
}

.class-tag {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.category-tag {
  background: rgba(34, 197, 94, 0.2);
  color: #4ade80;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.cluster-tag {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
  border: 1px solid rgba(168, 85, 247, 0.3);
}

/* ===== RIGHT SECTION - METRICS ===== */
.metrics-section {
  flex: 1.5;
  padding: 40px;
  display: flex;
  align-items: stretch;
  background: transparent;
  min-height: 300px;
}

.metrics-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr 1fr !important;
  gap: 20px;
  width: 100%;
  min-height: 240px;
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  overflow: visible;
  backdrop-filter: none;
  box-shadow: none;
  transition: all 0.3s ease;
}

/* ===== METRIC CARDS ===== */
.metric-card {
  padding: 24px 20px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  min-height: 200px;
  height: 100%;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  backdrop-filter: blur(20px);
}

/* 移除分隔线效果 */
.metric-card:not(:last-child) {
  border-right: none;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 特定卡片的背景颜色 - 降低饱和度，更柔和的样式 */
.metrics-grid .metric-card.noi-card {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.2)) !important;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.metrics-grid .metric-card.noi-card:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.25), rgba(5, 150, 105, 0.3)) !important;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.metrics-grid .metric-card.area-card {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.2)) !important;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.metrics-grid .metric-card.area-card:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.25), rgba(37, 99, 235, 0.3)) !important;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.metrics-grid .metric-card.price-card {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.15), rgba(126, 34, 206, 0.2)) !important;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(147, 51, 234, 0.2);
}

.metrics-grid .metric-card.price-card:hover {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.25), rgba(126, 34, 206, 0.3)) !important;
  border: 1px solid rgba(147, 51, 234, 0.3);
}

.metric-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 8px;
  width: 100%;
}

/* Simple metric icon styling - no background, no border */
.metric-icon-simple {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  transition: transform 0.3s ease;
}

/* Icon colors for different metric cards */
.metrics-grid .metric-card.noi-card .metric-icon-simple {
  color: rgba(16, 185, 129, 1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.metrics-grid .metric-card.area-card .metric-icon-simple {
  color: rgba(59, 130, 246, 1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.metrics-grid .metric-card.price-card .metric-icon-simple {
  color: rgba(147, 51, 234, 1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Hover effects for simple icons */
.metric-card:hover .metric-icon-simple {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
}

.metric-label {
  font-size: 11px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95) !important;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-align: center;
  line-height: 1.2;
  transition: all 0.3s ease;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff !important;
  line-height: 1.1;
  text-align: center;
  word-break: break-word;
  transition: all 0.3s ease;
  margin-bottom: 4px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.metric-unit {
  font-size: 11px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.metric-change {
  font-size: 11px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-align: center;
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.25);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.metric-date {
  font-size: 11px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Metric icon color styling removed since icons are no longer used */

/* 特定卡片的文字颜色 - 优化对比度 */
.metrics-grid .metric-card.noi-card .metric-label {
  color: rgba(255, 255, 255, 0.95) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.metrics-grid .metric-card.noi-card .metric-value {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.metrics-grid .metric-card.area-card .metric-label {
  color: rgba(255, 255, 255, 0.95) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.metrics-grid .metric-card.area-card .metric-value {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.metrics-grid .metric-card.price-card .metric-label {
  color: rgba(255, 255, 255, 0.95) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.metrics-grid .metric-card.price-card .metric-value {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

/* 特定卡片的辅助文字颜色 - 优化对比度 */
.metrics-grid .metric-card.noi-card .metric-change {
  color: rgba(255, 255, 255, 0.95) !important;
  background: rgba(16, 185, 129, 0.2) !important;
  border: 1px solid rgba(16, 185, 129, 0.3) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.metrics-grid .metric-card.area-card .metric-unit {
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.metrics-grid .metric-card.price-card .metric-date {
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Metric icon hover effects removed since icons are no longer used */

/* 移除特定卡片的悬停背景效果，保持简洁 */

/* 数值动画效果 */
.metric-card:hover .metric-value {
  transform: scale(1.02);
}

/* 标签悬停动画效果 - 保持白色以确保可读性 */
.metrics-grid .metric-card.noi-card:hover .metric-label {
  color: rgba(255, 255, 255, 1) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.metrics-grid .metric-card.area-card:hover .metric-label {
  color: rgba(255, 255, 255, 1) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.metrics-grid .metric-card.price-card:hover .metric-label {
  color: rgba(255, 255, 255, 1) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

/* 整体卡片动画 */
.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.03), transparent);
  transition: left 0.6s ease;
  pointer-events: none;
}

.metric-card:hover::before {
  left: 100%;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .hero-card-content {
    flex-direction: column;
  }

  .parcel-info-section {
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 32px 24px 24px;
  }

  .metrics-section {
    border-left: none;
    padding: 24px 24px 32px;
  }

  .metrics-grid {
    grid-template-columns: 1fr 1fr 1fr !important;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 24px 16px;
  }

  .parcel-info-section,
  .metrics-section {
    padding: 24px;
  }

  .metrics-grid {
    grid-template-columns: 1fr !important;
    gap: 16px;
  }

  .metric-card {
    padding: 20px 16px;
    min-height: 140px;
    gap: 8px;
  }

  /* Metric icon responsive styling removed since icons are no longer used */

  .metric-value {
    font-size: 20px;
  }

  .metric-label {
    font-size: 10px;
  }

  .parcel-title {
    font-size: 20px;
  }

  .parcel-apn {
    font-size: 32px;
  }

  .parcel-header {
    gap: 16px;
  }

  /* Parcel icon responsive styling removed since icon is no longer used */
}

@keyframes tagFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

.tag:nth-child(1) {
  animation: tagFloat 3s ease-in-out infinite;
}

.tag:nth-child(2) {
  animation: tagFloat 3s ease-in-out infinite 0.5s;
}

.tag:nth-child(3) {
  animation: tagFloat 3s ease-in-out infinite 1s;
}

/* ===== LOADING & ERROR STATES ===== */
.loading-section {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #ffffff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

.error-section {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #ffffff;
  text-align: center;
  padding: 24px;
}

.error-section h2 {
  font-size: 24px;
  margin-bottom: 16px;
  color: #ef4444;
}

.error-section p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 24px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #3b82f6;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #2563eb;
}

/* ===== INFO PANELS ===== */
.info-panels {
  position: relative;
  z-index: 10;
  padding: 0 24px 16px;
}

.panels-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 16px;
  grid-template-areas:
    "basic financial owner"
    "basic financial location";
}

.info-panel {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.4) 0%, rgba(37, 99, 235, 0.3) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(30, 58, 138, 0.2);
  position: relative;
}

.info-panel:hover {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.5) 0%, rgba(37, 99, 235, 0.4) 100%);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 12px 48px rgba(30, 58, 138, 0.3);
}

/* Panel specific grid areas */
.basic-panel {
  grid-area: basic;
}

.financial-panel {
  grid-area: financial;
}

.owner-panel {
  grid-area: owner;
}

.location-panel {
  grid-area: location;
}

.panel-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px 8px;
  border-bottom: none;
  background: transparent;
  position: relative;
  z-index: 1;
}

.panel-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  background-color: transparent !important;
}

.basic-icon {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.15)) !important;
  color: #60a5fa;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.financial-icon {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.15)) !important;
  color: #34d399;
  border: 1px solid rgba(16, 185, 129, 0.3);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.owner-icon {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(124, 58, 237, 0.15)) !important;
  color: #a78bfa;
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

.location-icon {
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.2), rgba(234, 88, 12, 0.15)) !important;
  color: #fb923c;
  border: 1px solid rgba(249, 115, 22, 0.3);
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.2);
}

.panel-title {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.panel-content {
  padding: 16px 20px 20px;
  background: transparent;
  position: relative;
  z-index: 1;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  margin: 0;
  background: transparent;
  border-radius: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.2s ease;
}

.info-row:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.info-row:hover {
  padding-left: 8px;
  border-color: rgba(255, 255, 255, 0.12);
}

.info-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
  text-transform: none;
  letter-spacing: 0;
  line-height: 1.5;
}

.info-value {
  font-size: 15px;
  color: white;
  font-weight: 600;
  text-align: right;
  word-break: break-word;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

/* Financial Metrics Grid */
.financial-metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.financial-card {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(37, 99, 235, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 8px;
  padding: 12px;
  text-align: left;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  min-height: 70px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.financial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.financial-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.25);
}

.noi-metric::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.noi-metric {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.1)) !important;
  border-color: rgba(16, 185, 129, 0.25) !important;
}

.noi-metric:hover {
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.35) !important;
}

.noi-metric .financial-value {
  color: #34d399 !important;
}

.price-metric::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.price-metric {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(124, 58, 237, 0.1)) !important;
  border-color: rgba(139, 92, 246, 0.25) !important;
}

.price-metric:hover {
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.35) !important;
}

.price-metric .financial-value {
  color: #a78bfa !important;
}

.financial-label {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1.2;
}

.financial-value {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
}

/* New Financial Card Structure */
.financial-card-header {
  margin-bottom: 8px;
}

.financial-card-title {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0;
  line-height: 1.3;
}

.financial-card-value {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
}

/* Update NOI card value color */
.noi-metric .financial-card-value {
  color: #34d399 !important;
}

/* Update Price card value color */
.price-metric .financial-card-value {
  color: #a78bfa !important;
}

/* Owner Profile */
.owner-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 0;
}

.owner-avatar {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #ec4899, #be185d);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 16px;
  font-weight: 800;
  box-shadow: 0 4px 12px rgba(236, 72, 153, 0.3);
  border: none;
  flex-shrink: 0;
}

.owner-details {
  flex: 1;
}

.owner-name {
  font-size: 15px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  letter-spacing: -0.025em;
  line-height: 1.3;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .panels-container {
    grid-template-columns: 1fr 1fr;
    grid-template-areas:
      "basic financial"
      "owner location";
    gap: 20px;
  }
}

@media (max-width: 900px) {
  .panels-container {
    grid-template-columns: 1fr;
    grid-template-areas:
      "basic"
      "financial"
      "owner"
      "location";
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .header-content {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 16px 20px;
    text-align: center;
  }

  .header-left,
  .header-right {
    justify-content: center;
  }

  .header-center {
    order: -1;
  }

  .header-title {
    flex-direction: column;
    gap: 12px;
  }

  .title-content {
    text-align: center;
  }

  .back-to-map-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .title-main {
    font-size: 18px;
  }

  .title-subtitle {
    font-size: 13px;
  }

  .language-switcher {
    align-self: center;
  }

  /* Hero Section Responsive */
  .hero-content {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .parcel-hero-card {
    order: 1;
  }

  .metrics-container {
    order: 2;
  }

  .parcel-card-content {
    padding: 24px 20px;
  }

  .parcel-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .parcel-main-info {
    text-align: center;
  }

  .parcel-title {
    font-size: 20px;
  }

  .parcel-apn {
    font-size: 16px;
  }

  .parcel-location {
    justify-content: center;
  }

  .parcel-tags {
    justify-content: center;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .metric-card {
    min-height: 120px;
  }

  .metric-card-content {
    padding: 20px;
  }

  .metric-value {
    font-size: 24px;
  }

  .panels-container {
    grid-template-columns: 1fr;
    grid-template-areas:
      "basic"
      "financial"
      "owner"
      "location";
    gap: 16px;
  }

  .financial-metrics-grid {
    grid-template-columns: 1fr 1fr;
    gap: 10px;
  }

  .panel-header {
    padding: 20px 20px 16px;
  }

  .panel-content {
    padding: 20px 24px 28px;
  }

  .panel-icon {
    width: 44px;
    height: 44px;
  }

  .panel-title {
    font-size: 18px;
  }

  .owner-avatar {
    width: 48px;
    height: 48px;
    font-size: 18px;
  }

  .owner-name {
    font-size: 16px;
  }

  .hero-section,
  .info-panels {
    padding-left: 16px;
    padding-right: 16px;
  }

  .top-header {
    padding: 12px 16px;
  }
}

/* Additional enhancements for the new design */
.info-panel {
  position: relative;
  overflow: hidden;
}

.info-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.info-panel:hover::before {
  opacity: 1;
}

/* Panel specific styling - Basic Information Panel */
.basic-panel {
  grid-area: basic;
}

.basic-panel .panel-header {
  background: transparent;
  border-bottom: none;
}

/* Basic panel content spacing */
.basic-panel .panel-content {
  padding: 16px 20px 20px;
  background: transparent;
}

/* Clean minimal info rows for basic panel */
.basic-panel .info-row {
  padding: 10px 0;
  margin: 0;
  background: transparent;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.basic-panel .info-row:last-child {
  border-bottom: none;
}

.basic-panel .info-row:hover {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.15);
  transform: none;
  box-shadow: none;
  padding-left: 8px;
}

/* Clean typography for basic panel */
.basic-panel .info-label {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 0;
  text-transform: none;
}

.basic-panel .info-value {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  text-align: right;
  letter-spacing: -0.01em;
}

/* Special styling for specific values */
.basic-panel .info-row:first-child .info-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 15px;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.basic-panel .info-row:nth-child(4) .info-value,
.basic-panel .info-row:nth-child(5) .info-value {
  font-weight: 700;
  color: #60a5fa;
}

/* Basic panel title styling */
.basic-panel .panel-title {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  letter-spacing: -0.025em;
}

/* Basic panel hover effects */
.basic-panel:hover {
  border-color: rgba(59, 130, 246, 0.25);
  box-shadow: 0 12px 48px rgba(59, 130, 246, 0.15);
}

.basic-panel:hover .basic-icon {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

/* Responsive adjustments for basic panel */
@media (max-width: 768px) {
  .basic-panel .panel-content {
    padding: 16px 18px 20px;
  }

  .basic-panel .info-row {
    padding: 10px 0;
  }

  .basic-panel .info-row:hover {
    padding-left: 8px;
  }

  .basic-panel .info-label {
    font-size: 13px;
  }

  .basic-panel .info-value {
    font-size: 14px;
  }
}

.financial-panel {
  grid-area: financial;
}

/* Financial panel specific styling */
.financial-panel .panel-content {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 16px 20px 20px;
}

.financial-panel .info-row {
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: transparent;
  border-radius: 0;
  margin: 0;
  border-left: none;
  border-right: none;
  border-top: none;
  transition: all 0.2s ease;
}

.financial-panel .info-row:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.financial-panel .info-row:hover {
  padding-left: 8px;
  border-color: rgba(255, 255, 255, 0.12);
}

.financial-panel .info-label {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
  text-transform: none;
  letter-spacing: 0;
}

.financial-panel .info-value {
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
  text-align: right;
  letter-spacing: -0.01em;
}

/* Owner Panel Specific Styles */
.owner-panel {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.4) 0%, rgba(37, 99, 235, 0.3) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 8px 32px rgba(30, 58, 138, 0.2);
}

.owner-panel .panel-content {
  padding: 16px 20px 20px;
}

.owner-panel .info-row {
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  margin: 0;
  background: transparent;
  border-radius: 0;
  transition: all 0.2s ease;
}

.owner-panel .info-row:last-child {
  border-bottom: none;
}

.owner-panel .info-row:hover {
  padding-left: 8px;
  border-color: rgba(255, 255, 255, 0.12);
}

.owner-panel .info-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
  text-transform: none;
  letter-spacing: 0;
}

.owner-panel .info-value {
  font-size: 15px;
  color: #ffffff;
  font-weight: 600;
  text-align: right;
  letter-spacing: -0.01em;
}

/* Location Panel Specific Styles */
.location-panel {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.4) 0%, rgba(37, 99, 235, 0.3) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 8px 32px rgba(30, 58, 138, 0.2);
}

.location-panel .panel-content {
  padding: 16px 20px 20px;
}

.location-panel .info-row {
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  margin: 0;
  background: transparent;
  border-radius: 0;
  transition: all 0.2s ease;
}

.location-panel .info-row:last-child {
  border-bottom: none;
}

.location-panel .info-row:hover {
  padding-left: 8px;
  border-color: rgba(255, 255, 255, 0.12);
}

.location-panel .info-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
  text-transform: none;
  letter-spacing: 0;
}

.location-panel .info-value {
  font-size: 15px;
  color: #ffffff;
  font-weight: 600;
  text-align: right;
  letter-spacing: -0.01em;
}

/* Mobile responsive styles for all panels */
@media (max-width: 768px) {
  .financial-panel .panel-content,
  .owner-panel .panel-content,
  .location-panel .panel-content {
    padding: 16px 18px 20px;
  }

  .financial-panel .info-row,
  .owner-panel .info-row,
  .location-panel .info-row {
    padding: 10px 0;
  }

  .financial-panel .info-row:hover,
  .owner-panel .info-row:hover,
  .location-panel .info-row:hover {
    padding-left: 8px;
  }

  .owner-avatar {
    width: 48px;
    height: 48px;
    font-size: 18px;
  }

  .owner-name {
    font-size: 16px;
  }
}

/* Icon hover effects */
.panel-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Smooth animations */
@keyframes panelFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-panel {
  animation: panelFadeIn 0.6s ease-out;
}

.info-panel:nth-child(1) { animation-delay: 0.1s; }
.info-panel:nth-child(2) { animation-delay: 0.2s; }
.info-panel:nth-child(3) { animation-delay: 0.3s; }
.info-panel:nth-child(4) { animation-delay: 0.4s; }

/* Extra small screens - ensure financial cards remain side by side */
@media (max-width: 480px) {
  .financial-metrics-grid {
    grid-template-columns: 1fr 1fr;
    gap: 6px;
  }

  .financial-card {
    min-height: 60px;
    padding: 8px;
  }

  .financial-card-title {
    font-size: 10px;
    margin-bottom: 4px;
  }

  .financial-card-value {
    font-size: 14px;
  }
}

/* ===== BOTTOM ACTION BUTTONS ===== */
.bottom-actions {
  position: relative;
  z-index: 10;
  padding: 16px 20px 50px;
  margin-top: 8px;
}

.actions-container {
  max-width: 800px;
  margin: 0 auto;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  max-width: 600px;
  margin: 0 auto;
}

.action-btn {
  border: none;
  border-radius: 16px;
  padding: 14px 18px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 80px;
  font-family: inherit;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  z-index: -1;
}

/* Map Button - Blue Theme */
.map-btn {
  background: rgba(74, 144, 226, 0.15);
  color: #6BB6FF;
  border-color: rgba(74, 144, 226, 0.2);
}

.map-btn:hover {
  background: rgba(74, 144, 226, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(74, 144, 226, 0.2);
  border-color: rgba(74, 144, 226, 0.3);
}

/* ROI Button - Green Theme */
.roi-btn {
  background: rgba(80, 200, 120, 0.15);
  color: #6FD394;
  border-color: rgba(80, 200, 120, 0.2);
}

.roi-btn:hover {
  background: rgba(80, 200, 120, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(80, 200, 120, 0.2);
  border-color: rgba(80, 200, 120, 0.3);
}

/* Share Button - Purple Theme */
.share-btn {
  background: rgba(142, 68, 173, 0.15);
  color: #B794F6;
  border-color: rgba(142, 68, 173, 0.2);
}

.share-btn:hover {
  background: rgba(142, 68, 173, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(142, 68, 173, 0.2);
  border-color: rgba(142, 68, 173, 0.3);
}

.btn-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.btn-icon svg {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

.btn-content {
  text-align: center;
  flex: 1;
}

.btn-title {
  display: block;
  font-size: 13px;
  font-weight: 500;
  margin: 0;
  line-height: 1.2;
  opacity: 0.9;
}

.btn-subtitle {
  display: none;
}

/* ===== ROI MODAL ===== */
.roi-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.roi-modal {
  background: linear-gradient(135deg,
    rgba(26, 26, 46, 0.95) 0%,
    rgba(22, 33, 62, 0.95) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  backdrop-filter: blur(20px);
  animation: slideUp 0.3s ease;
}

.roi-modal-header {
  padding: 24px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 24px;
}

.roi-modal-header h3 {
  color: white;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 32px;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.roi-modal-content {
  padding: 0 24px 24px;
}

.roi-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.roi-metric {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.roi-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 500;
}

.roi-value {
  color: white;
  font-size: 20px;
  font-weight: 600;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .action-buttons {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
    max-width: 500px;
  }

  .action-btn {
    padding: 12px 14px;
    gap: 6px;
    min-height: 70px;
    border-radius: 14px;
  }

  .btn-icon {
    width: 18px;
    height: 18px;
  }

  .btn-icon svg {
    width: 18px;
    height: 18px;
  }

  .btn-title {
    font-size: 12px;
  }

  .roi-metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .roi-modal {
    margin: 10px;
    max-height: 85vh;
  }

  .roi-modal-header {
    padding: 20px 20px 0;
  }

  .roi-modal-content {
    padding: 0 20px 20px;
  }
}

@media (max-width: 480px) {
  .bottom-actions {
    padding: 12px 15px 40px;
  }

  .action-buttons {
    gap: 5px;
    max-width: 400px;
  }

  .action-btn {
    padding: 10px 12px;
    gap: 5px;
    min-height: 65px;
    border-radius: 12px;
  }

  .btn-icon {
    width: 16px;
    height: 16px;
  }

  .btn-icon svg {
    width: 16px;
    height: 16px;
  }

  .btn-title {
    font-size: 11px;
  }
}

