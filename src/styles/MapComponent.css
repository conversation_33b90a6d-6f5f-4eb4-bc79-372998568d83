/* ============= 全局与字体设置 ============= */
body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  background-color: #f5f6fa;
  color: #3b3b3b;
}

/* 容器整体布局：左侧栏 + 地图 */
.map-page-container {
  display: flex;
  flex-direction: row;
  height: 100vh;
  overflow: hidden;
}

/* ============= 侧边栏整体样式 ============= */
:root {
  --detail-sidebar-bg: linear-gradient(to bottom, #f5f7fa, #c3cfe2);
  --detail-sidebar-text: #333333;
  --detail-sidebar-text-muted: rgba(0, 0, 0, 0.6);
  --detail-sidebar-hover: rgba(0, 0, 0, 0.05);
  --detail-sidebar-active: rgba(52, 152, 219, 0.15);
  --detail-sidebar-border: rgba(0, 0, 0, 0.08);
  --detail-sidebar-active-border: #3498db;
  --detail-sidebar-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  --detail-sidebar-item-radius: 8px;
  --detail-sidebar-transition: all 0.25s ease;
  --detail-sidebar-width: 280px;
}

.sidebar {
  width: var(--detail-sidebar-width);
  background: var(--detail-sidebar-bg);
  box-shadow: var(--detail-sidebar-shadow);
  padding: 0;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
  position: sticky;
  top: 0;
  z-index: 1000;
  color: var(--detail-sidebar-text);
  border-right: 1px solid var(--detail-sidebar-border);
  display: flex;
  flex-direction: column;
}

/* ============= 侧边栏头部 ============= */
.sidebar-header {
  padding: 16px 20px;
  text-align: center;
  margin-bottom: 0;
  border-bottom: 1px solid var(--detail-sidebar-border);
  position: relative;
  flex-shrink: 0;
}
.sidebar-header h2 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: 0.3px;
}
.filter-count {
  font-size: 0.85rem;
  background: rgba(0, 0, 0, 0.03);
  padding: 5px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  margin-top: 6px;
}

/* ============= 小标题统一风格 ============= */
.sidebar h2,
.sidebar h3 {
  margin-top: 0;
  font-weight: 600;
  color: var(--detail-sidebar-text);
}
.sidebar h3 {
  font-size: 1rem;
  margin-bottom: 0.8rem;
  letter-spacing: 0.3px;
  border-left: 2px solid var(--detail-sidebar-active-border);
  padding-left: 10px;
}

/* ============= 筛选区 ============= */
.filter-section {
  padding: 16px 20px;
  border-bottom: 1px solid var(--detail-sidebar-border);
}
.filter-group {
  margin-bottom: 1.5rem;
  padding: 0;
}
.filter-group:last-child {
  margin-bottom: 0;
}

/* ============= 租金滑块 ============= */
.price-slider-container {
  margin-top: 0.8rem;
}
.price-slider {
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  outline: none;
  margin-bottom: 0.8rem;
  cursor: pointer;
}
.price-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3498db;
  box-shadow: 0 0 8px rgba(52, 152, 219, 0.6);
  cursor: pointer;
  transition: all 0.2s ease;
}
.price-slider::-webkit-slider-thumb:hover {
  background: #2980b9;
  transform: scale(1.1);
}
.price-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}
.price-label {
  color: #7f8c8d;
}
.price-value {
  font-weight: 600;
  color: #2c3e50;
  background: rgba(52, 152, 219, 0.15);
  padding: 4px 12px;
  border-radius: 12px;
}

/* ============= 多选框下拉列表 ============= */
.multi-select,
.state-select,
.city-select {
  max-height: 180px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.4);
  border-radius: var(--detail-sidebar-item-radius);
  border: 1px solid var(--detail-sidebar-border);
  padding: 0.8rem;
  margin-top: 0.5rem;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 自定义滚动条 */
.multi-select::-webkit-scrollbar,
.state-select::-webkit-scrollbar,
.city-select::-webkit-scrollbar {
  width: 5px;
}

.multi-select::-webkit-scrollbar-track,
.state-select::-webkit-scrollbar-track,
.city-select::-webkit-scrollbar-track {
  background: transparent;
}

.multi-select::-webkit-scrollbar-thumb,
.state-select::-webkit-scrollbar-thumb,
.city-select::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}

/* ============= 复选框通用样式 ============= */
.checkbox-item,
.checkbox-item.enhanced {
  margin-bottom: 0.6rem;
  transition: background 0.2s ease;
}
.checkbox-item:hover,
.checkbox-item.enhanced:hover {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.checkbox-container,
.checkbox-container.enhanced {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  font-size: 0.95rem;
  color: #2c3e50;
  font-weight: 500;
  user-select: none;
  line-height: 1.5;
}

.checkbox-container input,
.checkbox-container.enhanced input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* 自定义勾选框 */
.checkmark,
.checkmark.enhanced {
  position: absolute;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.checkbox-container:hover input ~ .checkmark,
.checkbox-container.enhanced:hover input ~ .checkmark.enhanced {
  border-color: #3498db;
}

.checkbox-container input:checked ~ .checkmark,
.checkbox-container.enhanced input:checked ~ .checkmark.enhanced {
  background-color: #3498db;
  border-color: #3498db;
}

/* 勾选符号 */
.checkmark:after,
.checkmark.enhanced:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-container input:checked ~ .checkmark:after,
.checkbox-container.enhanced input:checked ~ .checkmark.enhanced:after {
  display: block;
}

.checkmark:after,
.checkmark.enhanced:after {
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid #ffffff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* ============= 固定操作区 ============= */
.filter-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

/* ============= 没有结果时的提示 ============= */
.no-results {
  color: #94a3b8;
  font-size: 0.9rem;
  padding: 0.5rem;
  text-align: center;
  font-style: italic;
}

/* ============= 超链接按钮 ============= */
.btn-link {
  background: none;
  border: none;
  color: var(--detail-sidebar-active-border);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0;
  text-decoration: none;
  transition: var(--detail-sidebar-transition);
  position: relative;
}

.btn-link:after {
  content: '';
  position: absolute;
  width: 0;
  height: 1px;
  bottom: -2px;
  left: 0;
  background-color: var(--detail-sidebar-active-border);
  transition: width 0.25s ease;
}

.btn-link:hover {
  color: #2980b9;
}

.btn-link:hover:after {
  width: 100%;
}

/* ============= 搜索输入框 ============= */
.search-filter {
  margin-bottom: 0.8rem;
}
.search-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--detail-sidebar-border);
  border-radius: var(--detail-sidebar-item-radius);
  font-size: 0.9rem;
  color: #333333 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  box-sizing: border-box;
  transition: var(--detail-sidebar-transition);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}
.search-input:focus {
  outline: none;
  border-color: var(--detail-sidebar-active-border);
  background: rgba(255, 255, 255, 1) !important;
  color: #222222 !important;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}
.search-input::placeholder {
  color: rgba(0, 0, 0, 0.5) !important;
}

/* ============= 显示更多按钮 ============= */
.show-more-btn {
  background: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--detail-sidebar-border);
  border-radius: var(--detail-sidebar-item-radius);
  color: var(--detail-sidebar-text);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 0;
  width: 100%;
  text-align: center;
  transition: var(--detail-sidebar-transition);
  margin-top: 0.5rem;
}

.show-more-btn:hover {
  background: var(--detail-sidebar-hover);
  border-color: rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

/* ============= 结果列表区 ============= */
.results-section {
  padding: 16px 20px;
  flex-grow: 1;
  overflow-y: auto;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

/* 排序按钮 */
.btn-sort {
  background: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--detail-sidebar-border);
  border-radius: var(--detail-sidebar-item-radius);
  padding: 0.4rem 0.8rem;
  font-size: 0.85rem;
  color: var(--detail-sidebar-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: var(--detail-sidebar-transition);
}
.btn-sort:hover {
  background: var(--detail-sidebar-hover);
  border-color: rgba(0, 0, 0, 0.12);
}
.sort-icon {
  margin-right: 5px;
  font-style: normal;
}

/* 列表 */
.park-list {
  list-style: none;
  padding-left: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.park-item {
  margin-bottom: 0.5rem;
}
.park-item button {
  background: rgba(255, 255, 255, 0.4);
  border: 1px solid var(--detail-sidebar-border);
  border-radius: var(--detail-sidebar-item-radius);
  padding: 0.8rem 1rem;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: var(--detail-sidebar-transition);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.park-item button:hover {
  background: rgba(255, 255, 255, 0.6);
  border-color: rgba(0, 0, 0, 0.12);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}
.park-name {
  font-weight: 600;
  color: var(--detail-sidebar-text);
  margin-bottom: 0.3rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.park-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
}
.park-location {
  color: var(--detail-sidebar-text-muted);
}
.park-cost {
  font-weight: 600;
  color: var(--detail-sidebar-active-border);
}

/* ============= 地图容器 ============= */
.map-container {
  flex: 1;
  position: relative;
}

/* 隐藏地图底部的版权信息 */
.leaflet-control-attribution {
  display: none !important;
}

/* ============= 地图风格切换按钮 ============= */
.map-style-toggle {
  position: relative;
}
.style-toggle-btn,
.display-mode-toggle-btn {
  position: absolute;
  z-index: 1000;
  background: #ffffff;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 6px 10px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 5px;
}
.style-toggle-btn {
  top: 10px;
  right: 10px;
}
.display-mode-toggle-btn {
  top: 50px;
  right: 10px;
}

/* ============= 稳定的 Marker 样式 ============= */
.animated-marker {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto !important;
  z-index: 1000 !important;
}

.marker-inner {
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  z-index: 3;
  border: 2px solid white;
  box-sizing: border-box;
  transition: transform 0.2s ease;
}

.marker-outer-glow {
  position: absolute;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  z-index: 2;
  opacity: 0.8;
}

/* 蓝色 Marker */
.blue .marker-inner {
  background: #007bff;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}
.blue .marker-outer-glow {
  background: rgba(0, 123, 255, 0.2);
  border: 1px solid rgba(0, 123, 255, 0.6);
}

/* 红色 Marker */
.red .marker-inner {
  background: #ff3a33;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}
.red .marker-outer-glow {
  background: rgba(255, 58, 51, 0.2);
  border: 1px solid rgba(255, 58, 51, 0.6);
}

/* 自定义颜色 Marker (使用CSS变量) */
.custom-color .marker-inner {
  background: var(--marker-color);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}
.custom-color .marker-outer-glow {
  background: color-mix(in srgb, var(--marker-color) 20%, transparent);
  border: 1px solid color-mix(in srgb, var(--marker-color) 60%, transparent);
}

/* 为不支持color-mix的浏览器提供回退 */
@supports not (background: color-mix(in srgb, red 20%, transparent)) {
  .custom-color .marker-outer-glow {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.3);
  }
}

/* Hover 效果 */
.animated-marker:hover .marker-inner {
  transform: scale(1.2);
}

/* ============= 用户自定义 Marker 样式 ============= */
.custom-user-marker {
  position: relative;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto !important;
  z-index: 1000 !important;
}

.custom-marker-outer {
  position: absolute;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  z-index: 2;
  opacity: 0.7;
  border: 2px dashed rgba(255, 255, 255, 0.8);
  animation: rotate 8s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.custom-marker-inner {
  position: absolute;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  z-index: 3;
  opacity: 0.6;
}

.custom-marker-center {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  z-index: 4;
  background: white;
  border: 2px solid rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

/* 绿色自定义 Marker */
.green .custom-marker-outer {
  border-color: rgba(76, 175, 80, 0.8);
}
.green .custom-marker-inner {
  background: rgba(76, 175, 80, 0.4);
  border: 1px solid rgba(76, 175, 80, 0.8);
}

/* 紫色自定义 Marker */
.purple .custom-marker-outer {
  border-color: rgba(156, 39, 176, 0.8);
}
.purple .custom-marker-inner {
  background: rgba(156, 39, 176, 0.4);
  border: 1px solid rgba(156, 39, 176, 0.8);
}

/* 橙色自定义 Marker */
.orange .custom-marker-outer {
  border-color: rgba(255, 152, 0, 0.8);
}
.orange .custom-marker-inner {
  background: rgba(255, 152, 0, 0.4);
  border: 1px solid rgba(255, 152, 0, 0.8);
}

/* 蓝色自定义 Marker */
.blue .custom-marker-outer {
  border-color: rgba(33, 150, 243, 0.8);
}
.blue .custom-marker-inner {
  background: rgba(33, 150, 243, 0.4);
  border: 1px solid rgba(33, 150, 243, 0.8);
}

/* 红色自定义 Marker */
.red .custom-marker-outer {
  border-color: rgba(244, 67, 54, 0.8);
}
.red .custom-marker-inner {
  background: rgba(244, 67, 54, 0.4);
  border: 1px solid rgba(244, 67, 54, 0.8);
}

/* Hover 效果 */
.custom-user-marker:hover .custom-marker-inner {
  opacity: 0.8;
}
.custom-user-marker:hover .custom-marker-center {
  transform: scale(1.2);
}

/* 自定义标记弹出窗口样式 */
.custom-marker-popup {
  padding: 16px;
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  min-width: 240px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background: white;
}

.custom-marker-popup-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.custom-marker-popup-title-display {
  font-weight: 600;
  color: #333;
  font-size: 16px;
  flex-grow: 1;
  cursor: pointer;
  padding: 6px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-marker-popup-title-display:hover {
  color: #2196F3;
}

.custom-marker-popup-title-display .edit-icon {
  opacity: 0.5;
  font-size: 14px;
  transition: opacity 0.2s ease;
}

.custom-marker-popup-title-display:hover .edit-icon {
  opacity: 1;
}

.custom-marker-popup-label-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 500;
  box-sizing: border-box;
  margin-right: 8px;
}

.custom-marker-popup-label-input:focus {
  border-color: #2196F3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.custom-marker-popup-edit-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-marker-popup-edit-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-marker-popup-edit-button.confirm {
  color: #4CAF50;
}

.custom-marker-popup-edit-button.cancel {
  color: #F44336;
}

.custom-marker-popup-edit-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.custom-marker-popup-coords {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 12px;
  margin-bottom: 16px;
  color: #666;
}

.custom-marker-popup-coord-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom-marker-popup-coord-label {
  font-weight: 500;
  color: #888;
}

.custom-marker-popup-coord-value {
  font-family: monospace;
  background: #f5f5f5;
  padding: 3px 8px;
  border-radius: 3px;
  color: #333;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.custom-marker-popup-coord-value .copy-icon {
  cursor: pointer;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.custom-marker-popup-coord-value .copy-icon:hover {
  opacity: 1;
}

.custom-marker-popup-color-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
  justify-content: center;
}

.custom-marker-popup-color-option {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.custom-marker-popup-color-option:hover {
  transform: scale(1.1);
}

.custom-marker-popup-color-option.selected {
  border-color: #333;
}

.custom-marker-popup-color-option.selected::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: white;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.custom-marker-popup-color-option.green {
  background-color: #4CAF50;
}

.custom-marker-popup-color-option.purple {
  background-color: #9C27B0;
}

.custom-marker-popup-color-option.orange {
  background-color: #FF9800;
}

.custom-marker-popup-color-option.blue {
  background-color: #2196F3;
}

.custom-marker-popup-color-option.red {
  background-color: #F44336;
}

.custom-marker-popup-actions {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.custom-marker-popup-button {
  flex: 1;
  padding: 10px 0;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.custom-marker-popup-button.save {
  background-color: #4CAF50;
  color: white;
}

.custom-marker-popup-button.save:hover {
  background-color: #45a049;
}

.custom-marker-popup-button.remove {
  background-color: #ff5252;
  color: white;
}

.custom-marker-popup-button.remove:hover {
  background-color: #ff1744;
}

/* 自定义标记弹出窗口容器 */
.custom-marker-popup-container .leaflet-popup-content-wrapper {
  border-radius: 8px;
  padding: 0;
  overflow: hidden;
  box-shadow: 0 3px 14px rgba(0, 0, 0, 0.15);
}

.custom-marker-popup-container .leaflet-popup-content {
  margin: 0;
  width: auto !important;
}

.custom-marker-popup-container .leaflet-popup-close-button {
  color: #666;
  font-size: 18px;
  padding: 4px;
  width: 24px;
  height: 24px;
  right: 10px;
  top: 10px;
  z-index: 1500; /* 确保关闭按钮在最上层 */
  text-decoration: none;
  background: none;
  border: none;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-marker-popup-container .leaflet-popup-close-button:hover {
  color: #333;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
}

/* 自定义标记弹出窗口内容样式 */
.custom-marker-popup {
  padding: 12px;
  padding-right: 30px; /* 为关闭按钮留出空间 */
}

/* 标题区域样式 */
.custom-marker-popup-header {
  margin-bottom: 12px;
  position: relative;
  height: 40px; /* 固定高度 */
  display: block; /* 改为块级元素 */
}

/* 编辑容器样式 */
.custom-marker-popup-edit-container {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.custom-marker-popup-title-display {
  font-size: 16px;
  font-weight: bold;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  transition: background-color 0.2s;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  box-sizing: border-box;
}

.title-text {
  margin-left: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-marker-popup-title-display:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.edit-icon {
  position: relative;
  margin-right: 8px;
  opacity: 0.6;
  font-size: 14px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
}

.custom-marker-popup-title-display:hover .edit-icon {
  opacity: 1;
}

/* 编辑模式样式 */
.custom-marker-popup-label-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 6px;
  box-sizing: border-box;
}

.custom-marker-popup-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 6px;
  margin-bottom: 0; /* 确保底部间距一致 */
}

.custom-marker-popup-edit-button {
  border: none;
  background: none;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.custom-marker-popup-edit-button.confirm {
  background-color: #A5D6A7;
  color: rgba(0, 0, 0, 0.7);
}

.custom-marker-popup-edit-button.cancel {
  background-color: #EF9A9A;
  color: rgba(0, 0, 0, 0.7);
}

/* 坐标信息样式 */
.custom-marker-popup-coords {
  margin-bottom: 12px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  padding: 8px;
}

.custom-marker-popup-coord-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.custom-marker-popup-coord-item:last-child {
  margin-bottom: 0;
}

.custom-marker-popup-coord-label {
  font-weight: 500;
  color: #555;
}

.custom-marker-popup-coord-value {
  display: flex;
  align-items: center;
}

.copy-icon {
  margin-left: 6px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.copy-icon:hover {
  opacity: 1;
}

/* 颜色选择器样式 */
.custom-marker-popup-color-options {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 12px;
}

.custom-marker-popup-color-option {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-marker-popup-color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.custom-marker-popup-color-option.selected {
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #333;
}

/* 颜色选项 - 进一步降低饱和度 */
.custom-marker-popup-color-option.green {
  background-color: #A5D6A7;
}

.custom-marker-popup-color-option.purple {
  background-color: #CE93D8;
}

.custom-marker-popup-color-option.orange {
  background-color: #FFCC80;
}

.custom-marker-popup-color-option.blue {
  background-color: #90CAF9;
}

.custom-marker-popup-color-option.red {
  background-color: #EF9A9A;
}

/* 按钮样式 */
.custom-marker-popup-actions {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.custom-marker-popup-button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-marker-popup-button span {
  margin-right: 6px;
}

.custom-marker-popup-button.save {
  background-color: #A5D6A7;
  color: rgba(0, 0, 0, 0.7);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-marker-popup-button.save:hover {
  background-color: #81C784;
}

.custom-marker-popup-button.remove {
  background-color: #EF9A9A;
  color: rgba(0, 0, 0, 0.7);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-marker-popup-button.remove:hover {
  background-color: #E57373;
}

.leaflet-div-icon {
  background: transparent;
  border: none;
}

/* ============= 小标题带图标提示 ============= */
.filter-group h3 {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0;
}
.info-icon {
  display: inline-block;
  position: relative;
  cursor: pointer;
  font-size: 16px;
  color: #3498db;
  transition: color 0.2s ease;
  z-index: 1001;
  opacity: 0.7;
  transition: opacity 0.25s ease;
}
.info-icon:hover {
  color: #2980b9;
  opacity: 1;
}

/* 提示气泡 */
.tooltip {
  position: absolute;
  bottom: calc(100% + 10px);
  left: 0;
  min-width: 200px;
  max-width: 300px;
  white-space: normal;
  word-break: break-word;
  line-height: 1.4;
  background: #ffffff;
  color: #2c3e50;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 2000;
  font-size: 14px;
  padding: 0.6rem 1rem;
  text-align: left;
  pointer-events: none;
}
.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 15px;
  border-width: 5px;
  border-style: solid;
  border-color: #ffffff transparent transparent transparent;
}

/* ============= 固定顶部操作区（例如全选/清空） ============= */
.fixed-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #f1f5f9;
  position: sticky;
  top: 0;
  background: #ffffff;
  z-index: 10;
}

/* ============= 弹窗按钮（地图 Marker Popup 中使用） ============= */
.popup-button {
  display: block;
  margin: 10px auto 0; /* Centered with top margin */
  background: linear-gradient(135deg, #6c63ff, #3f3d56);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.popup-button:hover {
  background: linear-gradient(135deg, #6c63ff, #3f3d56);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* ============= 国家选择样式 ============= */
.country-selector {
  padding: 1.2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.country-selector h3 {
  margin-bottom: 0.5rem;
  color: #ecf0f1;
  font-size: 1rem;
}
.country-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.country-btn {
  flex: 1;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #ecf0f1;
  transition: all 0.25s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.country-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}
.country-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

/* ============= 泰国省份选择样式 ============= */
.province-select {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.8rem;
  margin-top: 0.5rem;
}
.province-item {
  margin-bottom: 0.6rem;
}
.province-button {
  width: 100%;
  text-align: left;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #ecf0f1;
  transition: all 0.25s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.province-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(52, 152, 219, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.province-button.active {
  background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* ============= 保留：便于其他地方使用 ============= */
@media (max-width: 768px) {
  .map-page-container {
    flex-direction: column;
  }
  .sidebar {
    width: 100%;
    height: auto;
    max-height: 50vh;
  }
  .map-container {
    height: 50vh;
  }
}

/* ============= 标记聚合样式 ============= */
.marker-cluster {
  background-clip: padding-box;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.8);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  /* 允许鼠标事件穿透到下层 */
  pointer-events: auto;
}

.marker-cluster-small {
  background-color: rgba(52, 152, 219, 0.8);
}

.marker-cluster-medium {
  background-color: rgba(241, 196, 15, 0.8);
}

.marker-cluster-large {
  background-color: rgba(231, 76, 60, 0.8);
}

.cluster-marker {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 标记聚合多边形样式 */
.marker-cluster-polygon {
  /* 允许鼠标事件穿透到下层 */
  pointer-events: none !important;
}

/* 确保所有标记都允许鼠标事件穿透 */
.leaflet-marker-icon,
.leaflet-marker-shadow {
  pointer-events: auto !important;
}

/* 确保标记的点击事件仍然有效，但悬停事件可以穿透 */
.leaflet-marker-pane {
  pointer-events: auto;
}

/* 特殊标记类，允许事件穿透 */
.marker-with-events-passthrough {
  pointer-events: auto;
}

/* 确保地图中的所有图层都能接收鼠标事件 */
.leaflet-pane {
  z-index: auto !important;
}

/* 确保州边界层在标记下方但仍能接收鼠标事件 */
.leaflet-overlay-pane {
  z-index: 200 !important;
  pointer-events: auto !important;
}

/* 确保Modal在最顶层 */
.leaflet-popup {
  z-index: 1000 !important;
}

/* 防止所有工具提示变成持久的 */
.leaflet-tooltip {
  pointer-events: none !important;
  transition: opacity 0.2s ease !important;
}

/* 确保工具提示不会阻止地图交互 */
.leaflet-tooltip-pane {
  pointer-events: none !important;
}

/* 防止特定类型的工具提示变成持久的 */
.cluster-boundary-tooltip,
.parcel-sample-tooltip,
.compact-state-tooltip,
.province-tooltip {
  pointer-events: none !important;
  transition: opacity 0.2s ease !important;
}

/* 强制所有Leaflet工具提示不可交互 */
.leaflet-container .leaflet-tooltip {
  pointer-events: none !important;
  user-select: none !important;
}

/* 防止工具提示阻止地图点击事件 */
.leaflet-tooltip-pane .leaflet-tooltip {
  pointer-events: none !important;
}

/* Ensure popup close button is always clickable */
.leaflet-container .leaflet-popup-close-button {
  z-index: 2600 !important; /* 确保关闭按钮在弹窗内容之上 */
  pointer-events: auto !important;
  cursor: pointer !important;
  position: absolute !important;
  top: 10px !important;
  right: 10px !important;
}

.react-modal-overlay {
  z-index: 2000 !important;
}

.react-modal-content {
  z-index: 2001 !important;
}

.popup-button {
  position: relative; /* 确保按钮层级在Popup内 */
  z-index: 1001;
}

.detail-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  padding: 20px;
  z-index: 1000;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.detail-section, .transport-section {
  margin-bottom: 25px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.label {
  font-weight: 600;
  color: #2d3436;
}

.value {
  color: #636e72;
  max-width: 60%;
  text-align: right;
}
