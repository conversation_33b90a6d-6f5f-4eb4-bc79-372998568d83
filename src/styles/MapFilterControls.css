/* Map Filter Controls CSS */
:root {
  --filter-bg: rgba(255, 255, 255, 0.95);
  --filter-text: #333333;
  --filter-border: rgba(0, 0, 0, 0.08);
  --filter-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  --filter-radius: 8px;
  --primary-color: #3498db;
  --hover-bg: rgba(52, 152, 219, 0.08);
  --active-bg: rgba(52, 152, 219, 0.15);
  --transition: all 0.25s ease;
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', '微软雅黑', 'PingFang SC', '苹方', 'Heiti SC', '黑体-简', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Overall container for map filter controls */
.map-filter-container {
  position: absolute;
  top: 80px;
  left: 15px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 300px;
  font-family: var(--font-family);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Toggle button for filter visibility */
.filter-toggle-button {
  background: var(--filter-bg);
  border-radius: var(--filter-radius);
  box-shadow: var(--filter-shadow);
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--filter-text);
  display: flex;
  align-items: center;
  gap: 8px;
  transition: var(--transition);
}

.filter-toggle-button:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
}

.filter-toggle-button:active {
  transform: translateY(0);
}

.filter-toggle-icon {
  font-size: 1.1rem;
}

/* Container for the filter controls */
.map-filter-controls {
  background: var(--filter-bg);
  border-radius: var(--filter-radius);
  box-shadow: var(--filter-shadow);
  overflow: hidden;
  border: none;
  transition: var(--transition);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  max-height: 70vh;
  overflow-y: auto;
  padding: 0;
  width: 300px;
}

/* Filter header */
.filter-header {
  padding: 12px 16px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--filter-text);
  background: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid var(--filter-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: var(--filter-radius) var(--filter-radius) 0 0;
}

/* Filter content */
.filter-content {
  padding: 15px;
}

/* Filter group */
.filter-group {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--filter-border);
}

.filter-group:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.filter-group h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: var(--filter-text);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Country selector */
.map-country-select {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.map-country-label {
  font-weight: 500;
  color: #555;
  min-width: 60px;
}

.map-country-dropdown {
  flex: 1;
  padding: 8px 10px;
  border: 1px solid var(--filter-border);
  border-radius: 4px;
  background: white;
  font-size: 0.9rem;
  color: var(--filter-text);
  cursor: pointer;
}

.map-country-dropdown:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Price slider */
.map-price-slider-container {
  margin: 10px 0;
}

.map-price-slider {
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  outline: none;
  margin-bottom: 10px;
  cursor: pointer;
}

.map-price-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--primary-color);
  box-shadow: 0 0 8px rgba(52, 152, 219, 0.6);
  cursor: pointer;
  transition: var(--transition);
}

.map-price-slider::-webkit-slider-thumb:hover {
  background: #2980b9;
  transform: scale(1.1);
}

.map-price-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.map-price-label {
  color: #7f8c8d;
}

.map-price-value {
  font-weight: 600;
  color: var(--primary-color);
  background: var(--active-bg);
  padding: 3px 10px;
  border-radius: 12px;
}

/* Range toggle button */
.map-range-toggle {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid var(--filter-border);
  border-radius: 20px;
  padding: 6px 12px;
  cursor: pointer;
  color: #2c3e50;
  transition: var(--transition);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  font-size: 0.85rem;
  margin-bottom: 8px;
  width: 100%;
}

.map-range-toggle:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: var(--primary-color);
}

/* Multi-select lists */
.map-multi-select {
  max-height: 150px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--filter-radius);
  border: 1px solid var(--filter-border);
  padding: 8px;
  margin-top: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Custom scrollbar */
.map-multi-select::-webkit-scrollbar {
  width: 4px;
}

.map-multi-select::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}

/* Filter actions */
.map-filter-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

/* Button link */
.map-btn-link {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0;
  text-decoration: none;
  transition: var(--transition);
}

.map-btn-link:hover {
  color: #2980b9;
  text-decoration: underline;
}

/* Checkbox item */
.map-checkbox-item {
  margin-bottom: 6px;
  transition: background 0.2s ease;
}

.map-checkbox-item:hover {
  background: var(--hover-bg);
  border-radius: 4px;
}

.map-checkbox-container {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 25px;
  cursor: pointer;
  font-size: 0.85rem;
  color: var(--filter-text);
  font-weight: 500;
  user-select: none;
  line-height: 1.4;
}

.map-checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.map-checkmark {
  position: absolute;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: var(--transition);
}

.map-checkbox-container:hover input ~ .map-checkmark {
  border-color: var(--primary-color);
}

.map-checkbox-container input:checked ~ .map-checkmark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.map-checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.map-checkbox-container input:checked ~ .map-checkmark:after {
  display: block;
}

.map-checkmark:after {
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid #ffffff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Search input */
.map-search-input {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid var(--filter-border);
  border-radius: var(--filter-radius);
  font-size: 0.85rem;
  color: #333333 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  box-sizing: border-box;
  transition: var(--transition);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 8px;
}

.map-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 1) !important;
  color: #222222 !important;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.map-search-input::placeholder {
  color: rgba(0, 0, 0, 0.5) !important;
}

/* Show more/less button */
.map-show-more-btn {
  background: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--filter-border);
  border-radius: var(--filter-radius);
  color: var(--filter-text);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  padding: 6px 0;
  width: 100%;
  text-align: center;
  transition: var(--transition);
  margin-top: 6px;
}

.map-show-more-btn:hover {
  background: var(--hover-bg);
  border-color: rgba(0, 0, 0, 0.12);
}

/* Province select */
.map-province-select {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 10px;
}

.map-province-button {
  padding: 8px 10px;
  background: white;
  border: 1px solid var(--filter-border);
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--filter-text);
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.map-province-button:hover {
  background-color: #f8f9fa;
  border-color: var(--primary-color);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* No results */
.map-no-results {
  color: #999;
  font-size: 0.85rem;
  padding: 8px;
  text-align: center;
  font-style: italic;
  background: rgba(0, 0, 0, 0.02);
  border-radius: var(--filter-radius);
}

/* Night mode styles */
.night-mode .map-filter-controls {
  background: rgba(30, 41, 59, 0.9);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.night-mode .filter-header {
  color: #e2e8f0;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.night-mode .filter-group {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.night-mode .filter-group h4 {
  color: #e2e8f0;
}

/* 只在真正的夜间模式下应用深色样式 */
body.night-mode .map-country-dropdown,
body.night-mode .map-search-input,
body.night-mode .map-range-toggle,
body.night-mode .map-province-button,
html.night-mode .map-country-dropdown,
html.night-mode .map-search-input,
html.night-mode .map-range-toggle,
html.night-mode .map-province-button {
  background: rgba(15, 23, 42, 0.8) !important;
  color: #e2e8f0 !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.night-mode .map-checkbox-container {
  color: #e2e8f0;
}

.night-mode .map-checkmark {
  background-color: rgba(15, 23, 42, 0.8);
  border-color: rgba(255, 255, 255, 0.2);
}

.night-mode .map-btn-link {
  color: #38bdf8;
}

.night-mode .map-btn-link:hover {
  color: #7dd3fc;
}

.night-mode .map-price-value {
  background: rgba(56, 189, 248, 0.2);
  color: #38bdf8;
}

.night-mode .map-no-results {
  color: #a0aec0;
  background: rgba(255, 255, 255, 0.05);
}

/* Responsive styles */
@media (max-width: 768px) {
  .map-filter-container {
    top: 70px;
    left: 10px;
  }

  .map-filter-controls {
    width: 250px;
  }

  .filter-header {
    padding: 10px 12px;
    font-size: 0.85rem;
  }

  .filter-content {
    padding: 12px;
  }

  .filter-group h4 {
    font-size: 0.85rem;
  }

  .map-province-select {
    grid-template-columns: 1fr;
  }
}
