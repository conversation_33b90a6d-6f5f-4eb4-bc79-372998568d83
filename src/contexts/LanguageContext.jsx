import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    // 如果没有 Provider，返回默认值
    return {
      isEnglish: true,
      language: 'en',
      toggleLanguage: () => {},
      setLanguage: () => {}
    };
  }
  return context;
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    // 从 localStorage 获取保存的语言设置，默认为英文
    return localStorage.getItem('preferredLanguage') || 'en';
  });

  const isEnglish = language === 'en';

  const toggleLanguage = () => {
    const newLanguage = language === 'en' ? 'zh' : 'en';
    setLanguage(newLanguage);
    localStorage.setItem('preferredLanguage', newLanguage);
  };

  const setLanguageDirectly = (lang) => {
    setLanguage(lang);
    localStorage.setItem('preferredLanguage', lang);
  };

  useEffect(() => {
    // 监听 localStorage 变化，以便在其他页面更改语言时同步
    const handleStorageChange = (e) => {
      if (e.key === 'preferredLanguage' && e.newValue) {
        setLanguage(e.newValue);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const value = {
    language,
    isEnglish,
    toggleLanguage,
    setLanguage: setLanguageDirectly
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext;
