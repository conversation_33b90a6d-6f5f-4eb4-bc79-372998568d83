"use client"

import { useState, useEffect, useMemo } from "react"
import { useParams, useNavigate } from 'react-router-dom'
import Silk from '../components/3D/Silk'
import "../styles/ParcelDetailPage.css"

// Icon Components
const BuildingIcon = () => (
  <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8v-3a1 1 0 011-1h2a1 1 0 011 1v3m-4 0h4" />
  </svg>
)

const MapPinIcon = () => (
  <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
)

const DollarIcon = () => (
  <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
)

const CalendarIcon = () => (
  <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
    <line x1="16" y1="2" x2="16" y2="6" />
    <line x1="8" y1="2" x2="8" y2="6" />
    <line x1="3" y1="10" x2="21" y2="10" />
  </svg>
)



const ArrowLeftIcon = () => (
  <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M19 12H5M12 19l-7-7 7-7" />
  </svg>
)

const UserIcon = () => (
  <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
  </svg>
)

// New Icons for Bottom Actions
const CalculatorIcon = () => (
  <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <rect x="4" y="2" width="16" height="20" rx="2" ry="2" />
    <line x1="8" y1="6" x2="16" y2="6" />
    <line x1="8" y1="10" x2="16" y2="10" />
    <line x1="8" y1="14" x2="16" y2="14" />
    <line x1="8" y1="18" x2="12" y2="18" />
    <line x1="16" y1="18" x2="16" y2="18" />
  </svg>
)

const ShareIcon = () => (
  <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <circle cx="18" cy="5" r="3" />
    <circle cx="6" cy="12" r="3" />
    <circle cx="18" cy="19" r="3" />
    <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" />
    <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" />
  </svg>
)

// Utility functions
const formatCurrency = (value) => {
  if (!value) return '$0'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value)
}

const formatNumber = (value) => {
  if (!value) return '0'
  return new Intl.NumberFormat('en-US').format(value)
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch {
    return dateString
  }
}

// ROI Calculation Functions
const calculateROI = (parcelData) => {
  if (!parcelData) return null

  // Basic assumptions for ROI calculation
  const buildingArea = parcelData.building_area || 50000
  const noi = parcelData.noi || 800000
  const latestSalePrice = parcelData.latest_sale_price || 12000000
  const annualTaxes = parcelData.annual_taxes || 120000

  // Estimated values for calculation
  const estimatedRentPerSqft = 8.50 // $8.50 per sq ft annually
  const operatingExpenseRatio = 0.25 // 25% of gross income
  const capRate = 0.065 // 6.5% cap rate
  const holdPeriod = 10 // 10 years
  const exitCapRate = 0.07 // 7% exit cap rate
  const discountRate = 0.10 // 10% discount rate

  // Calculate annual rental income
  const annualRentalIncome = buildingArea * estimatedRentPerSqft

  // Calculate operating expenses
  const operatingExpenses = annualRentalIncome * operatingExpenseRatio + annualTaxes

  // Calculate NOI (use provided NOI or calculate)
  const calculatedNOI = noi || (annualRentalIncome - operatingExpenses)

  // Calculate property value based on NOI and cap rate
  const propertyValue = calculatedNOI / capRate

  // Calculate cash-on-cash return (assuming 75% LTV)
  const loanAmount = propertyValue * 0.75
  const equityInvestment = propertyValue - loanAmount
  const interestRate = 0.045 // 4.5% interest rate
  const loanTerm = 25 // 25 years

  // Calculate annual debt service (simplified)
  const monthlyPayment = loanAmount * (interestRate / 12) / (1 - Math.pow(1 + interestRate / 12, -loanTerm * 12))
  const annualDebtService = monthlyPayment * 12

  // Calculate annual cash flow
  const annualCashFlow = calculatedNOI - annualDebtService

  // Calculate cash-on-cash return
  const cashOnCashReturn = annualCashFlow / equityInvestment

  // Calculate exit value and total return
  const exitNOI = calculatedNOI * Math.pow(1.025, holdPeriod) // 2.5% annual NOI growth
  const exitValue = exitNOI / exitCapRate
  const remainingLoanBalance = loanAmount * Math.pow(1 + interestRate, holdPeriod) -
    (monthlyPayment * 12 * (Math.pow(1 + interestRate, holdPeriod) - 1) / interestRate)
  const netExitProceeds = exitValue - Math.max(0, remainingLoanBalance)

  // Calculate total return and IRR (simplified)
  const totalCashFlow = annualCashFlow * holdPeriod + netExitProceeds
  const totalReturn = (totalCashFlow - equityInvestment) / equityInvestment
  const annualizedReturn = Math.pow(1 + totalReturn, 1 / holdPeriod) - 1

  return {
    propertyValue: Math.round(propertyValue),
    equityInvestment: Math.round(equityInvestment),
    annualNOI: Math.round(calculatedNOI),
    annualCashFlow: Math.round(annualCashFlow),
    cashOnCashReturn: (cashOnCashReturn * 100).toFixed(2),
    capRate: (capRate * 100).toFixed(2),
    totalReturn: (totalReturn * 100).toFixed(2),
    annualizedReturn: (annualizedReturn * 100).toFixed(2),
    exitValue: Math.round(exitValue),
    netExitProceeds: Math.round(netExitProceeds),
    annualRentalIncome: Math.round(annualRentalIncome),
    operatingExpenses: Math.round(operatingExpenses),
    annualDebtService: Math.round(annualDebtService)
  }
}

// CSV parsing function
const parseCSVLine = (line) => {
  const result = []
  let current = ''
  let inQuotes = false
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i]
    
    if (char === '"') {
      inQuotes = !inQuotes
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim())
      current = ''
    } else {
      current += char
    }
  }
  
  result.push(current.trim())
  return result
}

const ParcelDetailPage = () => {
  const { apn } = useParams()
  const navigate = useNavigate()
  const [parcelData, setParcelData] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [language, setLanguage] = useState('en')
  const [showROIModal, setShowROIModal] = useState(false)
  const [roiData, setROIData] = useState(null)

  // Multi-language support
  const translations = {
    en: {
      title: "Industrial Parcel Details",
      subtitle: "Comprehensive property information",
      backToMap: "Back to Map",
      loading: "Loading parcel data...",
      notFound: "Parcel Not Found",
      cannotLoad: "Cannot load parcel data for APN",
      back: "Back to Map"
    },
    zh: {
      title: "工业地块详情",
      subtitle: "全面的物业信息",
      backToMap: "返回地图",
      loading: "正在加载地块数据...",
      notFound: "未找到地块",
      cannotLoad: "无法加载APN的地块数据",
      back: "返回地图"
    }
  }

  const t = useMemo(() => translations[language], [language])

  const handleBack = () => {
    navigate('/main')
  }

  // Handle action buttons
  const handleViewOnMap = () => {
    if (parcelData && parcelData.latitude && parcelData.longitude) {
      const mapUrl = `https://www.google.com/maps?q=${parcelData.latitude},${parcelData.longitude}&z=18&t=h`
      window.open(mapUrl, '_blank')
    }
  }

  const handleCalculateROI = () => {
    if (parcelData) {
      const calculatedROI = calculateROI(parcelData)
      setROIData(calculatedROI)
      setShowROIModal(true)
    }
  }

  const handleShare = async () => {
    const shareData = {
      title: `Industrial Parcel ${parcelData?.apn}`,
      text: `Check out this industrial property: ${parcelData?.apn}`,
      url: window.location.href
    }

    if (navigator.share) {
      try {
        await navigator.share(shareData)
      } catch (err) {
        console.log('Error sharing:', err)
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href)
        alert('Link copied to clipboard!')
      }
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(window.location.href)
      alert('Link copied to clipboard!')
    }
  }

  useEffect(() => {
    const loadParcelData = async () => {
      if (!apn) {
        setError('No APN provided')
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        setError(null)



        // 从所有cluster文件中查找匹配的APN
        let foundParcel = null

        // 优先检查已知的cluster（基于常见的APN模式）
        const clusterOrder = [1, 0, 2, 3, 4, 5, 6, 7, 8, 9, 10]

        for (const clusterId of clusterOrder) {
          try {
            const url = `/data/cluster_${clusterId}_parcels.csv`


            const response = await fetch(url, {
              method: 'GET',
              headers: {
                'Accept': 'text/csv,text/plain,*/*',
                'Cache-Control': 'no-cache'
              }
            })

            if (!response.ok) {

              continue
            }

            const csvText = await response.text()

            if (!csvText || csvText.length < 100) {

              continue
            }



            const lines = csvText.split('\n').filter(line => line.trim())

            if (lines.length < 2) {

              continue
            }

            const headers = parseCSVLine(lines[0])


            // 检查headers是否正确
            if (!headers.includes('apn') || headers.length < 10) {

              continue
            }

            for (let i = 1; i < lines.length; i++) {
              const values = parseCSVLine(lines[i])

              if (values.length < headers.length) {
                continue // 跳过不完整的行
              }

              if (values[0] && values[0].trim() === apn) {


                const parcel = {}
                headers.forEach((header, index) => {
                  const value = values[index] ? values[index].trim() : ''

                  // 清理header名称
                  const cleanHeader = header.trim()

                  // 尝试转换数字
                  if (value && !isNaN(value) && value !== '' && !value.startsWith('{') && !value.includes('@')) {
                    parcel[cleanHeader] = parseFloat(value)
                  } else {
                    parcel[cleanHeader] = value
                  }
                })

                foundParcel = parcel
                foundParcel.cluster_id = clusterId
                break
              }
            }

            if (foundParcel) {
              break // 找到了，退出外层循环
            }

          } catch (error) {

            continue
          }
        }

        if (foundParcel) {

          setParcelData(foundParcel)
          setIsLoading(false)
        } else {

          setError(`Parcel "${apn}" not found in database. Please check the APN and try again.`)
          setIsLoading(false)
        }

      } catch (err) {
        console.error('Error loading parcel data:', err)
        setError(err.message || 'Failed to load parcel data')
        setIsLoading(false)
      }
    }

    loadParcelData()
  }, [apn])

  if (isLoading) {
    return (
      <div className="parcel-detail-container">
        <div className="parcel-background">
          <Silk speed={2} scale={1.2} color="#1a1a2e" noiseIntensity={0.4} rotation={0.1} />
          <div className="silk-overlay"></div>
        </div>
        <div className="loading-section">
          <div className="loading-spinner"></div>
          <p className="loading-text">{t.loading}</p>
        </div>
      </div>
    )
  }

  if (error || !parcelData) {
    return (
      <div className="parcel-detail-container">
        <div className="parcel-background">
          <Silk speed={2} scale={1.2} color="#1a1a2e" noiseIntensity={0.4} rotation={0.1} />
          <div className="silk-overlay"></div>
        </div>
        <div className="error-section">
          <h2>{t.notFound}</h2>
          <p>{error || `${t.cannotLoad} "${apn}"`}</p>
          <button className="back-button" onClick={handleBack}>
            <ArrowLeftIcon />
            {t.back}
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="parcel-detail-container">
      {/* Background */}
      <div className="parcel-background">
        <Silk speed={2} scale={1.2} color="#1a1a2e" noiseIntensity={0.4} rotation={0.1} />
        <div className="silk-overlay"></div>
      </div>

      {/* Enhanced Top Navigation Header */}
      <header className="top-header">
        <div className="header-backdrop"></div>
        <div className="header-content">
          {/* Left Section - Back Button */}
          <div className="header-left">
            <button className="back-to-map-btn" onClick={handleBack}>
              <div className="back-btn-icon">
                <ArrowLeftIcon />
              </div>
              <span className="back-btn-text">{t.backToMap}</span>
            </button>
          </div>

          {/* Center Section - Title */}
          <div className="header-center">
            <div className="header-title">
              <div className="title-icon-wrapper">
                <div className="title-icon">
                  <BuildingIcon />
                </div>
                <div className="title-icon-glow"></div>
              </div>
              <div className="title-content">
                <h1 className="title-main">{t.title}</h1>
                <p className="title-subtitle">{t.subtitle}</p>
              </div>
            </div>
          </div>

          {/* Right Section - Language Switcher */}
          <div className="header-right">
            <div className="language-switcher">
              <div className="lang-switcher-bg"></div>
              <button
                className={`lang-btn ${language === "en" ? "active" : ""}`}
                onClick={() => setLanguage("en")}
              >
                <span className="lang-text">EN</span>
              </button>
              <button
                className={`lang-btn ${language === "zh" ? "active" : ""}`}
                onClick={() => setLanguage("zh")}
              >
                <span className="lang-text">中文</span>
              </button>
            </div>
          </div>
        </div>

        {/* Header Bottom Border Effect */}
        <div className="header-border-effect">
          <div className="border-gradient"></div>
        </div>
      </header>

      {/* Unified Hero Section */}
      <main className="hero-section">
        <div className="hero-backdrop"></div>
        <div className="hero-content">
          {/* Unified Hero Card */}
          <div className="unified-hero-card">
            <div className="hero-card-bg"></div>
            <div className="hero-card-content">

              {/* Left Section - Parcel Information */}
              <div className="parcel-info-section">
                <div className="parcel-header">
                  <div className="parcel-main-info">
                    <h2 className="parcel-title">Industrial Parcel</h2>
                    <div className="parcel-apn">{parcelData.apn}</div>
                    <div className="parcel-location">
                      <MapPinIcon />
                      <span>{parcelData.latitude?.toFixed(6)}, {parcelData.longitude?.toFixed(6)}</span>
                    </div>
                  </div>
                </div>

                <div className="parcel-tags">
                  <div className="tag class-tag">
                    <span>Class {parcelData.building_class}</span>
                  </div>
                  <div className="tag category-tag">
                    <span>{parcelData.asset_category}</span>
                  </div>
                  <div className="tag cluster-tag">
                    <span>Cluster #{parcelData.cluster_id}</span>
                  </div>
                </div>
              </div>

              {/* Right Section - Metrics Grid */}
              <div className="metrics-section">
                <div className="metrics-grid">
                  {/* NOI Card */}
                  <div className="metric-card noi-card">
                    <div className="metric-icon-simple">
                      <DollarIcon />
                    </div>
                    <div className="metric-content">
                      <div className="metric-label">Net Operating Income</div>
                      <div className="metric-value">{formatCurrency(parcelData.noi)}</div>
                      <div className="metric-change">+12.5% YoY</div>
                    </div>
                  </div>

                  {/* Building Area Card */}
                  <div className="metric-card area-card">
                    <div className="metric-icon-simple">
                      <BuildingIcon />
                    </div>
                    <div className="metric-content">
                      <div className="metric-label">Building Area</div>
                      <div className="metric-value">{formatNumber(parcelData.building_area)}</div>
                      <div className="metric-unit">sq ft</div>
                    </div>
                  </div>

                  {/* Latest Sale Price Card */}
                  <div className="metric-card price-card">
                    <div className="metric-icon-simple">
                      <CalendarIcon />
                    </div>
                    <div className="metric-content">
                      <div className="metric-label">Latest Sale Price</div>
                      <div className="metric-value">{formatCurrency(parcelData.latest_sale_price)}</div>
                      <div className="metric-date">{formatDate(parcelData.latest_sale_date)}</div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </main>

      {/* Information Panels */}
      <section className="info-panels">
        <div className="panels-container">
          {/* Basic Information Panel */}
          <div className="info-panel basic-panel">
            <div className="panel-header">
              <div className="panel-icon basic-icon">
                <BuildingIcon />
              </div>
              <h3 className="panel-title">Basic Information</h3>
            </div>
            <div className="panel-content">
              <div className="info-row">
                <span className="info-label">Parcel ID (APN)</span>
                <span className="info-value">{parcelData.apn}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Building Area</span>
                <span className="info-value">{formatNumber(parcelData.building_area)} sq ft</span>
              </div>
              <div className="info-row">
                <span className="info-label">Lot Area</span>
                <span className="info-value">{formatNumber(parcelData.lot_area_ft)} sq ft</span>
              </div>
              <div className="info-row">
                <span className="info-label">Building Class</span>
                <span className="info-value">Class {parcelData.building_class}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Asset Category</span>
                <span className="info-value">{parcelData.asset_category}</span>
              </div>
            </div>
          </div>

          {/* Financial Information Panel */}
          <div className="info-panel financial-panel">
            <div className="panel-header">
              <div className="panel-icon financial-icon">
                <DollarIcon />
              </div>
              <h3 className="panel-title">Financial Information</h3>
            </div>
            <div className="panel-content">
              <div className="financial-metrics-grid">
                <div className="financial-card noi-metric">
                  <div className="financial-card-header">
                    <div className="financial-card-title">Net Operating Income</div>
                  </div>
                  <div className="financial-card-value">{formatCurrency(parcelData.noi)}</div>
                </div>
                <div className="financial-card price-metric">
                  <div className="financial-card-header">
                    <div className="financial-card-title">Latest Sale Price</div>
                  </div>
                  <div className="financial-card-value">{formatCurrency(parcelData.latest_sale_price)}</div>
                </div>
              </div>
              <div className="info-row">
                <span className="info-label">Annual Taxes</span>
                <span className="info-value">{formatCurrency(parcelData.annual_taxes)}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Sale Date</span>
                <span className="info-value">{formatDate(parcelData.latest_sale_date)}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Loan Balance</span>
                <span className="info-value">{formatCurrency(parcelData.loan_balance || 12000000)}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Loan Rate</span>
                <span className="info-value">{parcelData.loan_rate || '4.25%'}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Loan Due Date</span>
                <span className="info-value">{formatDate(parcelData.loan_due_date || '2028-08-15')}</span>
              </div>
            </div>
          </div>

          {/* Owner Information Panel */}
          <div className="info-panel owner-panel">
            <div className="panel-header">
              <div className="panel-icon owner-icon">
                <UserIcon />
              </div>
              <h3 className="panel-title">Owner Information</h3>
            </div>
            <div className="panel-content">
              <div className="owner-profile">
                <div className="owner-avatar">
                  <span>P</span>
                </div>
                <div className="owner-details">
                  <div className="owner-name">{parcelData.owner_name || 'Pacific Industrial Holdings LLC'}</div>
                </div>
              </div>
              <div className="info-row">
                <span className="info-label">Phone</span>
                <span className="info-value">{parcelData.owner_phone || '(*************'}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Email</span>
                <span className="info-value">{parcelData.owner_email || '<EMAIL>'}</span>
              </div>
            </div>
          </div>

          {/* Location Details Panel */}
          <div className="info-panel location-panel">
            <div className="panel-header">
              <div className="panel-icon location-icon">
                <MapPinIcon />
              </div>
              <h3 className="panel-title">Location Details</h3>
            </div>
            <div className="panel-content">
              <div className="info-row">
                <span className="info-label">Coordinates</span>
                <span className="info-value">{parcelData.latitude?.toFixed(6) || '34.052200'}, {parcelData.longitude?.toFixed(6) || '-118.243700'}</span>
              </div>
              <div className="info-row">
                <span className="info-label">FIPS Code</span>
                <span className="info-value">{parcelData.fips_code || '06037'}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Bottom Action Buttons */}
      <section className="bottom-actions">
        <div className="actions-container">
          <div className="action-buttons">
            {/* View on Map Button */}
            <button className="action-btn map-btn" onClick={handleViewOnMap}>
              <div className="btn-icon">
                <MapPinIcon />
              </div>
              <div className="btn-content">
                <span className="btn-title">View on Map</span>
                <span className="btn-subtitle">Open in Google Maps</span>
              </div>
            </button>

            {/* Calculate ROI Button */}
            <button className="action-btn roi-btn" onClick={handleCalculateROI}>
              <div className="btn-icon">
                <CalculatorIcon />
              </div>
              <div className="btn-content">
                <span className="btn-title">Calculate ROI</span>
                <span className="btn-subtitle">Investment Analysis</span>
              </div>
            </button>

            {/* Share Button */}
            <button className="action-btn share-btn" onClick={handleShare}>
              <div className="btn-icon">
                <ShareIcon />
              </div>
              <div className="btn-content">
                <span className="btn-title">Share</span>
                <span className="btn-subtitle">Send to others</span>
              </div>
            </button>
          </div>
        </div>
      </section>

      {/* ROI Modal */}
      {showROIModal && roiData && (
        <div className="roi-modal-overlay" onClick={() => setShowROIModal(false)}>
          <div className="roi-modal" onClick={(e) => e.stopPropagation()}>
            <div className="roi-modal-header">
              <h3>ROI Analysis</h3>
              <button className="close-btn" onClick={() => setShowROIModal(false)}>×</button>
            </div>
            <div className="roi-modal-content">
              <div className="roi-metrics-grid">
                <div className="roi-metric">
                  <span className="roi-label">Property Value</span>
                  <span className="roi-value">{formatCurrency(roiData.propertyValue)}</span>
                </div>
                <div className="roi-metric">
                  <span className="roi-label">Equity Investment</span>
                  <span className="roi-value">{formatCurrency(roiData.equityInvestment)}</span>
                </div>
                <div className="roi-metric">
                  <span className="roi-label">Annual NOI</span>
                  <span className="roi-value">{formatCurrency(roiData.annualNOI)}</span>
                </div>
                <div className="roi-metric">
                  <span className="roi-label">Annual Cash Flow</span>
                  <span className="roi-value">{formatCurrency(roiData.annualCashFlow)}</span>
                </div>
                <div className="roi-metric">
                  <span className="roi-label">Cash-on-Cash Return</span>
                  <span className="roi-value">{roiData.cashOnCashReturn}%</span>
                </div>
                <div className="roi-metric">
                  <span className="roi-label">Cap Rate</span>
                  <span className="roi-value">{roiData.capRate}%</span>
                </div>
                <div className="roi-metric">
                  <span className="roi-label">Total Return</span>
                  <span className="roi-value">{roiData.totalReturn}%</span>
                </div>
                <div className="roi-metric">
                  <span className="roi-label">Annualized Return</span>
                  <span className="roi-value">{roiData.annualizedReturn}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ParcelDetailPage
