import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import '../styles/ContactUsPage.css';

const ContactUsPage = () => {
  const { isEnglish } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    message: '',
    subject: 'general'
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // 这里将来可以连接到后端API
    console.log('Contact form submitted:', formData);
    alert(isEnglish ? 'Thank you for your message! We will get back to you soon.' : '感谢您的留言！我们会尽快回复您。');
  };

  return (
    <div className="contact-us-page">
      <div className="contact-container">
        {/* Header Section */}
        <div className="contact-header">
          <h1 className="contact-title">
            {isEnglish ? 'Contact Us' : '联系我们'}
          </h1>
          <p className="contact-subtitle">
            {isEnglish 
              ? 'Get in touch with our team to learn more about Industrial Discovery Platform'
              : '联系我们的团队，了解更多关于工业探索平台的信息'
            }
          </p>
        </div>

        <div className="contact-content">
          {/* Contact Information */}
          <div className="contact-info">
            <h3>{isEnglish ? 'Get in Touch' : '联系方式'}</h3>
            
            <div className="info-item">
              <div className="info-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z" stroke="currentColor" strokeWidth="2"/>
                  <circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <div className="info-content">
                <h4>{isEnglish ? 'Address' : '地址'}</h4>
                <p>{isEnglish ? 'Industrial Discovery Headquarters' : '工业探索总部'}</p>
                <p>{isEnglish ? 'Innovation District, Tech City' : '创新区，科技城'}</p>
              </div>
            </div>

            <div className="info-item">
              <div className="info-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M22 16.92V19.92C22 20.52 21.52 21 20.92 21C9.4 21 0 11.6 0 0.08C0 -0.52 0.48 -1 1.08 -1H4.08C4.68 -1 5.16 -0.52 5.16 0.08V3.08" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <div className="info-content">
                <h4>{isEnglish ? 'Phone' : '电话'}</h4>
                <p>+****************</p>
              </div>
            </div>

            <div className="info-item">
              <div className="info-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" strokeWidth="2"/>
                  <polyline points="22,6 12,13 2,6" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <div className="info-content">
                <h4>{isEnglish ? 'Email' : '邮箱'}</h4>
                <p><EMAIL></p>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="contact-form-container">
            <h3>{isEnglish ? 'Send us a Message' : '发送消息'}</h3>
            
            <form onSubmit={handleSubmit} className="contact-form">
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">
                    {isEnglish ? 'Full Name' : '姓名'} *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    placeholder={isEnglish ? 'Enter your full name' : '请输入您的姓名'}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email">
                    {isEnglish ? 'Email Address' : '邮箱地址'} *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    placeholder={isEnglish ? 'Enter your email' : '请输入您的邮箱'}
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="company">
                    {isEnglish ? 'Company' : '公司'}
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    placeholder={isEnglish ? 'Enter your company name' : '请输入您的公司名称'}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="phone">
                    {isEnglish ? 'Phone Number' : '电话号码'}
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder={isEnglish ? 'Enter your phone number' : '请输入您的电话号码'}
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="subject">
                  {isEnglish ? 'Subject' : '主题'}
                </label>
                <select
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                >
                  <option value="general">
                    {isEnglish ? 'General Inquiry' : '一般咨询'}
                  </option>
                  <option value="demo">
                    {isEnglish ? 'Request Demo' : '申请演示'}
                  </option>
                  <option value="partnership">
                    {isEnglish ? 'Partnership' : '合作伙伴'}
                  </option>
                  <option value="support">
                    {isEnglish ? 'Technical Support' : '技术支持'}
                  </option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="message">
                  {isEnglish ? 'Message' : '消息'} *
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows="5"
                  placeholder={isEnglish ? 'Tell us about your project or inquiry...' : '告诉我们您的项目或咨询内容...'}
                />
              </div>

              <button type="submit" className="submit-btn">
                {isEnglish ? 'Send Message' : '发送消息'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactUsPage;
