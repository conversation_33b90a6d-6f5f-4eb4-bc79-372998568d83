{"name": "industrialgeodev", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "NPM_CONFIG_LOGLEVEL=error vite --host", "start": "NPM_CONFIG_LOGLEVEL=error vite --host", "build": "NPM_CONFIG_LOGLEVEL=error vite build", "lint": "NPM_CONFIG_LOGLEVEL=error eslint .", "preview": "NPM_CONFIG_LOGLEVEL=error vite preview --host", "backend:dev": "NPM_CONFIG_LOGLEVEL=error cd backend-server && nodemon server.cjs", "dev:all": "NPM_CONFIG_LOGLEVEL=error concurrently \"npm run dev\" \"npm run backend:dev\"", "clean": "rm -rf node_modules package-lock.json", "reinstall": "npm run clean && npm cache clean --force && NPM_CONFIG_LOGLEVEL=error npm install", "monitor": "cd backend-server && node monitor-system.js start", "monitor:check": "cd backend-server && node monitor-system.js check", "check-env": "NPM_CONFIG_LOGLEVEL=error node check-env.cjs", "check-backend": "NPM_CONFIG_LOGLEVEL=error node backend-health-check.cjs"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@react-three/postprocessing": "^3.0.4", "@turf/turf": "^7.2.0", "animejs": "^3.2.2", "chart.js": "^4.4.0", "clsx": "^2.1.1", "d3": "^7.9.0", "fdir": "^6.4.6", "framer-motion": "^12.19.1", "gsap": "^3.13.0", "i18next": "^24.2.3", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet-geometryutil": "^0.10.3", "leaflet.markercluster": "^1.5.3", "liquid-glass-react": "^1.1.1", "lucide-react": "^0.523.0", "mapbox-gl": "^3.10.0", "ogl": "^1.0.11", "papaparse": "^5.5.2", "react": "^19.0.0", "react-chartjs-2": "^5.2.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-leaflet": "^5.0.0", "react-leaflet-markercluster": "^5.0.0-rc.0", "react-modal": "^3.16.3", "react-router-dom": "^7.5.3", "recharts": "^2.15.1", "three": "^0.176.0", "three-stdlib": "^2.36.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@stagewise/toolbar-react": "^0.6.2", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "eslint": "^9.21.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "nodemon": "^3.1.10", "tailwindcss": "^4.1.7", "vite": "^6.3.5"}, "optionalDependencies": {"@rollup/rollup-darwin-arm64": "^4.45.1", "@rollup/rollup-darwin-x64": "^4.45.1"}}