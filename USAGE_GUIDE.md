# 底部功能按钮使用指南

## 概述

我已经成功在 ParcelDetailPage 底部添加了三个功能按钮，完全按照您提供的设计图实现：

1. **View on Map** (查看地图) - 蓝色按钮
2. **Calculate ROI** (计算投资回报率) - 绿色按钮
3. **Share** (分享) - 紫色按钮

## 如何测试新功能

### 1. 启动应用程序
```bash
cd /Users/<USER>/Desktop/IndustrialGeoDev
npm start
```

应用程序将在 http://localhost:5174 启动

### 2. 导航到地块详情页面
访问任何地块详情页面，例如：
- http://localhost:5174/parcel/5591-001-001
- http://localhost:5174/parcel/5591-001-002
- http://localhost:5174/parcel/5591-001-003

### 3. 测试功能按钮

#### View on Map (查看地图)
- 点击蓝色的 "View on Map" 按钮
- 将在新窗口中打开 Google Maps
- 显示地块的精确位置（基于经纬度坐标）

#### Calculate ROI (计算投资回报率)
- 点击绿色的 "Calculate ROI" 按钮
- 打开 ROI 分析模态框
- 显示详细的投资分析指标：
  - Property Value (物业价值)
  - Equity Investment (股权投资)
  - Annual NOI (年度净营业收入)
  - Annual Cash Flow (年度现金流)
  - Cash-on-Cash Return (现金回报率)
  - Cap Rate (资本化率)
  - Total Return (总回报率)
  - Annualized Return (年化回报率)

#### Share (分享)
- 点击紫色的 "Share" 按钮
- 如果浏览器支持原生分享 API，将打开分享对话框
- 否则将复制页面链接到剪贴板并显示提示

## ROI 计算说明

### 数据来源
ROI 计算使用地块的实际数据：
- 建筑面积 (building_area)
- 净营业收入 (noi)
- 最新销售价格 (latest_sale_price)
- 年度税费 (annual_taxes)

### 计算假设
- 租金: $8.50/平方英尺/年
- 运营费用比率: 25%
- 资本化率: 6.5%
- 持有期: 10年
- 退出资本化率: 7%
- 贷款价值比: 75%
- 利率: 4.5%
- 贷款期限: 25年

### 计算指标
1. **Property Value**: 基于 NOI 和资本化率计算
2. **Equity Investment**: 物业价值减去贷款金额
3. **Annual Cash Flow**: NOI 减去年度债务服务
4. **Cash-on-Cash Return**: 年度现金流除以股权投资
5. **Total Return**: 总现金流减去初始投资的回报率
6. **Annualized Return**: 年化投资回报率

## 响应式设计

### 桌面端 (>768px)
- 三个按钮水平排列
- 矩形按钮，图标和文字水平排列
- 蓝色、绿色、紫色三种颜色主题

### 移动端 (≤768px)
- 按钮垂直堆叠
- 保持水平布局（图标在左，文字在右）
- 调整按钮高度和间距

### 小屏幕 (≤480px)
- 进一步优化间距和字体大小
- 图标尺寸适配小屏幕
- 保持良好的可用性

## 技术实现

### 核心文件
1. **ParcelDetailPage.jsx**: 主要组件逻辑
2. **ParcelDetailPage.css**: 样式定义
3. **roiCalculator.test.js**: 测试文件

### 关键特性
- 矩形按钮设计，匹配提供的截图
- 三种颜色主题：蓝色、绿色、紫色
- 图标和文字水平排列
- 平滑的悬停动画
- 模态框交互
- 错误处理
- 响应式布局

## 故障排除

### 如果按钮不显示
1. 确保应用程序正在运行
2. 检查浏览器控制台是否有错误
3. 确保 CSS 文件正确加载

### 如果 ROI 计算不工作
1. 检查地块数据是否加载完成
2. 查看浏览器控制台的错误信息
3. 确保数据格式正确

### 如果分享功能不工作
1. 检查浏览器是否支持 Clipboard API
2. 确保页面在 HTTPS 或 localhost 上运行
3. 检查浏览器权限设置

## 下一步改进

1. **自定义参数**: 允许用户调整 ROI 计算参数
2. **导出功能**: 支持导出 ROI 分析报告为 PDF
3. **比较功能**: 支持多个地块的 ROI 比较
4. **历史数据**: 集成历史价格和租金趋势
5. **敏感性分析**: 添加不同情景下的风险评估
