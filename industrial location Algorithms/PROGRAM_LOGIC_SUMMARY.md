# Industrial Discovery Inc. - Program Logic Summary

This document provides a comprehensive overview of the program logic for each branch of the Industrial Discovery Inc. industrial site selection system.

## 📋 System Overview

The Industrial Discovery Inc. system is designed to provide **monetized financial analysis** for industrial real estate investments, moving beyond abstract scores to deliver actual dollar values for investment decisions.

### Core Philosophy
- **Dollar-driven decisions**: All outputs are in real monetary values (NPV, IRR, cash flows)
- **Investment-grade analysis**: Professional financial metrics used by real estate investors
- **Risk quantification**: Stress testing and sensitivity analysis with dollar impacts
- **Comprehensive workflow**: From site discovery to deal closure

---

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Clients   │────│    Services     │────│   CLI/Main      │
│  (Data Layer)   │    │ (Business Logic)│    │ (User Interface)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────│     Models      │──────────────┘
                        │  (Domain Data)  │
                        └─────────────────┘
                                 │
                    ┌─────────────────────────────┐
                    │   Pure Logic Modules        │
                    │ (prospecting, underwriting, │
                    │  deal_closure)              │
                    └─────────────────────────────┘
```

---

## 1️⃣ API Clients (`api_clients/`)

**Purpose**: Uniform interface layer for all external data sources with fallback to mock data.

### Program Logic Flow:

#### 1.1 RegridClient (`regrid_client.py`)
```python
# Logic Flow:
1. Initialize with API key
2. Rate limiting (0.5 second intervals)
3. Make authenticated requests to Regrid API
4. Parse parcel data into Parcel models
5. Fallback to mock data if API fails

# Key Methods:
- fetch_parcel(parcel_id) → Parcel object
- search_parcels(lat, lng, radius) → List[Parcel]
- get_parcel_boundaries() → Boundary data
```

#### 1.2 AttomClient (`attom_client.py`)
```python
# Logic Flow:
1. Property and zoning data retrieval
2. Parse Attom API responses
3. Extract zoning codes, restrictions, setbacks
4. Return ZoningData objects with building limitations

# Key Output:
- Zoning compliance data
- Building restrictions (height, coverage, FAR)
- Setback requirements for development planning
```

#### 1.3 BLSClient (`bls_client.py`)
```python
# Logic Flow:
1. Bureau of Labor Statistics API integration
2. Fetch employment, wage, and labor force data
3. Calculate workforce metrics by geographic area
4. Return LaborMetrics with cost implications

# Key Calculations:
- Average wages by skill level
- Unemployment rates
- Labor force availability
- Industry-specific wage data
```

#### 1.4 EIAClient (`eia_client.py`)
```python
# Logic Flow:
1. Energy Information Administration API
2. Retrieve electricity and gas rates by region
3. Calculate utility cost factors
4. Return EnergyRates for operating cost modeling

# Key Data:
- $/kWh electricity rates
- Natural gas prices
- Regional energy cost variations
```

#### 1.5 CensusClient (`census_client.py`)
```python
# Logic Flow:
1. US Census Bureau demographic data
2. American Community Survey (ACS) integration
3. Parse population, income, education data
4. Calculate workforce quality scores

# Key Metrics:
- Population density
- Median household income
- Education levels (affects labor quality)
- Demographics for market analysis
```

#### 1.6 IncentivesClient (`incentives_client.py`)
```python
# Logic Flow:
1. Tax incentive database integration
2. Location-specific incentive lookup
3. Calculate actual dollar savings
4. Return IncentiveData with financial benefits

# Key Calculations:
- Property tax abatements ($/year)
- Job creation credits ($2,500/job)
- Investment tax credits (% of investment)
- Training grants and utility rebates
```

#### 1.7 DealpathClient (`dealpath_client.py`)
```python
# Logic Flow:
1. Deal management system integration
2. CRUD operations for deals, tasks, documents
3. Pipeline stage management
4. Return Deal objects with workflow status
```

#### 1.8 ProprietaryDBClient (`proprietary_db_client.py`)
```python
# Logic Flow:
1. SQLite database for internal data storage
2. Store parcels, analysis results, deals
3. Search and retrieval with criteria filtering
4. Analysis history tracking
```

### Error Handling Strategy:
- All clients gracefully fallback to mock data
- Rate limiting prevents API quota exhaustion
- Retry logic for transient failures
- Structured error logging

---

## 2️⃣ Domain Models (`models/`)

**Purpose**: Type-safe domain entities using Pydantic for validation and serialization.

### Program Logic:

#### 2.1 Parcel (`parcel.py`)
```python
# Core Entity representing industrial property
class Parcel:
    # Geographic data
    lat, lng, address, city, state, zip_code
    
    # Property characteristics
    lot_size, land_use, zoning_code
    assessed_value, market_value
    
    # Ownership and prospecting data
    owner_name, owner_contact
    exclusive_flag, prospecting_notes
    
    # Utility methods
    def calculate_lot_value_per_sqft()
    def is_suitable_for_industrial()
    def get_owner_motivation_score()
```

#### 2.2 LaborMetrics (`labor.py`)
```python
# Workforce cost analysis
class LaborMetrics:
    # Wage data by skill level
    average_wage, skilled_wage, entry_wage
    
    # Market conditions
    unemployment_rate, labor_force_count
    
    # Cost calculation methods
    def calculate_annual_labor_cost(employees: int)
    def get_wage_competitiveness()
```

#### 2.3 EnergyRates (`energy.py`)
```python
# Utility cost modeling
class EnergyRates:
    electricity_rate, gas_rate, water_rate
    
    # Cost projections
    def calculate_annual_energy_cost(sqft: int, industry: str)
    def project_utility_savings(efficiency_factor: float)
```

#### 2.4 IncentiveData (`incentives.py`)
```python
# Tax benefit calculations
class IncentiveData:
    property_tax_rate, sales_tax_rate
    abatement_years, abatement_percentage
    
    # Financial benefit methods
    def calculate_total_savings(property_value, payroll, employees)
    def get_abatement_value(assessment: float) → float
```

#### 2.5 Deal (`deal.py`)
```python
# Transaction workflow management
class Deal:
    # Deal identification
    id, name, parcel_id, deal_type
    
    # Financial terms
    purchase_price, financing_amount, deal_value
    
    # Workflow management
    current_stage: DealStage (enum)
    tasks: List[Task]
    documents: List[Document]
    
    # Stage progression logic
    def update_stage(new_stage: DealStage)
    def calculate_completion_percentage()
```

---

## 3️⃣ Services (`services/`)

**Purpose**: High-level business workflows that orchestrate API clients and domain logic.

### Program Logic Flow:

#### 3.1 ProspectingService (`prospecting_service.py`)
```python
# Monetized site analysis workflow
def run_prospecting(lat, lng, radius, industry) → MonetizedProspectResult:
    
    # 1. Data Collection Phase
    parcels = regrid_client.search_parcels(lat, lng, radius)
    labor_data = bls_client.get_labor_data(lat, lng)
    energy_data = eia_client.get_energy_rates(lat, lng)
    transport_data = maps_client.calculate_transportation_metrics(lat, lng)
    demographics = census_client.get_demographics(lat, lng)
    incentives = incentives_client.get_incentives(lat, lng, industry)
    
    # 2. Parcel Enhancement
    enriched_parcel = enrich_owner_data(parcel)
    
    # 3. Strategic Analysis
    strategic_overlay = calculate_strategic_overlay(
        parcel, labor_data, energy_data, transport_data, demographics
    )
    
    # 4. Monetized Value Calculation
    return _calculate_monetized_value(
        parcel, labor_data, energy_data, transport_data, 
        demographics, incentives, strategic_overlay, industry
    )

# Key Financial Calculations:
def _calculate_monetized_value():
    # Market value estimation
    estimated_market_value = base_land_value_psf * lot_size
    
    # Development cost modeling
    development_cost_estimate = building_size * construction_cost_psf
    
    # Revenue projections
    potential_rental_income = building_size * market_rent_psf
    
    # Value driver analysis
    location_value_premium = market_value * location_premium_factor * 0.15
    transportation_cost_savings = transport_savings * 10_year_npv
    labor_cost_advantage = wage_differential * employees * 10_years
    energy_cost_savings = electricity_savings * kwh_usage * 10_years
    
    # Investment metrics
    roi_percent = total_benefits / acquisition_cost * 100
    npv = calculate_npv(annual_net_income, sale_value, discount_rate)
    
    return MonetizedProspectResult(
        # All dollar amounts, no abstract scores
        estimated_market_value, cost_savings_potential,
        revenue_enhancement_potential, acquisition_cost,
        roi_percent, payback_period_years, net_present_value
    )
```

#### 3.2 UnderwritingService (`underwriting_service.py`)
```python
# Comprehensive financial analysis workflow
def run_underwriting(parcel_id) → UnderwritingResult:
    
    # 1. Data Assembly
    parcel = regrid_client.fetch_parcel(parcel_id)
    labor_data = bls_client.fetch_labor_stats(parcel.location)
    energy_data = eia_client.fetch_energy_rates(parcel.state)
    demographics = census_client.fetch_demographics(parcel.tract)
    incentives = incentives_client.fetch_incentives(parcel_id)
    
    # 2. Input Normalization
    inputs = merge_inputs(parcel, labor, energy, demographics, incentives)
    
    # 3. Financial Modeling
    cashflow_result = compute_cashflow(inputs)
    ratios_result = compute_ratios(cashflow_result)
    sensitivity_result = run_stress_tests(inputs)
    
    # 4. Tax Benefit Analysis
    tax_benefits = incentives_client.calculate_incentive_value(
        incentives, project_details
    )
    
    # 5. Investment Recommendation
    recommendation = _generate_recommendation(cashflow_result, ratios_result)
    
    return UnderwritingResult(
        # Professional real estate metrics
        npv, irr, cash_on_cash_return, dscr,
        best_case_npv, worst_case_npv, recommendation
    )
```

#### 3.3 DealClosureService (`deal_closure_service.py`)
```python
# Transaction management workflow
def run_deal_closure(deal_id) → DealClosureResult:
    
    # 1. Deal Data Retrieval
    deal = dealpath_client.fetch_deal(deal_id)
    
    # 2. Pipeline Management
    updated_deal = advance_stage(deal)
    
    # 3. Task Synchronization
    tasks = sync_tasks(updated_deal)
    
    # 4. Document Generation
    documents = generate_docs(updated_deal)
    
    # 5. Compliance Verification
    compliance_result = verify_compliance(updated_deal)
    
    # 6. Progress Calculation
    completion_pct = _calculate_completion(tasks)
    
    # 7. Next Actions Generation
    next_actions = _generate_next_actions(deal, tasks, compliance_result)
    
    return DealClosureResult(
        current_stage, completion_percentage,
        pending_tasks, next_actions, compliance_status
    )
```

---

## 4️⃣ Prospecting Logic (`prospecting/`)

**Purpose**: Pure logic functions for parcel discovery and strategic value assessment.

### Program Logic:

#### 4.1 Parcel Prospecting (`parcel_prospecting.py`)
```python
# Owner data enrichment
def enrich_owner_data(parcel: Parcel) → Parcel:
    # Mock owner contact enhancement
    # In production: integrate with skip tracing services
    
    parcel.owner_contact = {
        "email": generate_owner_email(parcel.owner_name),
        "phone": generate_owner_phone(),
        "motivation_score": assess_owner_motivation(parcel)
    }
    
    return parcel

def assess_owner_motivation(parcel: Parcel) → float:
    # Analyze factors indicating seller motivation
    # - Property holding period
    # - Tax assessment vs market value
    # - Recent comparable sales
    return motivation_score
```

#### 4.2 Strategic Value (`strategic_value.py`)
```python
# Comprehensive location analysis
def calculate_strategic_overlay(parcel, labor, energy, transport, demographics):
    
    # Location scoring
    location_analysis = {
        "highway_access": calculate_highway_proximity(parcel),
        "port_distance": calculate_port_access(parcel),
        "airport_proximity": calculate_airport_distance(parcel),
        "total_score": weighted_location_score
    }
    
    # Transportation efficiency
    transportation_analysis = {
        "logistics_efficiency": calculate_logistics_score(transport),
        "shipping_costs": estimate_shipping_costs(transport),
        "multimodal_access": assess_transport_modes(parcel),
        "total_score": weighted_transport_score
    }
    
    # Infrastructure assessment
    infrastructure_analysis = {
        "utilities_capacity": assess_utility_infrastructure(parcel),
        "road_quality": evaluate_road_conditions(parcel),
        "telecom_infrastructure": assess_connectivity(parcel),
        "total_score": weighted_infrastructure_score
    }
    
    # Market context
    market_analysis = {
        "industrial_density": calculate_industrial_cluster_effect(parcel),
        "labor_market_depth": assess_labor_availability(demographics),
        "supplier_ecosystem": evaluate_supplier_proximity(parcel),
        "total_score": weighted_market_score
    }
    
    # Risk assessment
    risk_analysis = {
        "environmental_risk": assess_environmental_factors(parcel),
        "regulatory_risk": evaluate_zoning_stability(parcel),
        "market_risk": assess_demand_volatility(parcel),
        "total_score": weighted_risk_score
    }
    
    return {
        "location": location_analysis,
        "transportation": transportation_analysis,
        "infrastructure": infrastructure_analysis,
        "market": market_analysis,
        "risk": risk_analysis
    }
```

#### 4.3 Negotiate Listing (`negotiate_listing.py`)
```python
# Exclusive listing evaluation
def evaluate_exclusive_listing(parcel: Parcel, strategic_overlay: Dict):
    
    # Exclusive listing criteria
    criteria_met = 0
    total_criteria = 5
    
    # High strategic value
    if strategic_overlay["location"]["total_score"] > 75:
        criteria_met += 1
    
    # Owner motivation
    if parcel.owner_contact and parcel.owner_contact["motivation_score"] > 0.7:
        criteria_met += 1
    
    # Market opportunity
    if strategic_overlay["market"]["total_score"] > 70:
        criteria_met += 1
    
    # Size threshold
    if parcel.lot_size and parcel.lot_size > 100000:  # 100k+ sqft
        criteria_met += 1
    
    # Investment potential
    estimated_value = calculate_estimated_value(parcel, strategic_overlay)
    if estimated_value > 5000000:  # $5M+ value
        criteria_met += 1
    
    # Decision logic
    exclusive_worthy = criteria_met >= 3
    
    if exclusive_worthy:
        parcel.exclusive_flag = True
        parcel.exclusive_timestamp = datetime.now()
    
    return {
        "exclusive_recommended": exclusive_worthy,
        "criteria_score": f"{criteria_met}/{total_criteria}",
        "estimated_value": estimated_value,
        "target_buyers": identify_target_buyers(parcel, strategic_overlay),
        "outreach_sequence": plan_outreach_strategy(parcel)
    }
```

---

## 5️⃣ Underwriting Logic (`underwriting/`)

**Purpose**: Professional real estate financial modeling with institutional-grade analysis.

### Program Logic:

#### 5.1 Input Normalization (`normalize_inputs.py`)
```python
# Standardize all inputs for financial modeling
def merge_inputs(parcel, labor, energy, demographics, incentives):
    
    # Property metrics
    lot_size_sqft = parcel.lot_size or 250000
    building_size_sqft = lot_size_sqft * 0.5  # 50% coverage
    market_value = parcel.market_value or parcel.assessed_value
    
    # Development costs
    land_cost = market_value
    construction_cost_per_sqft = 85  # Industrial construction
    development_cost = building_size_sqft * construction_cost_per_sqft
    soft_costs = development_cost * 0.15  # 15% soft costs
    financing_costs = total_cost * 0.03  # 3% financing fees
    
    # Revenue modeling
    base_rent_per_sqft = _calculate_market_rent(parcel, demographics)
    annual_rent_escalation = 0.025  # 2.5% annual increases
    
    # Operating expenses
    property_taxes = (land_cost + development_cost) * incentives.property_tax_rate
    insurance = building_size_sqft * 0.25
    utilities = building_size_sqft * energy.electricity_rate * 12 * 50
    maintenance = building_size_sqft * 1.50
    management = rental_income * 0.05
    
    # Financing assumptions
    loan_to_cost = 0.75  # 75% LTC
    loan_amount = total_project_cost * loan_to_cost
    interest_rate = 0.065  # 6.5%
    loan_term_years = 25
    
    return normalized_inputs_dict
```

#### 5.2 Cash Flow Calculation (`calc_cashflow.py`)
```python
# Professional cash flow modeling
def compute_cashflow(inputs: Dict) → Dict:
    
    # Extract parameters
    building_size = inputs["property"]["building_size_sqft"]
    base_rent = inputs["revenue"]["base_rent_per_sqft"]
    hold_period = inputs["assumptions"]["hold_period_years"]
    
    # Calculate debt service
    annual_debt_service = -npf.pmt(interest_rate, loan_term, loan_amount)
    
    # Project cash flows
    cash_flows = []
    for year in range(1, hold_period + 1):
        # Rental income with escalation
        annual_rent = base_rent * (1 + rent_escalation) ** (year - 1)
        rental_income = building_size * annual_rent
        
        # Net Operating Income
        noi = rental_income - total_operating_expenses
        
        # Cash flow after debt service
        cash_flow = noi - annual_debt_service
        cash_flows.append(cash_flow)
    
    # Exit value calculation
    final_year_noi = noi_by_year[-1]
    exit_value = final_year_noi / exit_cap_rate
    remaining_balance = npf.fv(interest_rate, hold_period, -annual_debt_service, loan_amount)
    net_exit_proceeds = exit_value - abs(remaining_balance)
    
    # Financial metrics
    initial_investment = total_project_cost - loan_amount
    total_cash_flows = cash_flows[:-1] + [cash_flows[-1] + net_exit_proceeds]
    
    npv = npf.npv(discount_rate, [-initial_investment] + total_cash_flows)
    irr = npf.irr([-initial_investment] + total_cash_flows)
    
    return {
        "npv": npv,
        "irr": irr,
        "annual_cash_flows": cash_flows,
        "total_project_cost": total_project_cost,
        "initial_investment": initial_investment,
        "exit_value": exit_value,
        "net_exit_proceeds": net_exit_proceeds
    }
```

#### 5.3 Financial Ratios (`calc_ratios.py`)
```python
# Key performance indicators
def compute_ratios(cashflow_result: Dict) → Dict:
    
    # Cash-on-Cash Return (Year 1)
    year_1_cash_flow = annual_cash_flows[0]
    cash_on_cash_return = year_1_cash_flow / initial_investment
    
    # Debt Service Coverage Ratio
    dscr_values = []
    for cash_flow in annual_cash_flows:
        noi = cash_flow + annual_debt_service
        dscr = noi / annual_debt_service
        dscr_values.append(dscr)
    
    # Cap Rate
    cap_rate = year_1_noi / total_project_cost
    
    # Payback Period
    cumulative_cash_flow = 0
    for i, cash_flow in enumerate(annual_cash_flows):
        cumulative_cash_flow += cash_flow
        if cumulative_cash_flow >= initial_investment:
            payback_period = i + 1
            break
    
    # Equity Multiple
    total_cash_received = sum(annual_cash_flows) + net_exit_proceeds
    equity_multiple = total_cash_received / initial_investment
    
    return {
        "cash_on_cash_return": cash_on_cash_return,
        "min_dscr": min(dscr_values),
        "average_dscr": sum(dscr_values) / len(dscr_values),
        "cap_rate": cap_rate,
        "payback_period": payback_period,
        "equity_multiple": equity_multiple
    }
```

#### 5.4 Sensitivity Analysis (`sensitivity.py`)
```python
# Risk assessment and stress testing
def run_stress_tests(inputs: Dict) → Dict:
    
    base_case = compute_cashflow(inputs)
    base_npv = base_case["npv"]
    
    # Stress test scenarios
    scenarios = {
        "rent_down_10": _modify_inputs(inputs, {"rent_adjustment": -0.10}),
        "rent_down_20": _modify_inputs(inputs, {"rent_adjustment": -0.20}),
        "expenses_up_15": _modify_inputs(inputs, {"expense_adjustment": 0.15}),
        "interest_up_200bp": _modify_inputs(inputs, {"interest_rate_adjustment": 0.02}),
        "construction_cost_up_25": _modify_inputs(inputs, {"construction_cost_adjustment": 0.25})
    }
    
    # Calculate NPV impact for each scenario
    scenario_results = {}
    for scenario_name, scenario_inputs in scenarios.items():
        scenario_cashflow = compute_cashflow(scenario_inputs)
        scenario_results[scenario_name] = scenario_cashflow["npv"]
    
    # Best/worst case analysis
    best_case = compute_cashflow(_modify_inputs(inputs, {
        "rent_adjustment": 0.15,
        "expense_adjustment": -0.10
    }))
    
    worst_case = compute_cashflow(_modify_inputs(inputs, {
        "rent_adjustment": -0.20,
        "expense_adjustment": 0.20,
        "interest_rate_adjustment": 0.02
    }))
    
    # Break-even analysis
    break_even_rent = _calculate_break_even_rent(inputs)
    
    return {
        "base_case_npv": base_npv,
        "best_case_npv": best_case["npv"],
        "worst_case_npv": worst_case["npv"],
        "npv_range": best_case["npv"] - worst_case["npv"],
        "break_even_rent": break_even_rent,
        "scenario_results": scenario_results
    }
```

---

## 6️⃣ Deal Closure Logic (`deal_closure/`)

**Purpose**: Transaction workflow management with compliance tracking.

### Program Logic:

#### 6.1 Pipeline Management (`pipeline.py`)
```python
# Deal stage progression logic
def advance_stage(deal: Deal) → Deal:
    
    current_stage = deal.current_stage
    
    # Stage advancement criteria
    if current_stage == DealStage.LEAD:
        if _is_qualified(deal):  # Has buyer, purchase price
            deal.current_stage = DealStage.QUALIFIED
    
    elif current_stage == DealStage.QUALIFIED:
        if _has_proposal(deal):  # Terms proposed
            deal.current_stage = DealStage.PROPOSAL
    
    elif current_stage == DealStage.DUE_DILIGENCE:
        if _due_diligence_complete(deal):  # All DD tasks done
            deal.current_stage = DealStage.FINANCING
    
    elif current_stage == DealStage.FINANCING:
        if _financing_approved(deal):  # Loan approved
            deal.current_stage = DealStage.CLOSING
    
    elif current_stage == DealStage.CLOSING:
        if _ready_to_close(deal):  # All tasks complete
            deal.current_stage = DealStage.CLOSED
            deal.actual_closing_date = datetime.now()
    
    # Update probability based on stage
    deal.update_stage(deal.current_stage)
    
    return deal
```

#### 6.2 Task Management (`tasks.py`)
```python
# Dynamic task creation and tracking
def sync_tasks(deal: Deal) → List[Task]:
    
    existing_tasks = deal.tasks.copy()
    required_tasks = _get_required_tasks_for_stage(deal.current_stage)
    
    # Add missing tasks for current stage
    for task_name in required_tasks:
        if not any(task.name == task_name for task in existing_tasks):
            new_task = Task(
                name=task_name,
                status=TaskStatus.PENDING,
                due_date=datetime.now() + timedelta(days=14)
            )
            existing_tasks.append(new_task)
    
    deal.tasks = existing_tasks
    return existing_tasks

# Stage-specific task templates
def _get_required_tasks_for_stage(stage: DealStage):
    task_templates = {
        DealStage.DUE_DILIGENCE: [
            "Phase I Environmental Assessment",
            "Title Review",
            "Survey Review",
            "Zoning Verification",
            "Physical Inspection"
        ],
        DealStage.FINANCING: [
            "Loan Application",
            "Appraisal",
            "Loan Approval",
            "Loan Documents"
        ],
        DealStage.CLOSING: [
            "Final Walkthrough",
            "Closing Documents Review",
            "Wire Transfer Setup",
            "Title Insurance",
            "Recording"
        ]
    }
    return task_templates.get(stage, [])
```

#### 6.3 Document Management (`documents.py`)
```python
# Automated document generation
def generate_docs(deal: Deal) → List[Document]:
    
    existing_docs = deal.documents.copy()
    required_docs = _get_required_documents_for_stage(deal.current_stage)
    
    # Generate missing documents
    for doc_name, doc_type in required_docs:
        if not any(doc.name == doc_name for doc in existing_docs):
            new_doc = Document(
                name=doc_name,
                document_type=doc_type,
                created_date=datetime.now()
            )
            existing_docs.append(new_doc)
    
    deal.documents = existing_docs
    return existing_docs
```

#### 6.4 Compliance Verification (`compliance.py`)
```python
# Regulatory compliance checking
def verify_compliance(deal: Deal) → Dict[str, Any]:
    
    compliance_checks = []
    issues = []
    
    # Environmental compliance
    if deal.current_stage in [DealStage.DUE_DILIGENCE, DealStage.CLOSING]:
        env_check = _check_environmental_compliance(deal)
        compliance_checks.append(env_check)
        if not env_check["passed"]:
            issues.extend(env_check["issues"])
    
    # Financial compliance
    if deal.current_stage in [DealStage.FINANCING, DealStage.CLOSING]:
        financial_check = _check_financial_compliance(deal)
        compliance_checks.append(financial_check)
        if not financial_check["passed"]:
            issues.extend(financial_check["issues"])
    
    # Overall status
    all_passed = all(check["passed"] for check in compliance_checks)
    status = "compliant" if all_passed else "non_compliant"
    
    return {
        "status": status,
        "checks_performed": len(compliance_checks),
        "issues": issues,
        "compliance_checks": compliance_checks
    }
```

---

## 7️⃣ Utilities (`utils/`)

**Purpose**: Configuration management and logging infrastructure.

### Program Logic:

#### 7.1 Configuration (`config.py`)
```python
# YAML configuration loading with environment overrides
def load_config(config_path: str) → Dict[str, Any]:
    
    # Load base configuration from YAML
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Override with environment variables
    for section, values in config.items():
        if isinstance(values, dict):
            for key, value in values.items():
                env_var = f"{section.upper()}_{key.upper()}"
                if env_var in os.environ:
                    config[section][key] = os.environ[env_var]
    
    # Validate required configuration
    _validate_config(config)
    
    return config

def get_api_key(config: Dict, service: str) → Optional[str]:
    return config.get("api_keys", {}).get(service)
```

#### 7.2 Logging (`logging.py`)
```python
# Structured logging setup
def setup_logging(verbose: bool = False):
    
    # Configure structlog for structured JSON logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=logging.DEBUG if verbose else logging.INFO,
    )
    
    structlog.configure(
        processors=[
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.add_log_level,
            structlog.processors.JSONRenderer()
        ],
        wrapper_class=structlog.make_filtering_bound_logger(logging.INFO),
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    return structlog.get_logger()
```

---

## 8️⃣ CLI Interface (`main.py`)

**Purpose**: User-friendly command-line interface for all system operations.

### Program Logic:

#### 8.1 Main CLI Structure
```python
# Click-based CLI with context management
@click.group()
@click.option('--config', '-c', type=click.Path(exists=True))
@click.option('--verbose', '-v', is_flag=True)
@click.pass_context
def cli(ctx, config, verbose):
    # Load configuration
    if config:
        ctx.obj['config'] = load_config(config)
    else:
        # Try default config or use empty dict
        ctx.obj['config'] = load_config("config.yaml") if Path("config.yaml").exists() else {}
    
    # Setup logging
    setup_logging(verbose)
```

#### 8.2 Prospect Command
```python
@cli.command()
@click.option('--latitude', type=float, required=True)
@click.option('--longitude', type=float, required=True)
@click.option('--radius', type=float, default=5.0)
@click.option('--industry', default="manufacturing")
def prospect(ctx, latitude, longitude, radius, industry, output):
    
    # Initialize service
    prospecting_service = ProspectingService(ctx.obj['config'])
    
    # Run analysis with progress bar
    with click.progressbar(length=100, label="Analyzing site potential") as bar:
        result = prospecting_service.run_prospecting(
            latitude, longitude, radius, industry
        )
        bar.update(80)
    
    # Display monetized results
    click.echo("💰 MONETIZED INVESTMENT ANALYSIS")
    click.echo(f"Market Value: ${result.estimated_market_value:,.0f}")
    click.echo(f"NPV: ${result.net_present_value:,.0f}")
    click.echo(f"ROI: {result.estimated_roi_percent:.1f}%")
    
    # Investment recommendation based on financial metrics
    if result.net_present_value > 0 and result.estimated_roi_percent > 12:
        click.echo("🎯 RECOMMENDATION: STRONG BUY")
    elif result.net_present_value > 0:
        click.echo("🤔 RECOMMENDATION: CONSIDER")
    else:
        click.echo("❌ RECOMMENDATION: PASS")
```

#### 8.3 Underwrite Command
```python
@cli.command()
@click.option('--parcel-id', required=True)
@click.option('--purchase-price', type=float)
def underwrite(ctx, parcel_id, purchase_price, loan_amount, hold_period):
    
    underwriting_service = UnderwritingService(ctx.obj['config'])
    
    # Run comprehensive financial analysis
    result = underwriting_service.run_underwriting(
        parcel_id, purchase_price, loan_amount, hold_period
    )
    
    # Display professional real estate metrics
    click.echo("📊 FINANCIAL UNDERWRITING ANALYSIS")
    click.echo(f"NPV: ${result.npv:,.0f}")
    click.echo(f"IRR: {result.irr:.2%}")
    click.echo(f"Cash-on-Cash: {result.cash_on_cash_return:.2%}")
    click.echo(f"DSCR: {result.min_dscr:.2f}")
    
    # Investment grade recommendation
    if result.recommendation == "STRONG BUY":
        click.echo("🎯 RECOMMENDATION: STRONG BUY")
    elif result.recommendation == "BUY":
        click.echo("🤔 RECOMMENDATION: BUY") 
    else:
        click.echo("❌ RECOMMENDATION: REJECT")
```

---

## 🔄 System Integration Flow

### Complete Workflow Example:

```python
# 1. Site Discovery
prospect_result = prospecting_service.run_prospecting(32.7767, -96.7970, 5.0, "manufacturing")
# Returns: $16.8M total investment, $2.8M NPV, 15.2% ROI

# 2. Financial Underwriting  
underwriting_result = underwriting_service.run_underwriting("parcel_123")
# Returns: Grade A investment, 14.5% IRR, 1.35 DSCR

# 3. Deal Management
deal_result = deal_closure_service.run_deal_closure("deal_456")
# Returns: Due diligence stage, 60% complete, 5 pending tasks

# 4. Decision Making
if prospect_result.net_present_value > 0 and underwriting_result.irr > 0.12:
    proceed_with_acquisition = True
    initiate_deal_workflow(parcel_id)
```

---

## 💡 Key Design Principles

### 1. **Monetized Results**
- All outputs are in actual dollar amounts
- No abstract scores or percentages without context
- Investment-grade financial metrics (NPV, IRR, DSCR)

### 2. **Modularity**
- Pure functions with clear inputs/outputs
- API clients isolated from business logic
- Services orchestrate but don't contain logic

### 3. **Fault Tolerance**
- Mock data fallbacks for all API failures
- Graceful degradation of functionality
- Comprehensive error handling

### 4. **Professional Standards**
- Real estate industry standard calculations
- Institutional-grade financial modeling
- Compliance tracking and verification

### 5. **Scalability**
- Stateless service design
- Database abstraction layer
- Rate limiting and resource management

---

This system transforms abstract "strategic scores" into concrete **dollar-based investment decisions**, providing the financial clarity needed for industrial real estate acquisition and development. 