# Parcel Prospector Module Specification

## Underwriting (underwriting.py)

- Estimates Loan-to-Value (LTV) by comparing mortgages[0].original_loan_amount with the most recent sale_price from sales[] or the assessment.total_value.
+ Calculates Loan-to-Value (LTV) and Debt Service Coverage Ratio (DSCR):
+ ```python
+ LTV = active_loan_amount / current_property_value
+ DSCR = net_operating_income / annual_debt_service
+ ```
+ where net_operating_income is derived from historical rent rolls, operating expenses, and modeled vacancy rates.  

- Projects debt coverage needs and refinancing risk using mortgages[0].maturity_date, interest_rate, and recording_date.
+ Projects debt yield (NOI / active_loan_amount) and runs stress tests on DSCR under 5‑ and 10‑point interest‑rate shocks and vacancy increases :contentReference.

- Uses lender_name to cluster regional lending activity and analyze lender concentration trends.
+ Enriches lender concentration analysis with counterparty risk scores and tracks shifts between bank vs. nonbank (private‑credit) lending :contentReference

+ **Adds cash‑flow forecasting**:
+ - Pull rent roll, expense, and vacancy history from leases[].
+ - Model future NOI with rent growth and expense inflation assumptions.
+ - Compute cap rate implied value and compare against appraisal value.

- Constructs full financing trajectory per parcel: date and size of each mortgage, lender behavior, debt stacking, and implied financial leverage.
+ **Enhance with scenario outputs**:
+ - Base, upside, and downside DSCR and LTV scenarios.
+ - Automatic “go/no‑go” underwriting flags based on lender threshold criteria (e.g., DSCR ≥ 1.25).

- Enables underwriting teams to distinguish between high-leverage parcels and fully owned assets, a critical step in determining acquisition strategy and funding structures.
+ **Output Schema (extended)**:
+ ```json
+ {
+   "parcel_id": "...",
+   "current_LTV": 0.65,
+   "current_DSCR": 1.30,
+   "debt_yield": 8.5,
+   "stress_DSCR_+200bp": 1.10,
+   "implied_cap_rate_value": 12_000_000,
+   "risk_flags": ["maturity_90d", "low_DSCR_1.10"],
+   …
+ }
+ ```


## Dependencies

### Required Packages
- `pandas`, `numpy` – core data manipulation and calculations  
- `python-dateutil` – parsing and handling dates (loan maturities, sale dates)  
- `requests` or your Reonomy API client – fetching mortgage, sale, financials, CMBS and tenant data  
- Optional: `scipy` or `statsmodels` – for running interest‑rate and vacancy stress scenarios  

## Configuration
- **API credentials** (Reonomy API key, endpoints)  
- **Stress test parameters**  
  - Interest‑rate shock (e.g. +200 bps, +500 bps)  
  - Vacancy shock (e.g. +5 %, +10 %)  
- **Underwriting thresholds**  
  - Minimum DSCR (e.g. 1.25)  
  - Maximum LTV (e.g. 75 %)  

## Output Schema
```json
{
  "parcel_id": "string",
  "current_LTV": float,           // active_loan_amount / current_property_value
  "current_DSCR": float,          // net_operating_income / annual_debt_service
  "debt_yield": float,            // net_operating_income / active_loan_amount
  "stress_DSCR": {                // DSCR under each stress scenario
    "rate_shock_200bp": float,
    "vacancy_shock_5pct": float
  },
  "implied_cap_rate_value": float,   // valuation = NOI / cap_rate
  "risk_flags": [ "string", … ],     // e.g. "maturity_90d", "low_DSCR_1.10"
  "scenario_outcomes": [             // summary of base/upside/downside scenarios
    { "name": "base", "LTV": float, "DSCR": float },
    { "name": "downside", "LTV": float, "DSCR": float }
  ]
}

Processing Steps
Fetch data

Mortgages (amount, rate, maturity, lender)

Sales history (price, date)

Tax‑roll financials (NOI, OPEX) and CMBS operating statements

Tenant/lease summaries (rent, occupancy)

Compute current metrics

Determine current property value (latest sale or assessment total)

Calculate LTV, DSCR and debt yield from outstanding debt and NOI

Model cash flows

Baseline NOI from historical rent rolls, expenses and occupancy

Project forward with rent growth and expense inflation assumptions

Run stress scenarios

Apply configured interest‑rate and vacancy shocks

Recalculate DSCR under each scenario

Generate risk flags

Flag near‑term maturities, DSCR below threshold, LTV above limit, recent large refinancings

Assemble output

Populate schema with metrics, stress results, scenario summaries and flags

