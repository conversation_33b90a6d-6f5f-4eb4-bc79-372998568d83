# Cluster Prospector Module Specification

## Overview
The Cluster Prospector module provides a macro-level view of industrial real estate activity through spatial clustering and market analysis. It automatically generates and analyzes clusters of industrial properties without requiring initial user filters.

## Module Information
- **Filename**: `cluster_prospector.py`
- **Purpose**: Generate and analyze geographic clusters of industrial properties
- **Activation**: Automatic on platform entry

## Dependencies

### Required Packages
- `geopandas`: Spatial operations and polygon handling
- `numpy`: Numerical computations
- `pandas`: Data manipulation
- `statsmodels`: Kalman filter implementation
- `shapely`: Geometric operations

### System Requirements
- Memory: 4GB+ RAM (for large datasets)
- Storage: SSD recommended for spatial operations
- CPU: Multi-core recommended for parallel processing

## Configuration
```python
cluster_config = {
    "clustering": {
        "method": "nearest_neighbor",    # Dynamic cluster discovery
        "max_parcels": 50,              # Maximum parcels per cluster
        "min_parcels": 3,               # Minimum parcels per cluster
        "max_distance": 10000           # Maximum distance between parcels (meters)
    },
    
    "time_windows": {
        "recent": 12,                   # months for recent activity
        "historical": 120,              # months for historical comparison
        "quarterly_bins": True          # use quarterly time bins
    },
    
    "ownership": {
        "min_percent": 3.0,             # minimum ownership percentage to track
        "consolidation_threshold": 25    # HHI threshold for consolidated control
    },
    
    "trend_analysis": {
        "kalman_window": "quarterly",
        "trend_threshold": {
            "up": 0.05,                 # +5% for upward trend
            "down": -0.05               # -5% for downward trend
        }
    }
}
```

## Input Schema
```python
ParcelData = {
    "parcel_id": str,
    "latitude": float,
    "longitude": float,
    "building_area": float,
    "true_owner_name": str,            # Matches Reonomy field name
    "last_sale": {
        "price": float,
        "date": datetime
    },
    "sales": List[{                    # Matches Reonomy field name
        "price": float,
        "date": datetime
    }]
}
```

## Output Schema
```python
ClusterAnalysis = {
    "geometry": "polygon",              # Convex hull or alpha shape of cluster
    "avg_bldg_sf": int,                # Mean building area
    "owner_hhi_pct": float,            # Herfindahl Index × 100
    "owner_breakout": {                # Owners with >3% holdings
        "owner_name": float            # Percentage owned
    },
    # Flattened transaction metrics for direct frontend binding
    "txn_recent_12mo_sum": float,      # 12-month transaction volume
    "txn_recent_12mo_count": int,      # Number of transactions in last 12 months
    "txn_total_10yr_sum": float,       # 10-year transaction volume
    "txn_total_10yr_count": int,       # Number of transactions in last 10 years
    "portion_recent": float,           # Recent volume as % of historical
    "avg_last_sale_price": float,      # Average of latest sale per parcel
    # Flattened Kalman metrics for simpler map popups
    "kalman_level": float,             # Current smoothed quarterly count
    "kalman_trend": float,             # Change in level
    "kalman_class": str               # "UP", "FLAT", or "DOWN"
}
```

## Processing Steps
1. **Spatial Clustering**
   - Generate hex grid or compute alpha shapes
   - Assign parcels to clusters
   - Filter clusters below minimum parcel count

2. **Ownership Analysis**
   - Calculate Herfindahl Index per cluster
   - Identify significant owners (>3%)
   - Compute concentration metrics

3. **Transaction Analysis**
   - Bin transactions by quarter
   - Calculate recent vs historical metrics
   - Compute volume and count statistics

4. **Trend Detection**
   - Apply Kalman filter to quarterly data
   - Calculate trend metrics
   - Classify trend direction

## Performance Considerations

### Optimization
- Spatial indexing for efficient clustering
- Parallel processing for large datasets
- Caching of intermediate results

### Memory Management
- Streaming processing for large datasets
- Efficient polygon simplification
- Regular garbage collection

## Error Handling
- Invalid coordinates handling
- Missing data imputation
- Edge case management for small clusters

## Testing Requirements
- Spatial clustering validation
- Trend detection accuracy
- Performance benchmarking
- Edge case testing



