import pandas as pd
import numpy as np
from faker import Faker
import random
import uuid
from dataclasses import dataclass
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta

@dataclass
class CaliforniaParcel:
    """Represents a California industrial parcel with Reonomy-compatible attributes."""
    apn: str
    latitude: float
    longitude: float
    fips: str
    asset_category: str
    building_area: int
    lot_area_ft: float
    building_class: str
    financials: Dict[str, float]  # noi, opex
    taxes: Dict[str, float]       # annual_taxes
    sales: List[Dict[str, Any]]   # executed_date, sale_price
    mortgages: List[Dict[str, Any]]  # remaining_balance, due_date, rate
    contacts: List[Dict[str, Any]]   # persons: name, phone, email

class CaliforniaParcelSimulator:
    """Generates synthetic California industrial property data."""
    
    REGIONS = {
        "Inland Empire": {
            "counties": ["San Bernardino", "Riverside"],
            "vacancy_mean": 0.067,
            "asking_rent_psf_mo": (0.98, 1.18),
            "cap_rate_range": (0.048, 0.056),
            "building_size_range": (200_000, 700_000)
        },
        "Los Angeles": {
            "counties": ["Los Angeles"],
            "vacancy_mean": 0.048,
            "asking_rent_psf_mo": (1.25, 1.45),
            "cap_rate_range": (0.045, 0.055),
            "building_size_range": (20_000, 180_000),
            "lot_size_range": (0.5, 3.0)
        },
        "Orange County": {
            "counties": ["Orange"],
            "vacancy_mean": 0.044,
            "asking_rent_psf_mo": (1.25, 1.45),
            "cap_rate_range": (0.045, 0.055)
        },
        "Alameda": {
            "counties": ["Alameda"],
            "vacancy_mean": 0.048,
            "asking_rent_psf_mo": (1.35, 1.55),
            "price_psf_sale": (275, 375),
            "building_size_range": (50_000, 200_000)
        },
        "Santa Clara": {
            "counties": ["Santa Clara"], 
            "vacancy_mean": 0.042,
            "asking_rent_psf_mo": (1.45, 1.65),
            "price_psf_sale": (300, 400),
            "building_size_range": (75_000, 250_000)
        },
        "Solano": {
            "counties": ["Solano"],
            "vacancy_mean": 0.065,
            "asking_rent_psf_mo": (1.10, 1.30),
            "price_psf_sale": (180, 250),
            "building_size_range": (100_000, 300_000)
        },
        
        "San Diego": {
            "counties": ["San Diego"],
            "vacancy_mean": 0.077,
            "asking_rent_psf_mo": (1.20, 1.40),
            "building_size_range": (50_000, 250_000),
            "lot_size_range": (5, 15)
        },
        "Central Valley": {
            "counties": ["Fresno", "Kern", "Stanislaus", "San Joaquin"],
            "vacancy_mean": 0.065,
            "cap_rate_range": (0.055, 0.070),
            "building_size_range": (100_000, 400_000),
            "lot_size_range": (10, 40)
        }
    }

    COUNTY_WEIGHTS = {
        "Los Angeles": 0.23,        # Increased from 0.22
        "San Bernardino": 0.18,
        "Riverside": 0.16,
        "Orange": 0.11,
        "San Diego": 0.08,          # Increased from 0.07
        "Alameda": 0.05,
        "Santa Clara": 0.05,
        "Kern": 0.03,
        "Fresno": 0.03,
        "Solano": 0.04,            # Increased from 0.02
        "Stanislaus": 0.04         # Increased from 0.02
    }   # Total now equals 1.0

    FIPS_CODES = {
        "Los Angeles": "06037", "San Bernardino": "06071", "Riverside": "06065",
        "Orange": "06059", "San Diego": "06073", "Alameda": "06001",
        "Santa Clara": "06085", "Kern": "06029", "Fresno": "06019",
        "Solano": "06095", "Stanislaus": "06099"
    }

    def __init__(self, seed: Optional[int] = None, config: Optional[Dict] = None):
        """Initialize simulator with optional seed and configuration."""
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
        
        self.faker = Faker()
        if seed is not None:
            Faker.seed(seed)
            
        self.config = config or {
            "num_parcels": 500_000,  # Reduced from 1M to 500k
            "state": "CA",
            "county_pool": "ALL_CA",
            "year_range": (1960, 2023)
        }

    def _get_region_by_county(self, county: str) -> str:
        """Determine region based on county."""
        for region, data in self.REGIONS.items():
            if county in data.get("counties", []):
                return region
        return random.choice(list(self.REGIONS.keys()))

    def _generate_coordinates(self, county: str) -> Dict[str, float]:
        """Generate realistic coordinates for California counties."""
        county_coords = {
            "Los Angeles": (34.0522, -118.2437),
            "San Bernardino": (34.9592, -116.4194),
            "Riverside": (33.9533, -117.3961),
            "Orange": (33.7175, -117.8311),
            "San Diego": (32.7157, -117.1611),
            "Alameda": (37.6017, -121.7195),
            "Santa Clara": (37.3541, -121.9552),
            "Kern": (35.3733, -119.0187),
            "Fresno": (36.7378, -119.7871),
            "Solano": (38.3105, -121.9018),
            "Stanislaus": (37.5091, -120.9876)
        }
        
        base_lat, base_lon = county_coords.get(
            county, 
            (random.uniform(32.5, 42.0), random.uniform(-124.4, -114.1))
        )
        return {
            "lat": base_lat + random.uniform(-0.1, 0.1),
            "lon": base_lon + random.uniform(-0.1, 0.1)
        }

    def _sample_tax_rate(self) -> float:
        """Generate realistic property tax rate."""
        return float(np.clip(np.random.normal(0.011, 0.0005), 0.009, 0.013))

    def _sample_year_built(self) -> int:
        """Generate realistic building construction year."""
        return int(np.random.triangular(1960, 1975, 2023))

    def _generate_sale_history(self, value: float, year_built: int) -> List[Dict]:
        """Generate realistic sale history."""
        num_sales = random.randint(1, 3)
        sales = []
        current_year = datetime.now().year
        
        for i in range(num_sales):
            year = random.randint(year_built, current_year)
            price = value * random.uniform(0.8, 1.2)
            sales.append({
                "sale_date": f"{year}-{random.randint(1,12):02d}-{random.randint(1,28):02d}",
                "sale_price": round(price, 2)
            })
        return sorted(sales, key=lambda x: x["sale_date"])

    def _generate_mortgage(self, latest_sale: Dict) -> Dict:
        """Generate realistic mortgage data."""
        loan_amount = float(latest_sale["sale_price"]) * random.uniform(0.5, 0.75)
        sale_date = datetime.strptime(latest_sale["sale_date"], "%Y-%m-%d")
        maturity_date = sale_date + timedelta(days=365 * random.randint(5, 10))
        
        return {
            "lender_name": f"{self.faker.company()} Bank",
            "original_loan_amount": round(loan_amount, 2),
            "maturity_date": maturity_date.strftime("%Y-%m-%d"),
            "interest_rate": round(random.uniform(3.5, 6.0), 2)
        }

    def generate_parcel(self, county: str = None) -> CaliforniaParcel:
        """Generate a single California industrial parcel."""
        if county is None:
            county = np.random.choice(
                list(self.COUNTY_WEIGHTS.keys()),
                p=list(self.COUNTY_WEIGHTS.values())
            )
            
        region = self._get_region_by_county(county)
        region_data = self.REGIONS[region]
        
        # Generate basic attributes
        building_area = random.randint(
            *region_data.get("building_size_range", (20_000, 200_000))
        )
        lot_size_ft = random.uniform(
            *region_data.get("lot_size_range", (2, 10))
        ) * 43560  # Convert acres to square feet
        
        # Generate financials with default values if needed
        default_rent = (1.20, 1.40)  # Default rent range if not specified
        default_vacancy = 0.05       # Default vacancy if not specified
        
        # Get rent and vacancy rates with defaults
        rent_range = region_data.get("asking_rent_psf_mo", default_rent)
        
        # Calculate NOI and OpEx
        noi = building_area * np.random.uniform(8, 12)  # Base NOI on building size
        opex = noi * np.random.uniform(0.35, 0.55)  # Operating expenses as % of NOI
        
        # Calculate value using cap rate or price per sq ft
        if "price_psf_sale" in region_data:
            value = building_area * random.uniform(*region_data["price_psf_sale"])
        else:
            cap_rate_range = region_data.get("cap_rate_range", (0.05, 0.06))
            cap_rate = random.uniform(*cap_rate_range)
            value = noi / cap_rate
            
        annual_taxes = value * self._sample_tax_rate()
        
        # Generate sales and mortgage history
        sales = []
        current_year = datetime.now().year
        num_sales = random.randint(1, 3)
        
        for i in range(num_sales):
            year = current_year - random.randint(0, 10)
            price = value * random.uniform(0.8, 1.2)
            sales.append({
                "executed_date": f"{year}-{random.randint(1,12):02d}-{random.randint(1,28):02d}",
                "sale_price": round(price, 2)
            })
        sales = sorted(sales, key=lambda x: x["executed_date"])
        
        # Generate mortgage data
        mortgage = None
        if sales:
            loan_amount = float(sales[-1]["sale_price"]) * random.uniform(0.5, 0.75)
            sale_date = datetime.strptime(sales[-1]["executed_date"], "%Y-%m-%d")
            due_date = sale_date + timedelta(days=365 * random.randint(5, 10))
            
            mortgage = {
                "remaining_balance": round(loan_amount, 2),
                "due_date": due_date.strftime("%Y-%m-%d"),
                "rate": round(random.uniform(3.5, 6.0), 2)
            }

        # Generate contact information
        contact = {
            "persons": [{
                "name": self.faker.name(),
                "phone": self.faker.phone_number(),
                "email": self.faker.company_email()
            }]
        }

        return CaliforniaParcel(
            apn=f"{random.randint(100, 999)}-{random.randint(100, 999)}-{random.randint(10, 99)}",
            latitude=self._generate_coordinates(county)["lat"],
            longitude=self._generate_coordinates(county)["lon"],
            fips=self.FIPS_CODES[county],
            asset_category="Industrial",
            building_area=building_area,
            lot_area_ft=round(lot_size_ft, 2),
            building_class=random.choice(["A", "B", "C"]),
            financials={
                "noi": round(noi, 2),
                "opex": round(opex, 2)
            },
            taxes={
                "annual_taxes": round(annual_taxes, 2)
            },
            sales=sales,
            mortgages=[mortgage] if mortgage else [],
            contacts=[contact]
        )

    def generate_batch(self, batch_size: int = 1000) -> List[CaliforniaParcel]:
        """Generate a batch of parcels with memory optimization."""
        return [self.generate_parcel() for _ in range(batch_size)]

    def parcel_to_csv_row(self, parcel: CaliforniaParcel) -> Dict:
        """Convert a parcel object to a flat dictionary for CSV export."""
        # Get the latest sale if exists
        latest_sale = parcel.sales[-1] if parcel.sales else {
            "executed_date": None,
            "sale_price": None
        }
        # Get the latest mortgage if exists
        latest_mortgage = parcel.mortgages[-1] if parcel.mortgages else {
            "remaining_balance": None,
            "due_date": None,
            "rate": None
        }
        # Get the primary contact if exists
        primary_contact = parcel.contacts[0]["persons"][0] if parcel.contacts else {
            "name": None,
            "phone": None,
            "email": None
        }

        return {
            "apn": parcel.apn,
            "latitude": parcel.latitude,
            "longitude": parcel.longitude,
            "fips": parcel.fips,
            "asset_category": parcel.asset_category,
            "building_area": parcel.building_area,
            "lot_area_ft": parcel.lot_area_ft,
            "building_class": parcel.building_class,
            "noi": parcel.financials["noi"],
            "opex": parcel.financials["opex"],
            "annual_taxes": parcel.taxes["annual_taxes"],
            "latest_sale_date": latest_sale["executed_date"],
            "latest_sale_price": latest_sale["sale_price"],
            "loan_balance": latest_mortgage["remaining_balance"],
            "loan_due_date": latest_mortgage["due_date"],
            "loan_rate": latest_mortgage["rate"],
            "owner_name": primary_contact["name"],
            "owner_phone": primary_contact["phone"],
            "owner_email": primary_contact["email"]
        }

    def save_to_csv(self, filename: str, num_parcels: int = None):
        """Save generated parcels to CSV file in batches."""
        if num_parcels is None:
            num_parcels = self.config["num_parcels"]
            
        batch_size = 1000  # Fixed batch size for memory efficiency
        total_batches = (num_parcels + batch_size - 1) // batch_size  # Ceiling division
        
        print(f"Generating {num_parcels:,} parcels in {total_batches} batches...")
        
        # Initialize progress tracking
        parcels_generated = 0
        progress_interval = max(num_parcels // 20, batch_size)  # Show progress every 5%
        
        try:
            remaining_parcels = num_parcels
            batch_num = 0
            
            while remaining_parcels > 0:
                # Calculate current batch size
                current_batch_size = min(batch_size, remaining_parcels)
                
                # Generate batch
                batch = self.generate_batch(current_batch_size)
                batch_rows = [self.parcel_to_csv_row(parcel) for parcel in batch]
                
                # Write to CSV
                df = pd.DataFrame(batch_rows)
                if batch_num == 0:
                    df.to_csv(filename, index=False, mode='w')
                else:
                    df.to_csv(filename, index=False, mode='a', header=False)
                
                # Update counters
                parcels_generated += len(batch)
                remaining_parcels -= len(batch)
                batch_num += 1
                
                # Show progress
                if parcels_generated % progress_interval == 0:
                    print(f"Generated {parcels_generated:,} parcels ({(parcels_generated/num_parcels)*100:.1f}%)")
            
            print(f"\nSuccessfully generated {parcels_generated:,} parcels!")
            
            # Verify file size
            df_check = pd.read_csv(filename)
            print(f"Final CSV file contains {len(df_check):,} records")
            
        except Exception as e:
            print(f"Error generating parcels: {str(e)}")
            raise

if __name__ == "__main__":
    # Initialize the simulator
    simulator = CaliforniaParcelSimulator(seed=42)
    
    # Create output directory if it doesn't exist
    import os
    output_dir = "generated_data"
    os.makedirs(output_dir, exist_ok=True)
    
    print("Starting parcel generation...")
    
    # Save parcels to CSV file
    output_file = os.path.join(output_dir, "california_parcels.csv")
    print(f"Generating 500k parcels and saving to {output_file}")
    
    try:
        simulator.save_to_csv(output_file)
        
        # Read and display first few rows of the generated CSV
        print("\nFirst few rows of generated data:")
        df = pd.read_csv(output_file, nrows=5)
        print(df[["apn", "asset_category", "building_area", "noi", "latest_sale_price"]].to_string())
        
    except Exception as e:
        print(f"Error generating parcels: {str(e)}")
