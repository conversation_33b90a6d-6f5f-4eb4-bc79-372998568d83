"""
API Key Setup Script for Industrial Location Algorithms

This script allows you to securely input your API keys for BLS and EIA services.
The keys will be stored in the config.py file for use by the application.

Usage:
    python setup_api_keys.py
"""

import os
import re
from getpass import getpass

def get_api_key_input(service_name: str, description: str, current_key: str = None) -> str:
    """
    Prompt user for API key input with option to keep current key.
    
    Args:
        service_name: Name of the API service (e.g., "BLS", "EIA")
        description: Description of what the API is used for
        current_key: Current API key value (if any)
    
    Returns:
        The API key to use (new input or existing)
    """
    print(f"\n{'='*60}")
    print(f"{service_name} API Key Setup")
    print(f"{'='*60}")
    print(f"Purpose: {description}")
    
    if current_key and current_key != "YOUR_API_KEY_HERE":
        masked_key = f"{current_key[:8]}...{current_key[-4:]}" if len(current_key) > 12 else "***"
        print(f"Current key: {masked_key}")
        
        while True:
            choice = input(f"\nKeep current {service_name} API key? (y/n): ").lower().strip()
            if choice in ['y', 'yes']:
                return current_key
            elif choice in ['n', 'no']:
                break
            else:
                print("Please enter 'y' or 'n'")
    
    while True:
        print(f"\nPlease enter your {service_name} API key:")
        print("(Input will be hidden for security)")
        
        # Use getpass to hide the input
        api_key = getpass("API Key: ").strip()
        
        if not api_key:
            print("API key cannot be empty. Please try again.")
            continue
            
        # Basic validation - check if it looks like an API key
        if len(api_key) < 8:
            print("API key seems too short. Please verify and try again.")
            retry = input("Continue anyway? (y/n): ").lower().strip()
            if retry not in ['y', 'yes']:
                continue
        
        # Confirm the key
        print(f"\nAPI key entered: {api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else '***'}")
        confirm = input("Is this correct? (y/n): ").lower().strip()
        
        if confirm in ['y', 'yes']:
            return api_key
        else:
            print("Let's try again...")

def read_config_file() -> str:
    """Read the current config.py file content."""
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print("Error: config.py file not found!")
        print("Please make sure you're running this script from the correct directory.")
        exit(1)

def extract_current_api_key(content: str, key_name: str) -> str:
    """Extract current API key value from config content."""
    pattern = rf'{key_name}\s*=\s*["\']([^"\']*)["\']'
    match = re.search(pattern, content)
    return match.group(1) if match else None

def update_config_content(content: str, key_name: str, new_value: str) -> str:
    """Update API key in config content."""
    pattern = rf'({key_name}\s*=\s*["\'])([^"\']*?)(["\'])'
    replacement = rf'\g<1>{new_value}\g<3>'
    
    if re.search(pattern, content):
        # Update existing key
        return re.sub(pattern, replacement, content)
    else:
        # Add new key if it doesn't exist
        lines = content.split('\n')
        
        # Find a good place to insert (after other API keys or at the beginning)
        insert_index = 0
        for i, line in enumerate(lines):
            if 'API' in line and '=' in line:
                insert_index = i + 1
            elif line.strip().startswith('#') and 'API' in line:
                insert_index = i + 1
        
        new_line = f'{key_name} = "{new_value}"  # {key_name.replace("_", " ").title()}'
        lines.insert(insert_index, new_line)
        return '\n'.join(lines)

def backup_config():
    """Create a backup of the current config file."""
    import shutil
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"config_backup_{timestamp}.py"
    
    try:
        shutil.copy2('config.py', backup_name)
        print(f"✅ Backup created: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"⚠️  Warning: Could not create backup: {e}")
        return None

def main():
    """Main function to run the API key setup process."""
    print("🔧 Industrial Location Algorithms - API Key Setup")
    print("=" * 60)
    print("This script will help you configure your API keys securely.")
    print("Your keys will be stored in config.py for use by the application.")
    
    # Create backup
    backup_file = backup_config()
    
    # Read current config
    config_content = read_config_file()
    
    # Get current API keys
    current_bls_key = extract_current_api_key(config_content, 'BLS_API_KEY')
    current_eia_key = extract_current_api_key(config_content, 'EIA_API_KEY')
    
    print(f"\nCurrent status:")
    print(f"  BLS API Key: {'✅ Set' if current_bls_key and current_bls_key != 'YOUR_API_KEY_HERE' else '❌ Not set'}")
    print(f"  EIA API Key: {'✅ Set' if current_eia_key and current_eia_key != 'YOUR_API_KEY_HERE' else '❌ Not set'}")
    
    # Setup BLS API Key
    bls_key = get_api_key_input(
        "BLS", 
        "Bureau of Labor Statistics - for employment and compensation data",
        current_bls_key
    )
    
    # Setup EIA API Key
    eia_key = get_api_key_input(
        "EIA",
        "Energy Information Administration - for electricity pricing data", 
        current_eia_key
    )
    
    # Update config content
    print(f"\n{'='*60}")
    print("Updating configuration...")
    print(f"{'='*60}")
    
    updated_content = config_content
    updated_content = update_config_content(updated_content, 'BLS_API_KEY', bls_key)
    updated_content = update_config_content(updated_content, 'EIA_API_KEY', eia_key)
    
    # Write updated config
    try:
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("✅ Configuration updated successfully!")
        print(f"\nAPI Keys configured:")
        print(f"  BLS: {bls_key[:8]}...{bls_key[-4:] if len(bls_key) > 12 else '***'}")
        print(f"  EIA: {eia_key[:8]}...{eia_key[-4:] if len(eia_key) > 12 else '***'}")
        
        if backup_file:
            print(f"\n💾 Original config backed up as: {backup_file}")
        
        print(f"\n🚀 You can now run the main application scripts!")
        print("   Example: python fetch_ecec_by_region.py")
        
    except Exception as e:
        print(f"❌ Error writing config file: {e}")
        if backup_file:
            print(f"Your original config is backed up as: {backup_file}")
        exit(1)

if __name__ == "__main__":
    main() 