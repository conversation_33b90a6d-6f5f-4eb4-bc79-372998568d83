"""
Fetch ECEC (Employer Costs for Employee Compensation) data by region.

This script retrieves fully-loaded hourly compensation data for various U.S. regions
and metropolitan areas using BLS ECEC series data with corrected series IDs.

Usage:
    python fetch_ecec_by_region.py
"""

from bls_client import fetch_fully_loaded_hourly, generate_ecec_series_id, VALID_ECEC_SUBCELLS
from typing import Dict, Optional
import time

def fetch_region_ecec_data() -> None:
    """
    Fetch and display ECEC data for all defined regions.
    
    Uses only valid ECEC subcell codes and the corrected CMU2 series format.
    Loops through each region, generates the appropriate series IDs,
    and retrieves both total compensation and wages-only data.
    """
    
    # Use only valid ECEC subcell codes from the BLS client
    regions = {
        "national": "000",
        "northeast_region": "210", 
        "south_region": "220",
        "midwest_region": "230",
        "west_region": "240",
        "new_england": "211",
        "middle_atlantic": "212", 
        "south_atlantic": "221",
        "east_south_central": "222",
        "west_south_central": "223",
        "east_north_central": "231",
        "west_north_central": "232",
        "mountain": "241",
        "pacific": "242",
        "la_csa": "0LG",
        "sf_csa": "0LK", 
        "seattle_csa": "0LL",
        "chicago_csa": "0LC",
        "dc_csa": "0LD",
        "boston_csa": "0LE",
        "ny_csa": "0LF",
        "philadelphia_csa": "0LH",
        "detroit_csa": "0LI",
        "atlanta_csa": "0LJ"
    }
    
    print("🏭 ECEC DATA BY REGION (CORRECTED SERIES)")
    print("=" * 90)
    print(f"{'Region':<22} {'Subcell':<8} {'Total Comp ($/hr)':<18} {'Wages Only ($/hr)':<17}")
    print("-" * 90)
    
    results = []
    successful_fetches = 0
    
    for region_name, subcell_code in regions.items():
        try:
            # Validate subcell code
            if subcell_code not in VALID_ECEC_SUBCELLS:
                print(f"{region_name:<22} {subcell_code:<8} {'INVALID CODE':<18} {'INVALID CODE':<17}")
                continue
            
            # Generate series IDs using corrected CMU2 format
            total_comp_series = generate_ecec_series_id(subcell_code, "total")
            wages_series = generate_ecec_series_id(subcell_code, "wages")
            
            print(f"\nProcessing {region_name} ({VALID_ECEC_SUBCELLS[subcell_code]})...")
            
            # Use unified fetching logic - try total comp first, then wages with multiplier
            total_comp_rate = fetch_fully_loaded_hourly(
                total_comp_series=total_comp_series,
                wages_only_series=wages_series
            )
            
            # Also get wages-only rate for comparison
            wages_rate = fetch_fully_loaded_hourly(
                wages_only_series=wages_series
            )
            
            # Format the results
            total_comp_str = f"${total_comp_rate:.2f}" if total_comp_rate is not None else "N/A"
            wages_str = f"${wages_rate:.2f}" if wages_rate is not None else "N/A"
            
            print(f"{region_name:<22} {subcell_code:<8} {total_comp_str:<18} {wages_str:<17}")
            
            if total_comp_rate is not None or wages_rate is not None:
                successful_fetches += 1
            
            # Store for summary
            results.append({
                "region": region_name,
                "subcell": subcell_code,
                "description": VALID_ECEC_SUBCELLS[subcell_code],
                "total_comp": total_comp_rate,
                "wages": wages_rate,
                "total_comp_series": total_comp_series,
                "wages_series": wages_series
            })
            
            # Rate limiting to be respectful to BLS API
            time.sleep(0.5)
            
        except Exception as e:
            print(f"{region_name:<22} {subcell_code:<8} {'ERROR':<18} {'ERROR':<17}")
            print(f"  Error: {str(e)}")
    
    # Print summary statistics
    print("\n" + "=" * 90)
    print("SUMMARY STATISTICS")
    print("=" * 90)
    
    # Filter successful results
    valid_total_comp = [r['total_comp'] for r in results if r['total_comp'] is not None]
    valid_wages = [r['wages'] for r in results if r['wages'] is not None]
    
    print(f"Successfully fetched data for {successful_fetches}/{len(regions)} regions")
    
    if valid_total_comp:
        avg_total_comp = sum(valid_total_comp) / len(valid_total_comp)
        max_total_comp = max(valid_total_comp)
        min_total_comp = min(valid_total_comp)
        
        print(f"\nTotal Compensation:")
        print(f"  Average: ${avg_total_comp:.2f}/hr")
        print(f"  Range: ${min_total_comp:.2f} - ${max_total_comp:.2f}/hr")
        print(f"  Data points: {len(valid_total_comp)}/{len(regions)}")
        
        # Find highest and lowest regions
        max_region = next(r for r in results if r['total_comp'] == max_total_comp)
        min_region = next(r for r in results if r['total_comp'] == min_total_comp)
        print(f"  Highest: {max_region['region']} (${max_total_comp:.2f}/hr)")
        print(f"  Lowest: {min_region['region']} (${min_total_comp:.2f}/hr)")
    
    if valid_wages:
        avg_wages = sum(valid_wages) / len(valid_wages)
        max_wages = max(valid_wages)
        min_wages = min(valid_wages)
        
        print(f"\nWages Only:")
        print(f"  Average: ${avg_wages:.2f}/hr")
        print(f"  Range: ${min_wages:.2f} - ${max_wages:.2f}/hr") 
        print(f"  Data points: {len(valid_wages)}/{len(regions)}")
        
        # Find highest and lowest regions for wages
        max_wage_region = next(r for r in results if r['wages'] == max_wages)
        min_wage_region = next(r for r in results if r['wages'] == min_wages)
        print(f"  Highest: {max_wage_region['region']} (${max_wages:.2f}/hr)")
        print(f"  Lowest: {min_wage_region['region']} (${min_wages:.2f}/hr)")
    
    # Show which regions had data vs which didn't
    successful_regions = [r for r in results if r['total_comp'] is not None or r['wages'] is not None]
    failed_regions = [r for r in results if r['total_comp'] is None and r['wages'] is None]
    
    print(f"\n✅ Regions with data ({len(successful_regions)}):")
    for result in successful_regions:
        status = "✓ Total & Wages" if result['total_comp'] and result['wages'] else \
                "⚡ Total only" if result['total_comp'] else "💰 Wages only"
        print(f"  • {result['region']} ({result['subcell']}) - {status}")
    
    if failed_regions:
        print(f"\n❌ Regions without data ({len(failed_regions)}):")
        for result in failed_regions:
            print(f"  • {result['region']} ({result['subcell']}) - {result['description']}")
    
    # Show series IDs for reference (first few examples)
    print(f"\n📋 SAMPLE SERIES IDs (CMU2 Format):")
    print("-" * 50)
    for result in results[:5]:  # Show first 5 as examples
        print(f"{result['region']} ({result['subcell']}):")
        print(f"  Total Comp: {result['total_comp_series']}")
        print(f"  Wages Only: {result['wages_series']}")
        print(f"  Description: {result['description']}")
        print()

def validate_all_subcells():
    """Validate that all our region codes are in the official list."""
    print("🔍 VALIDATING SUBCELL CODES")
    print("=" * 50)
    
    our_codes = {
        "national": "000",
        "northeast_region": "210", 
        "south_region": "220",
        "midwest_region": "230",
        "west_region": "240",
        "new_england": "211",
        "middle_atlantic": "212", 
        "south_atlantic": "221",
        "east_south_central": "222",
        "west_south_central": "223",
        "east_north_central": "231",
        "west_north_central": "232",
        "mountain": "241",
        "pacific": "242",
        "la_csa": "0LG",
        "sf_csa": "0LK", 
        "seattle_csa": "0LL",
        "chicago_csa": "0LC",
        "dc_csa": "0LD",
        "boston_csa": "0LE",
        "ny_csa": "0LF",
        "philadelphia_csa": "0LH",
        "detroit_csa": "0LI",
        "atlanta_csa": "0LJ"
    }
    
    valid_count = 0
    invalid_count = 0
    
    for region_name, code in our_codes.items():
        if code in VALID_ECEC_SUBCELLS:
            print(f"✅ {region_name:<20} {code} - {VALID_ECEC_SUBCELLS[code]}")
            valid_count += 1
        else:
            print(f"❌ {region_name:<20} {code} - INVALID CODE")
            invalid_count += 1
    
    print(f"\nValidation Summary: {valid_count} valid, {invalid_count} invalid codes")
    
    if invalid_count > 0:
        print("\n📋 All valid ECEC subcell codes:")
        for code, desc in sorted(VALID_ECEC_SUBCELLS.items()):
            print(f"  {code}: {desc}")

def main():
    """Main function to run the ECEC data fetching."""
    print("🎯 CORRECTED ECEC DATA COLLECTION")
    print("Using CMU2 (private industry) series with validated subcell codes")
    print("This may take a few minutes due to API rate limiting.\n")
    
    # First validate our subcell codes
    validate_all_subcells()
    
    print("\n" + "="*90)
    
    try:
        fetch_region_ecec_data()
        print(f"\n✅ ECEC data collection complete!")
        print("📊 Data shows fully-loaded hourly rates including benefits")
        print("💡 CMU2 series provide private industry compensation data")
        
    except Exception as e:
        print(f"\n❌ Error during data collection: {str(e)}")
        raise

if __name__ == "__main__":
    main() 