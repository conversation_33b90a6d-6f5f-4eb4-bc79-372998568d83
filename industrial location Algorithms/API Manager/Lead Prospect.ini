Lead Prospect

This project implements an on-the-fly parcel matching engine that takes user specifications (location, radius, zoning, CapEx/Opex targets) and returns the best-fit parcel coordinate along with cost details. No pre-calculation of all parcels is required—costs are computed dynamically for the filtered subset.

Project Structure

project_root/
│
├── data_simulator.py       # Simulates Parcel API: generates mock parcel records
├── bls_client.py           # BLS API wrapper: fetches/caches wage data
├── eia_client.py           # EIA API wrapper: fetches/caches utility rates
├── routing_client.py       # Routing API wrapper: computes transport cost
│
├── cost_calculator.py      # Core formulas: calculates CapEx and OpEx per parcel
├── matcher.py              # Orchestrator: filters, calls calculators, ranks parcels
│
├── config.py               # Configuration: API keys, usage defaults, caching
├── main.py                 # Entry point: parses user input, invokes matcher, outputs JSON
└── requirements.txt        # Python dependencies

Components

Data Simulator (data_simulator.py)

Generates mock parcel data (ID, coords, sale price, value, zoning, acreage, etc.)

Acts as a stand-in for a real Parcel API or database query

BLS Client (bls_client.py)

Fetches average hourly wage for industrial sector by region

Implements caching to minimize API calls

EIA Client (eia_client.py)

Retrieves electricity and water rates for utility zones

Caches rates per zone

Routing Client (routing_client.py)

Calculates distance or travel time between parcel and key nodes

Converts distance/time into transportation cost

Cost Calculator (cost_calculator.py)

Implements formulas:

CapEx = (sale price or assessed value) × 1.02

OpEx = electricity + water + tax + labor + transport costs

Returns (capex, opex) for each parcel record

Matcher (matcher.py)

Filters parcels by user-specified city/radius/zoning

Invokes cost_calculator with data from clients

Computes fit score vs. user targets and ranks results

Returns top match(es) with parcel ID, coordinates, cost details

Entry Point (main.py)

Parses CLI or HTTP parameters (location, radius, zoning, CapEx/Opex targets)

Calls matcher.find_best_parcel()

Outputs JSON response

Installation

git clone <repo-url>
cd project_root
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

Usage

# Command-line example\ npython main.py \
  --city "Los Angeles" \
  --radius_km 5 \
  --zoning Industrial \
  --capex_target 500000 \
  --opex_target 100000

Response:

{
  "parcel_id": "P102",
  "latitude": 34.05,
  "longitude": -118.24,
  "capex": 512000,
  "opex": 75000,
  "fit_score": 462000
}

Configuration

API keys for BLS, EIA, and routing services go into config.py.

Default usage estimates can be adjusted in config.py (e.g., kWh/year, gallons/year, transport cost per mile).

Extensibility

Swap out data_simulator for a real database query in production.

Add caching layers or asynchronous calls for high-performance.

Expose main.py as a REST API using FastAPI or Flask.

Cursor-based scaffolding should follow this README structure to generate each file and implement the functions in the correct modules.