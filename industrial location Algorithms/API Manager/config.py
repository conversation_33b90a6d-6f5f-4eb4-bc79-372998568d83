# Configuration settings for the industrial location algorithm

# API Configuration
# Register for a BLS API key at: https://www.bls.gov/developers/
# Register for an EIA API key at: https://www.eia.gov/opendata/register.php
BLS_API_KEY = "9432e58731fe4ce6a5d3fda455f88d4f"  # BLS API key - set using setup_api_keys.py
EIA_API_KEY = "YzyXapQ0gZtmjVCaQDBcBa9ZQzcCWJxa2xU2nxkB"  # EIA API key - set using setup_api_keys.py

# Default search parameters
DEFAULT_SEARCH_RADIUS_KM = 50
DEFAULT_RESULTS_COUNT = 10

# Cache settings
CACHE_EXPIRY_HOURS = 24
MAX_CACHE_ENTRIES = 1000

# API rate limiting
MAX_REQUESTS_PER_MINUTE = 30
REQUEST_TIMEOUT_SECONDS = 10

# Labor cost calculation settings
FULLY_LOADED_LABOR_MULTIPLIER = 1.3  # 30% overhead for benefits, taxes, etc.
DEFAULT_WORK_HOURS_PER_YEAR = 2080   # 40 hours/week * 52 weeks

# BLS Series IDs for ECEC (Employer Costs for Employee Compensation) data
# Documentation: https://www.bls.gov/data/#compensation
BLS_SERIES = {
    "private_industry": {
        "total_compensation": "CMU1010000000000D",     # Total compensation
        "wages_and_salaries": "CMU1010000000000W",     # Wages and salaries
        "total_benefits": "CMU1010000000000B",         # Total benefits
        "paid_leave": "CMU1010000000000G",            # Paid leave
        "insurance": "CMU1010000000000H",             # Insurance benefits
        "retirement": "CMU1010000000000I"             # Retirement and savings
    },
    "manufacturing": {
        "total_compensation": "CMU2030000000000D",     # Total compensation
        "wages_and_salaries": "CMU2030000000000W",     # Wages and salaries
        "total_benefits": "CMU2030000000000B",         # Total benefits
        "paid_leave": "CMU2030000000000G",            # Paid leave
        "insurance": "CMU2030000000000H",             # Insurance benefits
        "retirement": "CMU2030000000000I"             # Retirement and savings
    }
} 