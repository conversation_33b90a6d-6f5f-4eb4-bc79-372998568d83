import requests
from functools import lru_cache
from typing import Optional, Dict, Any, List, Tuple
import config
import logging
from urllib.parse import urlencode, quote

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

EIA_BASE_URL = "https://api.eia.gov/v2/electricity/retail-sales/data/"

class EIAClientError(Exception):
    """Custom exception for EIA API client errors."""
    pass

class EIAClient:
    """Client for fetching electricity retail sales data from the EIA API."""
    def __init__(self, api_key: str = None):
        self.api_key = api_key or config.EIA_API_KEY
        self.default_rates = {
            "electricity": 0.12,  # $/kWh
            "natural_gas": 8.50   # $/MCF
        }

    @lru_cache(maxsize=32)
    def fetch_retail_sales(
        self,
        state_id: Optional[str] = None,
        sector_id: Optional[str] = None,
        data_types: List[str] = ["sales", "price"],
        frequency: str = "monthly",
        offset: int = 0,
        length: int = 1
    ) -> Dict[str, Any]:
        """
        Fetch retail electricity sales or price data.

        Args:
            state_id: U.S. postal code (e.g. 'CA') to filter by state
            sector_id: sector code (e.g. 'RES', 'COM', 'IND')
            data_types: list of data fields to request, e.g. ['sales','price']
            frequency: 'monthly' or 'annual'
            offset: pagination offset
            length: number of records to return

        Returns:
            A dict containing the JSON response data.

        Raises:
            EIAClientError: on network/API failure or missing data.
        """
        # Build parameters as a list of tuples to ensure correct URL encoding
        params: List[Tuple[str, str]] = [
            ('api_key', self.api_key),
            ('frequency', frequency),
            ('offset', str(offset)),
            ('length', str(length)),
            ('sort[0][column]', 'period'),
            ('sort[0][direction]', 'desc')
        ]
        
        # Add data types with repeated data[] keys
        for dt in data_types:
            params.append(('data[]', dt))
            
        # Add facets with correct lowercase names and array notation
        if state_id:
            params.append(('facets[stateid][]', state_id))
        if sector_id:
            params.append(('facets[sectorid][]', sector_id))

        try:
            # Create debug version of parameters with masked API key
            debug_params = [(k, '***' if k == 'api_key' else v) for k, v in params]
            logger.debug(f"Request parameters: {debug_params}")
            
            # Make the request
            resp = requests.get(EIA_BASE_URL, params=params, timeout=config.REQUEST_TIMEOUT_SECONDS)
            
            # Log the URL (with masked API key) to verify parameter formatting
            debug_url = resp.url.replace(self.api_key, '***')
            logger.info(f"Request URL: {debug_url}")
            
            resp.raise_for_status()
            data = resp.json()
            
            if 'error' in data:
                error_msg = data.get('error', {}).get('message', 'Unknown API error')
                logger.error(f"EIA API error: {error_msg}")
                raise EIAClientError(f"EIA API error: {error_msg}")
                
            if not data.get('response', {}).get('data'):
                logger.warning("No data returned from EIA API")
                return {'response': {'data': []}}
                
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch EIA data: {str(e)}")
            raise EIAClientError(f"Failed to fetch EIA data: {str(e)}")

    def get_latest_price_cents_per_kwh(
        self,
        state_id: str,
        sector_id: str = 'RES'
    ) -> Optional[float]:
        """
        Get the most recent electricity retail price in cents per kWh.

        Args:
            state_id: 2-letter state code
            sector_id: sector code ('RES','COM','IND','ALL')

        Returns:
            price in cents per kWh, or None if unavailable.
        """
        try:
            data = self.fetch_retail_sales(
                state_id=state_id,
                sector_id=sector_id,
                data_types=["price"],
                frequency="monthly",
                length=1
            )
            try:
                record = data['response']['data'][0]
                # price field is $/MWh; convert to cents/kWh
                price_mwh = float(record.get('price', 0))
                # $/MWh to $/kWh = price_mwh / 1000; to cents, multiply by 100
                return (price_mwh / 1000) * 100
            except (KeyError, IndexError, ValueError) as e:
                logger.warning(f"Failed to parse price data: {str(e)}")
                return None
        except EIAClientError as e:
            logger.warning(f"Failed to fetch price data: {str(e)}")
            return None

    def get_latest_sales_mwh(
        self,
        state_id: str,
        sector_id: str = 'RES'
    ) -> Optional[float]:
        """
        Get the most recent electricity retail sales in MWh.

        Args:
            state_id: 2-letter state code
            sector_id: sector code

        Returns:
            sales in MWh, or None if unavailable.
        """
        try:
            data = self.fetch_retail_sales(
                state_id=state_id,
                sector_id=sector_id,
                data_types=["sales"],
                frequency="monthly",
                length=1
            )
            try:
                record = data['response']['data'][0]
                return float(record.get('sales', 0))
            except (KeyError, IndexError, ValueError) as e:
                logger.warning(f"Failed to parse sales data: {str(e)}")
                return None
        except EIAClientError as e:
            logger.warning(f"Failed to fetch sales data: {str(e)}")
            return None

    def get_default_rate(self, rate_type: str, default_value: float = None) -> float:
        """
        Get default rate for a given utility type.
        
        Args:
            rate_type: Type of rate ('electricity' or 'natural_gas')
            default_value: Optional override for default value
            
        Returns:
            Default rate value
        """
        if default_value is not None:
            return default_value
        return self.default_rates.get(rate_type, 0.0)

# Example usage:
# client = EIAClient()\# rate = client.get_latest_price_cents_per_kwh('CA','RES')
# sales = client.get_latest_sales_mwh('CA','RES')
