"""
Demographics model for population and economic analysis
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class Demographics(BaseModel):
    """Demographic and economic data for a geographic area"""
    
    # Population metrics
    total_population: int = Field(..., description="Total population count")
    population_density: float = Field(..., description="Population per square mile")
    median_age: Optional[float] = Field(None, description="Median age of population")
    
    # Income metrics
    median_income: float = Field(..., description="Median household income in USD")
    per_capita_income: Optional[float] = Field(None, description="Per capita income in USD")
    poverty_rate: Optional[float] = Field(None, description="Poverty rate as decimal (0.15 = 15%)")
    
    # Education levels
    pct_high_school: Optional[float] = Field(None, description="Percentage with high school diploma")
    pct_college: float = Field(..., description="Percentage with college degree")
    pct_graduate_degree: Optional[float] = Field(None, description="Percentage with graduate degree")
    pct_technical_training: Optional[float] = Field(None, description="Percentage with technical/vocational training")
    
    # Age distribution
    pct_under_18: Optional[float] = Field(None, description="Percentage under 18 years old")
    pct_18_to_64: Optional[float] = Field(None, description="Percentage 18-64 years old (working age)")
    pct_over_65: Optional[float] = Field(None, description="Percentage over 65 years old")
    
    # Employment
    employment_rate: Optional[float] = Field(None, description="Employment rate as decimal")
    labor_force_participation: Optional[float] = Field(None, description="Labor force participation rate")
    
    # Housing
    median_home_value: Optional[float] = Field(None, description="Median home value in USD")
    homeownership_rate: Optional[float] = Field(None, description="Homeownership rate as decimal")
    cost_of_living_index: Optional[float] = Field(None, description="Cost of living index (100 = national average)")
    
    # Industry composition
    pct_manufacturing: Optional[float] = Field(None, description="Percentage employed in manufacturing")
    pct_services: Optional[float] = Field(None, description="Percentage employed in services")
    pct_government: Optional[float] = Field(None, description="Percentage employed in government")
    pct_agriculture: Optional[float] = Field(None, description="Percentage employed in agriculture")
    
    # Transportation patterns
    commute_time_avg: Optional[float] = Field(None, description="Average commute time in minutes")
    pct_drive_alone: Optional[float] = Field(None, description="Percentage who drive alone to work")
    pct_public_transport: Optional[float] = Field(None, description="Percentage who use public transport")
    
    # Geographic identifiers
    geographic_level: str = Field(..., description="Geographic level (tract, county, msa, etc.)")
    geographic_name: str = Field(..., description="Geographic area name")
    state: Optional[str] = Field(None, description="State")
    county: Optional[str] = Field(None, description="County")
    
    # Data metadata
    census_year: Optional[str] = Field(None, description="Census data year")
    data_source: Optional[str] = Field(None, description="Source of demographic data")
    data_date: Optional[str] = Field(None, description="Date of data collection")
    
    # Additional metrics
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional demographic metrics")
    
    def to_dict(self) -> dict:
        """Convert to dictionary format"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: dict) -> "Demographics":
        """Create from dictionary format"""
        return cls(**data)
    
    def workforce_quality_score(self) -> float:
        """Calculate workforce quality score (0-100)"""
        score = 0.0
        weights = 0.0
        
        # Education (50% weight)
        if self.pct_college is not None:
            education_score = min(100, self.pct_college * 100 * 2)  # Scale college percentage
            score += education_score * 0.5
            weights += 0.5
        
        # Income level (30% weight) 
        if self.median_income is not None:
            # Score based on income relative to national median (~$70k)
            income_score = min(100, (self.median_income / 70000) * 100)
            score += income_score * 0.3
            weights += 0.3
        
        # Working age population (20% weight)
        if self.pct_18_to_64 is not None:
            working_age_score = self.pct_18_to_64 * 100
            score += working_age_score * 0.2
            weights += 0.2
        
        return score / weights if weights > 0 else 0.0
    
    def economic_development_score(self) -> float:
        """Calculate economic development potential score (0-100)"""
        score = 0.0
        weights = 0.0
        
        # Population density indicates market size
        if self.population_density is not None:
            density_score = min(100, self.population_density / 10)  # Scale appropriately
            score += density_score * 0.3
            weights += 0.3
        
        # Income levels
        if self.median_income is not None:
            income_score = min(100, (self.median_income / 70000) * 100)
            score += income_score * 0.4
            weights += 0.4
        
        # Employment rate
        if self.employment_rate is not None:
            employment_score = self.employment_rate * 100
            score += employment_score * 0.3
            weights += 0.3
        
        return score / weights if weights > 0 else 0.0 