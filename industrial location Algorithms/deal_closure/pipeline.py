"""
Deal pipeline management
"""

from models.deal import Deal, DealStage
from datetime import datetime


def advance_stage(deal: Deal) -> Deal:
    """Advance deal to next stage if criteria are met"""
    
    current_stage = deal.current_stage
    
    # Stage advancement logic
    if current_stage == DealStage.LEAD:
        if _is_qualified(deal):
            deal.current_stage = DealStage.QUALIFIED
    
    elif current_stage == DealStage.QUALIFIED:
        if _has_proposal(deal):
            deal.current_stage = DealStage.PROPOSAL
    
    elif current_stage == DealStage.PROPOSAL:
        if _in_negotiation(deal):
            deal.current_stage = DealStage.NEGOTIATION
    
    elif current_stage == DealStage.NEGOTIATION:
        if _ready_for_due_diligence(deal):
            deal.current_stage = DealStage.DUE_DILIGENCE
    
    elif current_stage == DealStage.DUE_DILIGENCE:
        if _due_diligence_complete(deal):
            deal.current_stage = DealStage.FINANCING
    
    elif current_stage == DealStage.FINANCING:
        if _financing_approved(deal):
            deal.current_stage = DealStage.CLOSING
    
    elif current_stage == DealStage.CLOSING:
        if _ready_to_close(deal):
            deal.current_stage = DealStage.CLOSED
            deal.actual_closing_date = datetime.now()
    
    # Update probability based on stage
    deal.update_stage(deal.current_stage)
    
    return deal


def _is_qualified(deal: Deal) -> bool:
    """Check if lead is qualified"""
    return (deal.purchase_price is not None and 
            deal.buyer is not None)


def _has_proposal(deal: Deal) -> bool:
    """Check if proposal has been made"""
    return deal.purchase_price is not None


def _in_negotiation(deal: Deal) -> bool:
    """Check if deal is in negotiation"""
    # Mock: assume negotiation started if we have basic terms
    return deal.purchase_price is not None and deal.seller is not None


def _ready_for_due_diligence(deal: Deal) -> bool:
    """Check if ready for due diligence"""
    # Mock: assume ready if we have agreed terms
    return deal.purchase_price is not None


def _due_diligence_complete(deal: Deal) -> bool:
    """Check if due diligence is complete"""
    # Check for completion of due diligence tasks
    dd_tasks = [task for task in deal.tasks 
                if "due diligence" in task.name.lower() or 
                   "environmental" in task.name.lower() or
                   "title" in task.name.lower()]
    
    if not dd_tasks:
        return True  # No DD tasks, assume complete
    
    return all(task.status.value == "completed" for task in dd_tasks)


def _financing_approved(deal: Deal) -> bool:
    """Check if financing is approved"""
    financing_tasks = [task for task in deal.tasks 
                      if "financing" in task.name.lower() or
                         "loan" in task.name.lower()]
    
    if not financing_tasks:
        return True  # No financing tasks, assume approved
    
    return all(task.status.value == "completed" for task in financing_tasks)


def _ready_to_close(deal: Deal) -> bool:
    """Check if ready to close"""
    # All tasks should be complete
    if deal.tasks:
        return all(task.status.value == "completed" for task in deal.tasks)
    
    # If no tasks, check if we have required elements
    return (deal.purchase_price is not None and 
            deal.buyer is not None and 
            deal.seller is not None) 