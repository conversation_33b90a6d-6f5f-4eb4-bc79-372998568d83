

### Core Property Data
- `latitude`: float
- `longitude`: float
- `building_area`: float (sq ft)
- `zoning`: string
- `std_land_use_code`: string
- `year_built`: integer

### Financial Data
- `assessment.total_value`: float
  Assessed valuation of the property for tax purposes; used to benchmark market vs. tax valuation and estimate CapEx baselines.
- `taxes.annual_tax_amount`: float
  Most recent annual property tax billed; useful for estimating annual fixed OpEx.
- `sales[]`: array
  Chronological history of all recorded transactions, including:
  - `sale_date`
  - `sale_price`
  - `buyer_name`
  - `seller_name`
  - `document_type`
  - `recording_date`
  Useful for price trend modeling, turnover analysis, and market timing.
- `mortgages[]`: array
  Mortgage event history. Each entry contains:
  - `lender_name`
  - `original_loan_amount`
  - `interest_rate`
  - `maturity_date`
  - `recording_date`
  Used to compute leverage, financing structure, and debt risk.
- `mortgages[0].original_loan_amount`: float
  Amount of the most recent mortgage; critical for estimating current financing terms.
- `mortgages[0].maturity_date`: date
  Used to flag upcoming refinancing needs or debt risk.
- `mortgages[0].lender_name`: string
  Enables insight into lender concentration or regional lending patterns.

### Ownership Data
- `true_owner_name`: string
- `owner_contact_phone`: string
- `owner_contact_email`: string
## Required Data Fields

### Core Property Data
- `latitude`: float
- `longitude`: float
- `building_area`: float (sq ft)
- `zoning`: string
- `std_land_use_code`: string
- `year_built`: integer

### Financial Data
- `assessment.total_value`: float
  Assessed valuation of the property for tax purposes; used to benchmark market vs. tax valuation and estimate CapEx baselines.
- `taxes.annual_tax_amount`: float
  Most recent annual property tax billed; useful for estimating annual fixed OpEx.
- `sales[]`: array
  Chronological history of all recorded transactions, including:
  - `sale_date`
  - `sale_price`
  - `buyer_name`
  - `seller_name`
  - `document_type`
  - `recording_date`
  Useful for price trend modeling, turnover analysis, and market timing.
- `mortgages[]`: array
  Mortgage event history. Each entry contains:
  - `lender_name`
  - `original_loan_amount`
  - `interest_rate`
  - `maturity_date`
  - `recording_date`
  Used to compute leverage, financing structure, and debt risk.
- `mortgages[0].original_loan_amount`: float
  Amount of the most recent mortgage; critical for estimating current financing terms.
- `mortgages[0].maturity_date`: date
  Used to flag upcoming refinancing needs or debt risk.
- `mortgages[0].lender_name`: string
  Enables insight into lender concentration or regional lending patterns.

### Ownership Data
- `true_owner_name`: string
- `owner_contact_phone`: string
- `owner_contact_email`: string

## Configuration

The system requires the following configuration in `config.py`:
- API keys for external services
- Database connection strings
- Caching parameters
- Analysis weights and thresholds
- Document template paths
