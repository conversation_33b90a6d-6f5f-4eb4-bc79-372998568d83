"""
Sensitivity analysis and stress testing for real estate investments
"""

import numpy as np
from typing import Dict, Any, List
from .calc_cashflow import compute_cashflow


def run_stress_tests(inputs: Dict[str, Any]) -> Dict[str, float]:
    """Run comprehensive stress tests on key variables"""
    
    # Base case
    base_case = compute_cashflow(inputs)
    base_npv = base_case["npv"]
    
    # Define stress test scenarios
    scenarios = {
        "rent_down_10": _modify_inputs(inputs, {"rent_adjustment": -0.10}),
        "rent_down_20": _modify_inputs(inputs, {"rent_adjustment": -0.20}),
        "rent_up_10": _modify_inputs(inputs, {"rent_adjustment": 0.10}),
        "expenses_up_15": _modify_inputs(inputs, {"expense_adjustment": 0.15}),
        "expenses_up_25": _modify_inputs(inputs, {"expense_adjustment": 0.25}),
        "interest_up_100bp": _modify_inputs(inputs, {"interest_rate_adjustment": 0.01}),
        "interest_up_200bp": _modify_inputs(inputs, {"interest_rate_adjustment": 0.02}),
        "exit_cap_up_50bp": _modify_inputs(inputs, {"exit_cap_adjustment": 0.005}),
        "exit_cap_up_100bp": _modify_inputs(inputs, {"exit_cap_adjustment": 0.01}),
        "construction_cost_up_15": _modify_inputs(inputs, {"construction_cost_adjustment": 0.15}),
        "construction_cost_up_25": _modify_inputs(inputs, {"construction_cost_adjustment": 0.25})
    }
    
    # Calculate NPV for each scenario
    scenario_results = {}
    for scenario_name, scenario_inputs in scenarios.items():
        scenario_cashflow = compute_cashflow(scenario_inputs)
        scenario_results[scenario_name] = scenario_cashflow["npv"]
    
    # Best and worst case scenarios
    best_case_inputs = _modify_inputs(inputs, {
        "rent_adjustment": 0.15,
        "expense_adjustment": -0.10,
        "exit_cap_adjustment": -0.005
    })
    best_case = compute_cashflow(best_case_inputs)
    
    worst_case_inputs = _modify_inputs(inputs, {
        "rent_adjustment": -0.20,
        "expense_adjustment": 0.20,
        "interest_rate_adjustment": 0.02,
        "exit_cap_adjustment": 0.01
    })
    worst_case = compute_cashflow(worst_case_inputs)
    
    # Calculate break-even rent
    break_even_rent = _calculate_break_even_rent(inputs)
    
    # Sensitivity metrics
    rent_sensitivity = _calculate_rent_sensitivity(inputs)
    expense_sensitivity = _calculate_expense_sensitivity(inputs)
    
    return {
        "base_case_npv": base_npv,
        "best_case_npv": best_case["npv"],
        "worst_case_npv": worst_case["npv"],
        "npv_range": best_case["npv"] - worst_case["npv"],
        "downside_risk": max(0, base_npv - worst_case["npv"]),
        "upside_potential": max(0, best_case["npv"] - base_npv),
        "break_even_rent": break_even_rent,
        "rent_sensitivity_per_percent": rent_sensitivity,
        "expense_sensitivity_per_percent": expense_sensitivity,
        "scenario_results": scenario_results,
        "stress_test_summary": _generate_stress_summary(scenario_results, base_npv)
    }


def _modify_inputs(inputs: Dict[str, Any], adjustments: Dict[str, float]) -> Dict[str, Any]:
    """Create modified inputs based on stress test adjustments"""
    
    modified_inputs = inputs.copy()
    
    # Deep copy nested dictionaries
    for key in modified_inputs:
        if isinstance(modified_inputs[key], dict):
            modified_inputs[key] = modified_inputs[key].copy()
    
    # Apply rent adjustment
    if "rent_adjustment" in adjustments:
        original_rent = modified_inputs["revenue"]["base_rent_per_sqft"]
        modified_inputs["revenue"]["base_rent_per_sqft"] = original_rent * (1 + adjustments["rent_adjustment"])
    
    # Apply expense adjustment
    if "expense_adjustment" in adjustments:
        expense_multiplier = 1 + adjustments["expense_adjustment"]
        for expense_type in ["property_taxes", "insurance", "utilities", "maintenance", "management"]:
            if expense_type in modified_inputs["operating"]:
                modified_inputs["operating"][expense_type] *= expense_multiplier
    
    # Apply interest rate adjustment
    if "interest_rate_adjustment" in adjustments:
        modified_inputs["financing"]["interest_rate"] += adjustments["interest_rate_adjustment"]
    
    # Apply exit cap rate adjustment
    if "exit_cap_adjustment" in adjustments:
        modified_inputs["assumptions"]["exit_cap_rate"] += adjustments["exit_cap_adjustment"]
    
    # Apply construction cost adjustment
    if "construction_cost_adjustment" in adjustments:
        cost_multiplier = 1 + adjustments["construction_cost_adjustment"]
        modified_inputs["costs"]["development_cost"] *= cost_multiplier
        modified_inputs["development"]["total_project_cost"] = (
            modified_inputs["costs"]["land_cost"] +
            modified_inputs["costs"]["development_cost"] +
            modified_inputs["costs"]["soft_costs"] +
            modified_inputs["costs"]["financing_costs"]
        )
        
        # Recalculate financing based on new project cost
        loan_to_cost = modified_inputs["financing"]["loan_to_cost"]
        modified_inputs["financing"]["loan_amount"] = modified_inputs["development"]["total_project_cost"] * loan_to_cost
    
    return modified_inputs


def _calculate_break_even_rent(inputs: Dict[str, Any]) -> float:
    """Calculate break-even rent per square foot"""
    
    # Operating expenses
    total_operating_expenses = sum([
        inputs["operating"]["property_taxes"],
        inputs["operating"]["insurance"],
        inputs["operating"]["utilities"],
        inputs["operating"]["maintenance"],
        inputs["operating"]["management"]
    ])
    
    # Debt service
    loan_amount = inputs["financing"]["loan_amount"]
    interest_rate = inputs["financing"]["interest_rate"]
    loan_term = inputs["financing"]["loan_term_years"]
    
    # Calculate annual debt service using PMT formula
    if interest_rate > 0:
        monthly_rate = interest_rate / 12
        num_payments = loan_term * 12
        monthly_payment = loan_amount * (monthly_rate * (1 + monthly_rate) ** num_payments) / ((1 + monthly_rate) ** num_payments - 1)
        annual_debt_service = monthly_payment * 12
    else:
        annual_debt_service = loan_amount / loan_term
    
    # Break-even NOI
    break_even_noi = annual_debt_service
    
    # Break-even gross income
    break_even_gross_income = break_even_noi + total_operating_expenses
    
    # Break-even rent per square foot
    building_size = inputs["property"]["building_size_sqft"]
    break_even_rent = break_even_gross_income / building_size
    
    return round(break_even_rent, 2)


def _calculate_rent_sensitivity(inputs: Dict[str, Any]) -> float:
    """Calculate NPV sensitivity to 1% change in rent"""
    
    base_case = compute_cashflow(inputs)
    base_npv = base_case["npv"]
    
    # 1% rent increase
    adjusted_inputs = _modify_inputs(inputs, {"rent_adjustment": 0.01})
    adjusted_case = compute_cashflow(adjusted_inputs)
    
    return adjusted_case["npv"] - base_npv


def _calculate_expense_sensitivity(inputs: Dict[str, Any]) -> float:
    """Calculate NPV sensitivity to 1% change in expenses"""
    
    base_case = compute_cashflow(inputs)
    base_npv = base_case["npv"]
    
    # 1% expense increase
    adjusted_inputs = _modify_inputs(inputs, {"expense_adjustment": 0.01})
    adjusted_case = compute_cashflow(adjusted_inputs)
    
    return base_npv - adjusted_case["npv"]  # Positive value for NPV decrease


def _generate_stress_summary(scenario_results: Dict[str, float], base_npv: float) -> Dict[str, Any]:
    """Generate summary of stress test results"""
    
    # Calculate impact of each scenario
    impacts = {}
    for scenario, npv in scenario_results.items():
        impact = npv - base_npv
        impact_percent = (impact / base_npv * 100) if base_npv != 0 else 0
        impacts[scenario] = {
            "npv_impact": round(impact, 0),
            "percent_impact": round(impact_percent, 1)
        }
    
    # Find most positive and negative impacts
    best_scenario = max(impacts.items(), key=lambda x: x[1]["npv_impact"])
    worst_scenario = min(impacts.items(), key=lambda x: x[1]["npv_impact"])
    
    # Count scenarios with negative impact
    negative_scenarios = sum(1 for impact in impacts.values() if impact["npv_impact"] < 0)
    
    return {
        "scenario_impacts": impacts,
        "best_scenario": {
            "name": best_scenario[0],
            "impact": best_scenario[1]
        },
        "worst_scenario": {
            "name": worst_scenario[0],
            "impact": worst_scenario[1]
        },
        "negative_scenario_count": negative_scenarios,
        "total_scenarios": len(impacts),
        "risk_score": (negative_scenarios / len(impacts)) * 100 if impacts else 0
    }


def run_monte_carlo_analysis(inputs: Dict[str, Any], iterations: int = 1000) -> Dict[str, Any]:
    """Run Monte Carlo simulation on key variables"""
    
    np.random.seed(42)  # For reproducible results
    
    npv_results = []
    irr_results = []
    
    for _ in range(iterations):
        # Generate random adjustments within reasonable ranges
        rent_adjustment = np.random.normal(0, 0.10)  # ±10% standard deviation
        expense_adjustment = np.random.normal(0, 0.08)  # ±8% standard deviation
        interest_adjustment = np.random.normal(0, 0.005)  # ±50 basis points
        cap_adjustment = np.random.normal(0, 0.003)  # ±30 basis points
        
        # Apply adjustments
        adjusted_inputs = _modify_inputs(inputs, {
            "rent_adjustment": rent_adjustment,
            "expense_adjustment": expense_adjustment,
            "interest_rate_adjustment": interest_adjustment,
            "exit_cap_adjustment": cap_adjustment
        })
        
        # Calculate results
        result = compute_cashflow(adjusted_inputs)
        npv_results.append(result["npv"])
        irr_results.append(result["irr"])
    
    # Calculate statistics
    npv_stats = {
        "mean": np.mean(npv_results),
        "median": np.median(npv_results),
        "std_dev": np.std(npv_results),
        "min": np.min(npv_results),
        "max": np.max(npv_results),
        "percentile_5": np.percentile(npv_results, 5),
        "percentile_95": np.percentile(npv_results, 95),
        "probability_positive": sum(1 for npv in npv_results if npv > 0) / len(npv_results)
    }
    
    irr_stats = {
        "mean": np.mean(irr_results),
        "median": np.median(irr_results),
        "std_dev": np.std(irr_results),
        "min": np.min(irr_results),
        "max": np.max(irr_results),
        "percentile_5": np.percentile(irr_results, 5),
        "percentile_95": np.percentile(irr_results, 95)
    }
    
    return {
        "npv_statistics": npv_stats,
        "irr_statistics": irr_stats,
        "iterations": iterations
    } 