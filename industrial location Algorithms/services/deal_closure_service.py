"""
Deal closure service for transaction management
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from datetime import datetime, timedelta

from models.deal import Deal, Task, Document, DealStage, TaskStatus
from api_clients.dealpath_client import DealpathClient
from deal_closure.pipeline import advance_stage
from deal_closure.tasks import sync_tasks
from deal_closure.documents import generate_docs
from deal_closure.compliance import verify_compliance


class DealClosureResult(BaseModel):
    """Result of deal closure workflow"""
    deal_id: str
    current_stage: str
    completion_percentage: float
    pending_tasks: List[str]
    completed_tasks: List[str]
    required_documents: List[str]
    compliance_status: str
    estimated_closing_date: Optional[str]
    next_actions: List[str]
    
    def to_dict(self) -> dict:
        return self.model_dump()


class DealClosureService:
    """Deal closure workflow management service"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Initialize Dealpath client if available
        if config.get("api_keys", {}).get("dealpath"):
            self.dealpath_client = DealpathClient(
                api_key=config["api_keys"]["dealpath"]
            )
        else:
            self.dealpath_client = None
    
    def run_deal_closure(self, deal_id: str) -> DealClosureResult:
        """Run deal closure workflow"""
        
        # 1. Fetch deal data
        if self.dealpath_client:
            deal = self.dealpath_client.fetch_deal(deal_id)
        else:
            deal = self._create_mock_deal(deal_id)
        
        # 2. Advance pipeline stage if needed
        updated_deal = advance_stage(deal)
        
        # 3. Sync tasks
        tasks = sync_tasks(updated_deal)
        
        # 4. Generate required documents
        documents = generate_docs(updated_deal)
        
        # 5. Verify compliance
        compliance_result = verify_compliance(updated_deal)
        
        # 6. Calculate completion metrics
        completion_pct = self._calculate_completion(tasks)
        pending_tasks = [task.name for task in tasks if task.status == TaskStatus.PENDING]
        completed_tasks = [task.name for task in tasks if task.status == TaskStatus.COMPLETED]
        
        # 7. Estimate closing date
        estimated_closing = self._estimate_closing_date(updated_deal, tasks)
        
        # 8. Generate next actions
        next_actions = self._generate_next_actions(updated_deal, tasks, compliance_result)
        
        return DealClosureResult(
            deal_id=deal_id,
            current_stage=updated_deal.current_stage.value,
            completion_percentage=completion_pct,
            pending_tasks=pending_tasks,
            completed_tasks=completed_tasks,
            required_documents=[doc.name for doc in documents],
            compliance_status=compliance_result["status"],
            estimated_closing_date=estimated_closing,
            next_actions=next_actions
        )
    
    def _create_mock_deal(self, deal_id: str) -> Deal:
        """Create mock deal for testing"""
        return Deal(
            id=deal_id,
            name=f"Industrial Acquisition {deal_id}",
            parcel_id="parcel_123",
            current_stage=DealStage.DUE_DILIGENCE,
            deal_type="purchase",
            deal_value=2500000,
            purchase_price=2500000,
            buyer="Industrial Capital LLC",
            seller="Property Holdings Corp",
            target_closing_date=datetime.now() + timedelta(days=45)
        )
    
    def _calculate_completion(self, tasks: List[Task]) -> float:
        """Calculate completion percentage based on tasks"""
        if not tasks:
            return 0.0
        
        completed = len([task for task in tasks if task.status == TaskStatus.COMPLETED])
        return (completed / len(tasks)) * 100
    
    def _estimate_closing_date(self, deal: Deal, tasks: List[Task]) -> Optional[str]:
        """Estimate realistic closing date"""
        pending_tasks = [task for task in tasks if task.status != TaskStatus.COMPLETED]
        
        if not pending_tasks:
            # All tasks complete, can close soon
            estimated_date = datetime.now() + timedelta(days=7)
        else:
            # Estimate based on remaining work
            days_needed = len(pending_tasks) * 5  # 5 days per task average
            estimated_date = datetime.now() + timedelta(days=days_needed)
        
        # Don't go past target closing date
        if deal.target_closing_date and estimated_date > deal.target_closing_date:
            estimated_date = deal.target_closing_date
        
        return estimated_date.strftime("%Y-%m-%d")
    
    def _generate_next_actions(self, deal: Deal, tasks: List[Task], 
                             compliance_result: Dict[str, Any]) -> List[str]:
        """Generate prioritized next actions"""
        actions = []
        
        # Overdue tasks
        overdue_tasks = [task for task in tasks 
                        if task.due_date and task.due_date < datetime.now() 
                        and task.status != TaskStatus.COMPLETED]
        
        if overdue_tasks:
            actions.append(f"URGENT: Complete {len(overdue_tasks)} overdue tasks")
        
        # High priority pending tasks
        pending_tasks = [task for task in tasks if task.status == TaskStatus.PENDING]
        if pending_tasks:
            actions.append(f"Complete {len(pending_tasks)} pending tasks")
        
        # Compliance issues
        if compliance_result["status"] != "compliant":
            actions.append("Address compliance issues")
        
        # Stage-specific actions
        if deal.current_stage == DealStage.DUE_DILIGENCE:
            actions.append("Complete due diligence review")
        elif deal.current_stage == DealStage.FINANCING:
            actions.append("Finalize loan documents")
        elif deal.current_stage == DealStage.CLOSING:
            actions.append("Prepare for closing")
        
        return actions[:5]  # Limit to top 5 actions