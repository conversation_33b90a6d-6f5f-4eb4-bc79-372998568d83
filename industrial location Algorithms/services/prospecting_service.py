"""
Prospecting service for industrial site discovery and evaluation
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel

from models.parcel import Parcel
from models.labor import LaborMetrics
from models.energy import EnergyRates
from models.transport import TransportMetrics
from models.demographics import Demographics
from models.incentives import IncentiveData
from api_clients.regrid_client import RegridClient
from api_clients.bls_client import BLSClient
from api_clients.eia_client import EIAClient
from api_clients.maps_client import MapsClient
from api_clients.census_client import CensusClient
from api_clients.incentives_client import IncentivesClient
from prospecting.parcel_prospecting import enrich_owner_data
from prospecting.strategic_value import calculate_strategic_overlay
from prospecting.negotiate_listing import evaluate_exclusive_listing


class MonetizedProspectResult(BaseModel):
    """Result of monetized prospecting analysis"""
    parcel: Parcel
    
    # Financial Value Estimates (in dollars)
    estimated_market_value: float
    cost_savings_potential: float
    revenue_enhancement_potential: float
    risk_adjusted_value: float
    
    # Cost Analysis
    acquisition_cost: float
    development_cost_estimate: float
    operating_cost_annual: float
    
    # Revenue Analysis
    potential_rental_income: float
    sale_value_estimate: float
    
    # Value Drivers (dollar impacts)
    location_value_premium: float
    transportation_cost_savings: float
    labor_cost_advantage: float
    energy_cost_savings: float
    incentive_value: float
    
    # Investment Metrics
    estimated_roi_percent: float
    payback_period_years: float
    net_present_value: float
    
    # Qualitative factors that support the numbers
    strategic_advantages: List[str]
    risk_factors: List[str]
    
    def to_dict(self) -> dict:
        return self.model_dump()


class ProspectingService:
    """Main service for prospecting and site evaluation"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Initialize API clients
        self.regrid_client = RegridClient(config.get("api_keys", {}).get("regrid"))
        self.bls_client = BLSClient(config.get("api_keys", {}).get("bls"))
        self.eia_client = EIAClient(config.get("api_keys", {}).get("eia"))
        self.maps_client = MapsClient(config.get("api_keys", {}).get("google_maps"))
        self.census_client = CensusClient(config.get("api_keys", {}).get("census"))
        self.incentives_client = IncentivesClient(config.get("api_keys", {}).get("incentives"))
    
    def run_prospecting(self, latitude: float, longitude: float, 
                       radius_miles: float = 5.0,
                       target_industry: str = "manufacturing") -> MonetizedProspectResult:
        """Run complete prospecting analysis with monetized results"""
        
        # 1. Find parcels in area
        parcels = self.regrid_client.search_parcels(
            latitude=latitude,
            longitude=longitude,
            radius_miles=radius_miles
        )
        
        if not parcels:
            raise ValueError("No parcels found in specified area")
        
        # For now, analyze the first parcel
        parcel = parcels[0]
        
        # 2. Gather supporting data
        labor_data = self.bls_client.get_labor_data(
            latitude=latitude,
            longitude=longitude
        )
        
        energy_data = self.eia_client.get_energy_rates(
            latitude=latitude,
            longitude=longitude
        )
        
        transport_data = self.maps_client.calculate_transportation_metrics(
            origin_lat=latitude,
            origin_lng=longitude
        )
        
        demographics = self.census_client.get_demographics(
            latitude=latitude,
            longitude=longitude
        )
        
        incentives = self.incentives_client.get_incentives(
            latitude=latitude,
            longitude=longitude,
            industry=target_industry
        )
        
        # 3. Enrich parcel data
        enriched_parcel = enrich_owner_data(parcel)
        
        # 4. Calculate strategic value overlay
        strategic_overlay = calculate_strategic_overlay(
            enriched_parcel, labor_data, energy_data, transport_data, demographics
        )
        
        # 5. Evaluate listing potential
        listing_evaluation = evaluate_exclusive_listing(
            enriched_parcel, strategic_overlay
        )
        
        # 6. Convert to monetized analysis
        monetized_result = self._calculate_monetized_value(
            enriched_parcel, labor_data, energy_data, transport_data, 
            demographics, incentives, strategic_overlay, target_industry
        )
        
        return monetized_result
    
    def _calculate_monetized_value(self, parcel: Parcel, labor: LaborMetrics, 
                                 energy: EnergyRates, transport: TransportMetrics,
                                 demographics: Demographics, incentives: IncentiveData,
                                 strategic_overlay: Dict[str, Any], 
                                 target_industry: str) -> MonetizedProspectResult:
        """Calculate actual dollar values for investment analysis"""
        
        # Base property metrics
        lot_size = parcel.lot_size or 250000  # Default 250k sqft
        building_size = lot_size * 0.5  # 50% lot coverage
        
        # Market value calculation
        base_land_value_psf = parcel.market_value / lot_size if parcel.market_value and lot_size > 0 else 25
        estimated_market_value = base_land_value_psf * lot_size
        
        # Development cost estimates
        development_cost_psf = 85 if target_industry == "manufacturing" else 65  # $/sqft
        development_cost_estimate = building_size * development_cost_psf
        
        # Total acquisition cost
        acquisition_cost = estimated_market_value + development_cost_estimate
        
        # Revenue potential
        market_rent_psf = 8.50 if target_industry == "manufacturing" else 6.75  # $/sqft/year
        potential_rental_income = building_size * market_rent_psf
        
        # Location value premium (based on strategic score)
        location_score = strategic_overlay.get("location", {}).get("total_score", 70)
        location_premium_factor = (location_score - 50) / 50  # -1 to +1 scale
        location_value_premium = estimated_market_value * location_premium_factor * 0.15  # Up to 15% premium
        
        # Transportation cost savings
        transport_score = strategic_overlay.get("transportation", {}).get("total_score", 70)
        # Better transport = lower logistics costs
        annual_transport_savings = (transport_score - 50) * 200 * building_size / 1000  # $200 per 1000 sqft per score point
        transportation_cost_savings = annual_transport_savings * 10  # 10-year NPV
        
        # Labor cost advantage/disadvantage
        market_avg_wage = 65000  # $65k average industrial wage
        local_wage = labor.average_wage or market_avg_wage
        wage_differential = market_avg_wage - local_wage
        employees_per_1000_sqft = 2.5 if target_industry == "manufacturing" else 3.5
        annual_labor_savings = wage_differential * (building_size / 1000) * employees_per_1000_sqft
        labor_cost_advantage = annual_labor_savings * 10  # 10-year NPV
        
        # Energy cost savings
        market_avg_electricity = 0.12  # $0.12/kWh
        local_electricity = energy.electricity_rate or market_avg_electricity
        electricity_savings_per_kwh = market_avg_electricity - local_electricity
        annual_kwh_usage = building_size * 12  # 12 kWh per sqft per year
        annual_energy_savings = electricity_savings_per_kwh * annual_kwh_usage
        energy_cost_savings = annual_energy_savings * 10  # 10-year NPV
        
        # Incentive value
        incentive_value = incentives.calculate_total_savings(
            property_value=estimated_market_value,
            annual_payroll=labor.average_wage * (building_size / 1000) * employees_per_1000_sqft,
            employees=(building_size / 1000) * employees_per_1000_sqft
        )
        
        # Operating costs
        property_tax_rate = incentives.property_tax_rate or 0.015
        annual_property_taxes = estimated_market_value * property_tax_rate
        annual_insurance = estimated_market_value * 0.003  # 0.3% of value
        annual_utilities = building_size * 2.50  # $2.50/sqft/year
        annual_maintenance = building_size * 1.75  # $1.75/sqft/year
        operating_cost_annual = annual_property_taxes + annual_insurance + annual_utilities + annual_maintenance
        
        # Risk adjustment
        infrastructure_score = strategic_overlay.get("infrastructure", {}).get("total_score", 70)
        market_score = strategic_overlay.get("market", {}).get("total_score", 70)
        risk_score = strategic_overlay.get("risk", {}).get("total_score", 70)
        
        avg_score = (infrastructure_score + market_score + risk_score) / 3
        risk_factor = 1.0 - ((avg_score - 50) / 100)  # Lower scores = higher risk discount
        risk_adjustment = acquisition_cost * risk_factor * 0.10  # Up to 10% risk discount
        
        # Cost savings potential
        cost_savings_potential = (transportation_cost_savings + 
                                labor_cost_advantage + 
                                energy_cost_savings + 
                                incentive_value)
        
        # Revenue enhancement potential
        revenue_enhancement_potential = location_value_premium + (potential_rental_income * 0.05)  # 5% premium for good location
        
        # Risk adjusted value
        risk_adjusted_value = (estimated_market_value + 
                             cost_savings_potential + 
                             revenue_enhancement_potential - 
                             risk_adjustment)
        
        # Sale value estimate (5 years)
        appreciation_rate = 0.03  # 3% annual appreciation
        sale_value_estimate = risk_adjusted_value * (1 + appreciation_rate) ** 5
        
        # Investment metrics
        total_benefits = cost_savings_potential + revenue_enhancement_potential
        roi_percent = (total_benefits / acquisition_cost) * 100 if acquisition_cost > 0 else 0
        payback_period = acquisition_cost / (potential_rental_income - operating_cost_annual) if (potential_rental_income - operating_cost_annual) > 0 else 999
        
        # NPV calculation (simplified)
        annual_net_income = potential_rental_income - operating_cost_annual
        discount_rate = 0.10
        npv = -acquisition_cost
        for year in range(1, 11):  # 10 years
            npv += annual_net_income / (1 + discount_rate) ** year
        npv += sale_value_estimate / (1 + discount_rate) ** 10
        
        # Strategic advantages and risks
        strategic_advantages = []
        risk_factors = []
        
        if location_value_premium > 0:
            strategic_advantages.append(f"Premium location adds ${location_value_premium:,.0f} in value")
        if labor_cost_advantage > 0:
            strategic_advantages.append(f"Labor cost advantage worth ${labor_cost_advantage:,.0f} over 10 years")
        if energy_cost_savings > 0:
            strategic_advantages.append(f"Energy savings of ${energy_cost_savings:,.0f} over 10 years")
        if incentive_value > 0:
            strategic_advantages.append(f"Tax incentives worth ${incentive_value:,.0f}")
        
        if risk_adjustment > 0:
            risk_factors.append(f"Market/infrastructure risks reduce value by ${risk_adjustment:,.0f}")
        if payback_period > 10:
            risk_factors.append("Long payback period indicates poor cash flow")
        if labor_cost_advantage < 0:
            risk_factors.append(f"Higher labor costs add ${abs(labor_cost_advantage):,.0f} expense")
        
        return MonetizedProspectResult(
            parcel=parcel,
            estimated_market_value=round(estimated_market_value, 0),
            cost_savings_potential=round(cost_savings_potential, 0),
            revenue_enhancement_potential=round(revenue_enhancement_potential, 0),
            risk_adjusted_value=round(risk_adjusted_value, 0),
            acquisition_cost=round(acquisition_cost, 0),
            development_cost_estimate=round(development_cost_estimate, 0),
            operating_cost_annual=round(operating_cost_annual, 0),
            potential_rental_income=round(potential_rental_income, 0),
            sale_value_estimate=round(sale_value_estimate, 0),
            location_value_premium=round(location_value_premium, 0),
            transportation_cost_savings=round(transportation_cost_savings, 0),
            labor_cost_advantage=round(labor_cost_advantage, 0),
            energy_cost_savings=round(energy_cost_savings, 0),
            incentive_value=round(incentive_value, 0),
            estimated_roi_percent=round(roi_percent, 1),
            payback_period_years=round(payback_period, 1),
            net_present_value=round(npv, 0),
            strategic_advantages=strategic_advantages,
            risk_factors=risk_factors
        ) 