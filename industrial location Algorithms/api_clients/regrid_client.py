"""
Regrid API client for parcel data
"""

import requests
from typing import Optional, Dict, Any
import time
from models.parcel import Parcel


class RegridClient:
    """Client for Regrid parcel data API"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.regrid.com/api/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        })
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.1  # Minimum seconds between requests
    
    def _rate_limit(self):
        """Implement rate limiting"""
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a rate-limited API request"""
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Regrid API request failed: {e}")
    
    def fetch_parcel(self, parcel_id: str) -> Parcel:
        """Fetch parcel data by ID"""
        try:
            data = self._make_request(f"parcels/{parcel_id}")
            
            # Extract parcel information from Regrid response
            parcel_data = data.get("results", [{}])[0] if data.get("results") else {}
            
            return Parcel(
                id=parcel_id,
                lat=parcel_data.get("centroid", {}).get("coordinates", [0, 0])[1],
                lng=parcel_data.get("centroid", {}).get("coordinates", [0, 0])[0],
                land_use=parcel_data.get("land_use_general", "unknown"),
                lot_size=parcel_data.get("area_sqft", 0),
                assessed_value=parcel_data.get("assessed_value"),
                address=parcel_data.get("address"),
                city=parcel_data.get("city"),
                county=parcel_data.get("county"),
                state=parcel_data.get("state"),
                zip_code=parcel_data.get("zip"),
                zoning_code=parcel_data.get("zoning"),
                owner_name=parcel_data.get("owner"),
                metadata={"regrid_data": parcel_data}
            )
        except Exception as e:
            raise Exception(f"Failed to fetch parcel {parcel_id}: {e}")
    
    def get_boundaries(self, parcel_id: str) -> Dict[str, Any]:
        """Get parcel boundary geometry"""
        try:
            data = self._make_request(f"parcels/{parcel_id}/boundaries")
            return data.get("geometry", {})
        except Exception as e:
            raise Exception(f"Failed to get boundaries for parcel {parcel_id}: {e}")
    
    def get_owner_info(self, parcel_id: str) -> Dict[str, Any]:
        """Get detailed owner information"""
        try:
            data = self._make_request(f"parcels/{parcel_id}/ownership")
            return {
                "owner_name": data.get("owner_name"),
                "mailing_address": data.get("mailing_address"),
                "owner_type": data.get("owner_type"),
                "deed_date": data.get("deed_date"),
                "sale_date": data.get("sale_date"),
                "sale_price": data.get("sale_price")
            }
        except Exception as e:
            raise Exception(f"Failed to get owner info for parcel {parcel_id}: {e}")
    
    def get_assessed_value(self, parcel_id: str) -> Dict[str, Any]:
        """Get assessment and tax information"""
        try:
            data = self._make_request(f"parcels/{parcel_id}/assessment")
            return {
                "assessed_value": data.get("assessed_value"),
                "market_value": data.get("market_value"),
                "land_value": data.get("land_value"),
                "improvement_value": data.get("improvement_value"),
                "tax_amount": data.get("tax_amount"),
                "assessment_year": data.get("assessment_year")
            }
        except Exception as e:
            raise Exception(f"Failed to get assessed value for parcel {parcel_id}: {e}")
    
    def search_parcels(self, 
                      lat: float, 
                      lng: float, 
                      radius_miles: float = 1.0,
                      land_use: Optional[str] = None,
                      min_size: Optional[float] = None,
                      max_size: Optional[float] = None) -> list:
        """Search for parcels near a location"""
        try:
            params = {
                "lat": lat,
                "lng": lng,
                "radius": radius_miles,
                "limit": 100
            }
            
            if land_use:
                params["land_use"] = land_use
            if min_size:
                params["min_area"] = min_size
            if max_size:
                params["max_area"] = max_size
            
            data = self._make_request("parcels/search", params)
            
            parcels = []
            for parcel_data in data.get("results", []):
                parcel = Parcel(
                    id=parcel_data.get("id"),
                    lat=parcel_data.get("centroid", {}).get("coordinates", [0, 0])[1],
                    lng=parcel_data.get("centroid", {}).get("coordinates", [0, 0])[0],
                    land_use=parcel_data.get("land_use_general", "unknown"),
                    lot_size=parcel_data.get("area_sqft", 0),
                    assessed_value=parcel_data.get("assessed_value"),
                    address=parcel_data.get("address"),
                    city=parcel_data.get("city"),
                    county=parcel_data.get("county"),
                    state=parcel_data.get("state"),
                    zip_code=parcel_data.get("zip"),
                    metadata={"regrid_data": parcel_data}
                )
                parcels.append(parcel)
            
            return parcels
        except Exception as e:
            raise Exception(f"Failed to search parcels: {e}")
    
    def get_comparable_sales(self, parcel_id: str, radius_miles: float = 0.5) -> list:
        """Get comparable sales in the area"""
        try:
            params = {
                "parcel_id": parcel_id,
                "radius": radius_miles,
                "limit": 20
            }
            
            data = self._make_request("parcels/comps", params)
            return data.get("results", [])
        except Exception as e:
            raise Exception(f"Failed to get comparable sales for parcel {parcel_id}: {e}") 