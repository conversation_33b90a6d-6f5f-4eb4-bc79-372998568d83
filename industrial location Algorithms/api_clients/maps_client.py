"""
Google Maps API client for distance and directions
"""

import googlemaps
from typing import Optional, Dict, Any, List, Tuple
import time
from models.transport import TransportMetrics


class MapsClient:
    """Client for Google Maps API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = googlemaps.Client(key=api_key)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.1
    
    def _rate_limit(self):
        """Implement rate limiting"""
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()
    
    def get_distance_matrix(self, origins: List[Tuple[float, float]], 
                           destinations: List[Tuple[float, float]]) -> Dict[str, Any]:
        """Get distance matrix between origins and destinations"""
        self._rate_limit()
        
        try:
            result = self.client.distance_matrix(
                origins=origins,
                destinations=destinations,
                mode="driving",
                units="imperial",
                avoid="tolls"
            )
            
            return self._parse_distance_matrix(result)
        except Exception as e:
            raise Exception(f"Failed to get distance matrix: {e}")
    
    def get_directions(self, origin: Tuple[float, float], 
                      destination: Tuple[float, float],
                      mode: str = "driving") -> Dict[str, Any]:
        """Get directions between two points"""
        self._rate_limit()
        
        try:
            result = self.client.directions(
                origin=origin,
                destination=destination,
                mode=mode,
                units="imperial",
                avoid="tolls"
            )
            
            if result:
                leg = result[0]["legs"][0]
                return {
                    "distance_miles": leg["distance"]["value"] * 0.000621371,  # meters to miles
                    "duration_minutes": leg["duration"]["value"] / 60,  # seconds to minutes
                    "duration_text": leg["duration"]["text"],
                    "distance_text": leg["distance"]["text"]
                }
            else:
                return {"distance_miles": 0, "duration_minutes": 0}
        except Exception as e:
            raise Exception(f"Failed to get directions: {e}")
    
    def _parse_distance_matrix(self, matrix_result: Dict) -> Dict[str, Any]:
        """Parse distance matrix API response"""
        distances = []
        durations = []
        
        for row in matrix_result.get("rows", []):
            row_distances = []
            row_durations = []
            
            for element in row.get("elements", []):
                if element["status"] == "OK":
                    distance_miles = element["distance"]["value"] * 0.000621371  # meters to miles
                    duration_minutes = element["duration"]["value"] / 60  # seconds to minutes
                    
                    row_distances.append(distance_miles)
                    row_durations.append(duration_minutes)
                else:
                    row_distances.append(None)
                    row_durations.append(None)
            
            distances.append(row_distances)
            durations.append(row_durations)
        
        return {
            "distances_miles": distances,
            "durations_minutes": durations,
            "status": matrix_result.get("status"),
            "origin_addresses": matrix_result.get("origin_addresses", []),
            "destination_addresses": matrix_result.get("destination_addresses", [])
        }
    
    def find_nearest_highway(self, location: Tuple[float, float]) -> Dict[str, Any]:
        """Find nearest highway access point"""
        self._rate_limit()
        
        try:
            # Search for nearby highways
            places_result = self.client.places_nearby(
                location=location,
                radius=16093,  # 10 miles in meters
                type="route",
                keyword="highway interstate"
            )
            
            if places_result["results"]:
                nearest = places_result["results"][0]
                highway_location = (
                    nearest["geometry"]["location"]["lat"],
                    nearest["geometry"]["location"]["lng"]
                )
                
                # Get distance to highway
                directions = self.get_directions(location, highway_location)
                
                return {
                    "highway_name": nearest.get("name", "Unknown Highway"),
                    "distance_miles": directions["distance_miles"],
                    "drive_time_minutes": directions["duration_minutes"],
                    "highway_location": highway_location
                }
            else:
                return {"highway_name": None, "distance_miles": None, "drive_time_minutes": None}
        except Exception as e:
            return {"highway_name": None, "distance_miles": None, "drive_time_minutes": None}
    
    def find_nearest_airport(self, location: Tuple[float, float]) -> Dict[str, Any]:
        """Find nearest airport"""
        self._rate_limit()
        
        try:
            places_result = self.client.places_nearby(
                location=location,
                radius=80467,  # 50 miles in meters
                type="airport"
            )
            
            if places_result["results"]:
                nearest = places_result["results"][0]
                airport_location = (
                    nearest["geometry"]["location"]["lat"],
                    nearest["geometry"]["location"]["lng"]
                )
                
                directions = self.get_directions(location, airport_location)
                
                return {
                    "airport_name": nearest.get("name", "Unknown Airport"),
                    "distance_miles": directions["distance_miles"],
                    "drive_time_minutes": directions["duration_minutes"],
                    "airport_location": airport_location,
                    "rating": nearest.get("rating"),
                    "types": nearest.get("types", [])
                }
            else:
                return {"airport_name": None, "distance_miles": None, "drive_time_minutes": None}
        except Exception as e:
            return {"airport_name": None, "distance_miles": None, "drive_time_minutes": None}
    
    def calculate_transport_metrics(self, location: Tuple[float, float], 
                                   targets: Dict[str, Tuple[float, float]]) -> TransportMetrics:
        """Calculate comprehensive transportation metrics"""
        try:
            lat, lng = location
            
            # Find nearest highway
            highway_info = self.find_nearest_highway(location)
            
            # Find nearest airport
            airport_info = self.find_nearest_airport(location)
            
            # Calculate distances to specific targets
            port_distance = None
            rail_distance = None
            port_drive_time = None
            rail_drive_time = None
            
            if "port" in targets:
                port_directions = self.get_directions(location, targets["port"])
                port_distance = port_directions["distance_miles"]
                port_drive_time = port_directions["duration_minutes"]
            
            if "rail" in targets:
                rail_directions = self.get_directions(location, targets["rail"])
                rail_distance = rail_directions["distance_miles"]
                rail_drive_time = rail_directions["duration_minutes"]
            
            return TransportMetrics(
                distance_to_highway=highway_info.get("distance_miles", 10.0),
                distance_to_port=port_distance,
                distance_to_airport=airport_info.get("distance_miles"),
                distance_to_rail=rail_distance,
                drive_time_to_highway=highway_info.get("drive_time_minutes"),
                drive_time_to_port=port_drive_time,
                drive_time_to_airport=airport_info.get("drive_time_minutes"),
                drive_time_to_rail=rail_drive_time,
                highway_name=highway_info.get("highway_name"),
                airport_name=airport_info.get("airport_name"),
                location_name=f"{lat:.4f}, {lng:.4f}",
                coordinates=(lat, lng),
                data_source="Google Maps",
                metadata={
                    "highway_info": highway_info,
                    "airport_info": airport_info,
                    "targets": targets
                }
            )
        except Exception as e:
            raise Exception(f"Failed to calculate transport metrics: {e}")
    
    def geocode_address(self, address: str) -> Optional[Tuple[float, float]]:
        """Geocode an address to lat/lng coordinates"""
        self._rate_limit()
        
        try:
            result = self.client.geocode(address)
            if result:
                location = result[0]["geometry"]["location"]
                return (location["lat"], location["lng"])
            return None
        except Exception as e:
            return None
    
    def reverse_geocode(self, lat: float, lng: float) -> Optional[str]:
        """Reverse geocode coordinates to address"""
        self._rate_limit()
        
        try:
            result = self.client.reverse_geocode((lat, lng))
            if result:
                return result[0]["formatted_address"]
            return None
        except Exception as e:
            return None