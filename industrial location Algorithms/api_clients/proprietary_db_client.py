"""
Proprietary database client for internal data storage and retrieval
"""

import sqlite3
import json
from typing import Optional, Dict, Any, List
from pathlib import Path
from models.parcel import Parcel


class ProprietaryDBClient:
    """Client for internal proprietary database"""
    
    def __init__(self, db_path: str = "industrial_discovery.db"):
        self.db_path = Path(db_path)
        self._init_database()
    
    def _init_database(self):
        """Initialize database tables if they don't exist"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS parcels (
                    id TEXT PRIMARY KEY,
                    data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    parcel_id TEXT NOT NULL,
                    analysis_type TEXT NOT NULL,
                    results TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIG<PERSON> KEY (parcel_id) REFERENCES parcels (id)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS deals (
                    id TEXT PRIMARY KEY,
                    parcel_id TEXT,
                    data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
    
    def load_parcel(self, parcel_id: str) -> Optional[Parcel]:
        """Load parcel data from internal database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT data FROM parcels WHERE id = ?", 
                    (parcel_id,)
                )
                row = cursor.fetchone()
                
                if row:
                    parcel_data = json.loads(row[0])
                    return Parcel.from_dict(parcel_data)
                else:
                    return None
        except Exception as e:
            return None
    
    def save_parcel(self, parcel: Parcel) -> bool:
        """Save parcel data to internal database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                parcel_json = json.dumps(parcel.to_dict())
                
                conn.execute("""
                    INSERT OR REPLACE INTO parcels (id, data, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                """, (parcel.id, parcel_json))
                
                conn.commit()
                return True
        except Exception as e:
            return False
    
    def save_results(self, table_name: str, records: List[Dict[str, Any]]) -> bool:
        """Save analysis results to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                if table_name == "analysis_results":
                    for record in records:
                        conn.execute("""
                            INSERT INTO analysis_results (parcel_id, analysis_type, results)
                            VALUES (?, ?, ?)
                        """, (
                            record.get("parcel_id"),
                            record.get("analysis_type"),
                            json.dumps(record.get("results", {}))
                        ))
                
                elif table_name == "deals":
                    for record in records:
                        conn.execute("""
                            INSERT OR REPLACE INTO deals (id, parcel_id, data, updated_at)
                            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                        """, (
                            record.get("id"),
                            record.get("parcel_id"),
                            json.dumps(record)
                        ))
                
                conn.commit()
                return True
        except Exception as e:
            return False
    
    def get_analysis_history(self, parcel_id: str) -> List[Dict[str, Any]]:
        """Get analysis history for a parcel"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT analysis_type, results, created_at
                    FROM analysis_results
                    WHERE parcel_id = ?
                    ORDER BY created_at DESC
                """, (parcel_id,))
                
                results = []
                for row in cursor.fetchall():
                    results.append({
                        "analysis_type": row[0],
                        "results": json.loads(row[1]),
                        "created_at": row[2]
                    })
                
                return results
        except Exception as e:
            return []
    
    def search_parcels(self, criteria: Dict[str, Any]) -> List[Parcel]:
        """Search parcels based on criteria"""
        try:
            parcels = []
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT data FROM parcels")
                
                for row in cursor.fetchall():
                    parcel_data = json.loads(row[0])
                    parcel = Parcel.from_dict(parcel_data)
                    
                    # Apply filters
                    if self._matches_criteria(parcel, criteria):
                        parcels.append(parcel)
            
            return parcels
        except Exception as e:
            return []
    
    def _matches_criteria(self, parcel: Parcel, criteria: Dict[str, Any]) -> bool:
        """Check if parcel matches search criteria"""
        
        # City filter
        if "city" in criteria and criteria["city"].lower() not in (parcel.city or "").lower():
            return False
        
        # Land use filter
        if "land_use" in criteria and criteria["land_use"].lower() not in (parcel.land_use or "").lower():
            return False
        
        # Size filters
        if "min_size" in criteria and (not parcel.lot_size or parcel.lot_size < criteria["min_size"]):
            return False
        
        if "max_size" in criteria and (not parcel.lot_size or parcel.lot_size > criteria["max_size"]):
            return False
        
        # Value filters
        if "min_value" in criteria and (not parcel.assessed_value or parcel.assessed_value < criteria["min_value"]):
            return False
        
        if "max_value" in criteria and (not parcel.assessed_value or parcel.assessed_value > criteria["max_value"]):
            return False
        
        return True
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM parcels")
                parcel_count = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM analysis_results")
                analysis_count = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM deals")
                deal_count = cursor.fetchone()[0]
                
                return {
                    "total_parcels": parcel_count,
                    "total_analyses": analysis_count,
                    "total_deals": deal_count,
                    "database_size_mb": self.db_path.stat().st_size / (1024 * 1024)
                }
        except Exception as e:
            return {"error": str(e)} 