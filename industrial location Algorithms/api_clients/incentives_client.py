"""
Incentives API client for tax and regulatory benefits
"""

import requests
from typing import Optional, Dict, Any, List
import time
from models.incentives import IncentiveData


class IncentivesClient:
    """Client for tax incentives and economic development data"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.session = requests.Session()
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0
    
    def _rate_limit(self):
        """Implement rate limiting"""
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()
    
    def fetch_incentives(self, parcel_id: str, location: Dict[str, str] = None) -> IncentiveData:
        """Fetch tax incentives and benefits for a location"""
        try:
            # In real implementation, would call multiple sources:
            # - State economic development agencies
            # - Local tax assessor databases
            # - Federal incentive programs
            
            if location:
                city = location.get("city", "Dallas")
                county = location.get("county", "Dallas County")
                state = location.get("state", "TX")
            else:
                city, county, state = "Dallas", "Dallas County", "TX"
            
            return self._get_mock_incentives(city, county, state)
            
        except Exception as e:
            return self._get_default_incentives()
    
    def _get_mock_incentives(self, city: str, county: str, state: str) -> IncentiveData:
        """Generate realistic incentive data based on location"""
        
        # Texas-specific incentive structure
        if state == "TX":
            return self._get_texas_incentives(city, county)
        else:
            return self._get_default_incentives()
    
    def _get_texas_incentives(self, city: str, county: str) -> IncentiveData:
        """Get Texas-specific incentive data with real dollar calculations"""
        
        # Base property tax rates for Texas counties
        property_tax_rates = {
            "Dallas County": 0.0247,
            "Harris County": 0.0231,  # Houston
            "Travis County": 0.0193,  # Austin
            "Bexar County": 0.0215   # San Antonio
        }
        
        base_rate = property_tax_rates.get(county, 0.024)
        
        # Determine available programs based on city
        major_cities = ["Dallas", "Houston", "Austin", "San Antonio"]
        is_major_city = city in major_cities
        
        programs = []
        program_details = {}
        
        # Property tax abatement (common in Texas)
        abatement_available = True
        abatement_years = 10 if is_major_city else 7
        abatement_percentage = 0.75 if is_major_city else 0.50
        
        programs.append("Property Tax Abatement")
        program_details["Property Tax Abatement"] = {
            "duration_years": abatement_years,
            "percentage": abatement_percentage,
            "annual_savings_per_million": int(1000000 * base_rate * abatement_percentage),
            "total_savings_per_million": int(1000000 * base_rate * abatement_percentage * abatement_years)
        }
        
        # Texas Enterprise Zone Program
        if is_major_city:
            programs.append("Texas Enterprise Zone")
            program_details["Texas Enterprise Zone"] = {
                "job_creation_credit": 2500,  # per job created
                "minimum_jobs": 25,
                "sales_tax_refund": True,
                "potential_annual_benefit": 187500  # 75 jobs * $2500
            }
        
        # Chapter 380/381 Economic Development Programs
        programs.append("Chapter 380 Economic Development")
        program_details["Chapter 380 Economic Development"] = {
            "negotiable_incentives": True,
            "typical_range": "10-50% of new tax revenue",
            "cash_grants_available": True,
            "infrastructure_support": True
        }
        
        return IncentiveData(
            property_tax_rate=base_rate,
            sales_tax_rate=0.0825,  # Texas combined rate
            property_tax_abatement=abatement_available,
            abatement_years=abatement_years,
            abatement_percentage=abatement_percentage,
            enterprise_zone=is_major_city,
            opportunity_zone=True,  # Many Texas areas qualify
            job_creation_credits=is_major_city,
            jobs_required=25 if is_major_city else 10,
            wage_requirements=15.00,  # Minimum wage for credits
            investment_tax_credits=True,
            minimum_investment=1000000,
            utility_rebates=True,
            training_grants=True,
            training_amount=2500,  # Per employee
            programs=programs,
            program_details=program_details,
            jurisdiction=f"{city}, {county}, {state}",
            state=state,
            county=county,
            city=city,
            data_source="Texas Economic Development",
            metadata={
                "calculated_savings": {
                    "property_tax_savings_10_years": int(10000000 * base_rate * abatement_percentage * abatement_years),
                    "job_creation_benefits": 187500 if is_major_city else 62500,
                    "total_potential_benefits": int(10000000 * base_rate * abatement_percentage * abatement_years) + (187500 if is_major_city else 62500)
                }
            }
        )
    
    def _get_default_incentives(self) -> IncentiveData:
        """Get default incentive structure"""
        return IncentiveData(
            property_tax_rate=0.025,
            sales_tax_rate=0.08,
            property_tax_abatement=True,
            abatement_years=5,
            abatement_percentage=0.50,
            jurisdiction="Default Location",
            programs=["Basic Property Tax Abatement"],
            program_details={
                "Basic Property Tax Abatement": {
                    "duration_years": 5,
                    "percentage": 0.50,
                    "annual_savings_per_million": 12500,
                    "total_savings_per_million": 62500
                }
            },
            data_source="Default/Mock Data"
        )
    
    def calculate_incentive_value(self, incentives: IncentiveData, project_details: Dict[str, Any]) -> Dict[str, float]:
        """Calculate total dollar value of incentives for a specific project"""
        
        assessed_value = project_details.get("assessed_value", 10000000)  # $10M default
        jobs_created = project_details.get("jobs_created", 50)
        investment_amount = project_details.get("investment_amount", 15000000)  # $15M default
        
        total_value = 0.0
        breakdown = {}
        
        # Property tax abatement value
        if incentives.property_tax_abatement:
            annual_tax = assessed_value * incentives.property_tax_rate
            annual_savings = annual_tax * (incentives.abatement_percentage or 0.5)
            total_abatement = annual_savings * (incentives.abatement_years or 5)
            
            breakdown["property_tax_abatement"] = total_abatement
            total_value += total_abatement
        
        # Job creation credits
        if incentives.job_creation_credits and jobs_created >= (incentives.jobs_required or 10):
            job_credits = jobs_created * 2500  # Typical credit per job
            breakdown["job_creation_credits"] = job_credits
            total_value += job_credits
        
        # Investment tax credits
        if incentives.investment_tax_credits and investment_amount >= (incentives.minimum_investment or 1000000):
            investment_credit = min(investment_amount * 0.05, 500000)  # 5% up to $500k
            breakdown["investment_tax_credits"] = investment_credit
            total_value += investment_credit
        
        # Training grants
        if incentives.training_grants:
            training_value = jobs_created * (incentives.training_amount or 2000)
            breakdown["training_grants"] = training_value
            total_value += training_value
        
        # Utility rebates (estimated)
        if incentives.utility_rebates:
            utility_rebates = min(investment_amount * 0.02, 100000)  # 2% up to $100k
            breakdown["utility_rebates"] = utility_rebates
            total_value += utility_rebates
        
        breakdown["total_incentive_value"] = total_value
        breakdown["roi_on_incentives"] = (total_value / investment_amount) * 100 if investment_amount > 0 else 0
        
        return breakdown 