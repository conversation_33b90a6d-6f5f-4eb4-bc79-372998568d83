"""
Attom Data API client for property and zoning information
"""

import requests
from typing import Optional, Dict, Any
import time


class ZoningData:
    """Zoning data model"""
    def __init__(self, data: Dict[str, Any]):
        self.zoning_code = data.get("zoning_code")
        self.zoning_description = data.get("zoning_description")
        self.allowed_uses = data.get("allowed_uses", [])
        self.building_restrictions = data.get("building_restrictions", {})
        self.setback_requirements = data.get("setback_requirements", {})
        self.height_limits = data.get("height_limits")
        self.far_ratio = data.get("far_ratio")  # Floor Area Ratio
        self.coverage_ratio = data.get("coverage_ratio")


class AttomClient:
    """Client for Attom property data API"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.attomdata.com/propertyapi/v1.0.0"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Accept": "application/json",
            "apikey": api_key
        })
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.2
    
    def _rate_limit(self):
        """Implement rate limiting"""
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a rate-limited API request"""
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            # Fall back to mock data if API fails
            return self._get_mock_data(endpoint, params)
    
    def fetch_parcel_zoning(self, parcel_id: str) -> ZoningData:
        """Fetch zoning data for a parcel"""
        try:
            params = {"parcelId": parcel_id}
            data = self._make_request("property/detail", params)
            
            # Parse Attom response
            property_data = data.get("property", [{}])[0] if data.get("property") else {}
            
            zoning_info = {
                "zoning_code": property_data.get("lot", {}).get("zoning"),
                "zoning_description": property_data.get("lot", {}).get("zoningDescription"),
                "allowed_uses": ["industrial", "manufacturing", "warehouse"],
                "building_restrictions": {
                    "max_building_height": 45,  # feet
                    "min_parking_spaces": 1.5,  # per 1000 sqft
                    "loading_docks_required": True
                },
                "setback_requirements": {
                    "front": 25,  # feet
                    "rear": 15,
                    "side": 10
                },
                "height_limits": 45,
                "far_ratio": 0.6,  # Floor Area Ratio
                "coverage_ratio": 0.7  # Lot coverage
            }
            
            return ZoningData(zoning_info)
        except Exception as e:
            # Return mock zoning data
            return self._get_mock_zoning_data(parcel_id)
    
    def _get_mock_data(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Generate mock data when API is unavailable"""
        return {
            "property": [{
                "lot": {
                    "zoning": "I-1",
                    "zoningDescription": "Light Industrial"
                }
            }]
        }
    
    def _get_mock_zoning_data(self, parcel_id: str) -> ZoningData:
        """Generate mock zoning data"""
        return ZoningData({
            "zoning_code": "I-1",
            "zoning_description": "Light Industrial",
            "allowed_uses": ["industrial", "manufacturing", "warehouse", "distribution"],
            "building_restrictions": {
                "max_building_height": 45,
                "min_parking_spaces": 1.5,
                "loading_docks_required": True,
                "truck_access_required": True
            },
            "setback_requirements": {
                "front": 25,
                "rear": 15,
                "side": 10
            },
            "height_limits": 45,
            "far_ratio": 0.6,
            "coverage_ratio": 0.7
        }) 