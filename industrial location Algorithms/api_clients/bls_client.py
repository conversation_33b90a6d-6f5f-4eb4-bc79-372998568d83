"""
Bureau of Labor Statistics API client
"""

import requests
from typing import Optional, Dict, Any
import time
from models.labor import LaborMetrics


class BLSClient:
    """Client for Bureau of Labor Statistics API"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: str = "https://api.bls.gov/publicAPI/v2"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        
        if api_key:
            self.session.headers.update({
                "Content-Type": "application/json"
            })
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # BLS has stricter rate limits
    
    def _rate_limit(self):
        """Implement rate limiting"""
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()
    
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make a rate-limited API request"""
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        if self.api_key:
            data["registrationkey"] = self.api_key
        
        try:
            response = self.session.post(url, json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"BLS API request failed: {e}")
    
    def fetch_labor_stats(self, geographic_area: str) -> LaborMetrics:
        """Fetch labor statistics for a geographic area"""
        try:
            # Map geographic area to BLS area codes
            area_code = self._get_area_code(geographic_area)
            
            # Fetch employment and wage data
            employment_data = self._get_employment_data(area_code)
            wage_data = self._get_wage_data(area_code)
            
            return LaborMetrics(
                median_wage=wage_data.get("median_wage", 15.0),
                unemployment_rate=employment_data.get("unemployment_rate", 0.05),
                labor_force_count=employment_data.get("labor_force", 100000),
                manufacturing_employment=employment_data.get("manufacturing_jobs"),
                warehouse_employment=employment_data.get("warehouse_jobs"),
                construction_employment=employment_data.get("construction_jobs"),
                entry_level_wage=wage_data.get("entry_level_wage"),
                skilled_wage=wage_data.get("skilled_wage"),
                management_wage=wage_data.get("management_wage"),
                available_workforce=employment_data.get("available_workforce"),
                high_school_rate=employment_data.get("high_school_rate"),
                college_rate=employment_data.get("college_rate"),
                union_penetration=employment_data.get("union_rate"),
                geographic_level="msa",
                geographic_name=geographic_area,
                data_source="BLS",
                metadata={"raw_data": {"employment": employment_data, "wages": wage_data}}
            )
        except Exception as e:
            raise Exception(f"Failed to fetch labor stats for {geographic_area}: {e}")
    
    def _get_area_code(self, geographic_area: str) -> str:
        """Map geographic area to BLS area code"""
        # This would normally involve a lookup table or geocoding
        # For now, return a placeholder
        area_mapping = {
            "Dallas, TX": "19100",
            "Houston, TX": "26420",
            "Austin, TX": "12420",
            "San Antonio, TX": "41700"
        }
        return area_mapping.get(geographic_area, "19100")  # Default to Dallas
    
    def _get_employment_data(self, area_code: str) -> Dict[str, Any]:
        """Get employment statistics for area"""
        try:
            # Request unemployment rate
            unemployment_request = {
                "seriesid": [f"LAUMT{area_code}03"],
                "startyear": "2023",
                "endyear": "2023"
            }
            
            response = self._make_request("timeseries/data", unemployment_request)
            
            # Parse response (simplified)
            series_data = response.get("Results", {}).get("series", [])
            if series_data:
                latest_value = series_data[0].get("data", [{}])[0].get("value", "5.0")
                unemployment_rate = float(latest_value) / 100.0
            else:
                unemployment_rate = 0.05
            
            # Estimate other employment metrics based on area
            return {
                "unemployment_rate": unemployment_rate,
                "labor_force": 500000,  # Would come from additional BLS series
                "manufacturing_jobs": 45000,
                "warehouse_jobs": 25000,
                "construction_jobs": 35000,
                "available_workforce": int(500000 * unemployment_rate),
                "high_school_rate": 0.85,
                "college_rate": 0.35,
                "union_rate": 0.15
            }
        except Exception as e:
            # Return default values if API fails
            return {
                "unemployment_rate": 0.05,
                "labor_force": 500000,
                "manufacturing_jobs": 45000,
                "warehouse_jobs": 25000,
                "construction_jobs": 35000,
                "available_workforce": 25000,
                "high_school_rate": 0.85,
                "college_rate": 0.35,
                "union_rate": 0.15
            }
    
    def _get_wage_data(self, area_code: str) -> Dict[str, Any]:
        """Get wage statistics for area"""
        try:
            # Request average wage data
            wage_request = {
                "seriesid": [f"OEUM{area_code}000000000000003"],  # All occupations average wage
                "startyear": "2023",
                "endyear": "2023"
            }
            
            response = self._make_request("timeseries/data", wage_request)
            
            # Parse response (simplified)
            series_data = response.get("Results", {}).get("series", [])
            if series_data:
                latest_value = series_data[0].get("data", [{}])[0].get("value", "20.00")
                median_wage = float(latest_value)
            else:
                median_wage = 20.00
            
            return {
                "median_wage": median_wage,
                "entry_level_wage": median_wage * 0.7,
                "skilled_wage": median_wage * 1.3,
                "management_wage": median_wage * 2.0
            }
        except Exception as e:
            # Return default values if API fails
            return {
                "median_wage": 20.00,
                "entry_level_wage": 14.00,
                "skilled_wage": 26.00,
                "management_wage": 40.00
            }
    
    def get_industry_employment(self, area_code: str, industry_code: str) -> Dict[str, Any]:
        """Get employment data for specific industry"""
        try:
            request_data = {
                "seriesid": [f"SM{area_code}{industry_code}01"],
                "startyear": "2023",
                "endyear": "2023"
            }
            
            response = self._make_request("timeseries/data", request_data)
            
            series_data = response.get("Results", {}).get("series", [])
            if series_data:
                latest_value = series_data[0].get("data", [{}])[0].get("value", "0")
                employment = int(float(latest_value) * 1000)  # Convert from thousands
            else:
                employment = 0
            
            return {"employment": employment}
        except Exception as e:
            return {"employment": 0}
    
    def get_wage_by_occupation(self, area_code: str, occupation_code: str) -> Dict[str, Any]:
        """Get wage data for specific occupation"""
        try:
            request_data = {
                "seriesid": [f"OEUM{area_code}{occupation_code}03"],
                "startyear": "2023",
                "endyear": "2023"
            }
            
            response = self._make_request("timeseries/data", request_data)
            
            series_data = response.get("Results", {}).get("series", [])
            if series_data:
                latest_value = series_data[0].get("data", [{}])[0].get("value", "20.00")
                wage = float(latest_value)
            else:
                wage = 20.00
            
            return {"hourly_wage": wage, "annual_wage": wage * 2080}
        except Exception as e:
            return {"hourly_wage": 20.00, "annual_wage": 41600} 