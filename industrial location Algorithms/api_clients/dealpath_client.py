"""
Dealpath API client for deal management
"""

import requests
from typing import Optional, Dict, Any, List
import time
from models.deal import Deal, Task, Document, DealStage, TaskStatus


class DealpathClient:
    """Client for Dealpath deal management API"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.dealpath.com/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        })
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.5
    
    def _rate_limit(self):
        """Implement rate limiting"""
        elapsed = time.time() - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a rate-limited API request"""
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url)
            elif method.upper() == "POST":
                response = self.session.post(url, json=data)
            elif method.upper() == "PUT":
                response = self.session.put(url, json=data)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            # Fall back to mock data for testing
            return self._get_mock_deal_data(endpoint)
    
    def fetch_deal(self, deal_id: str) -> Deal:
        """Fetch deal by ID"""
        try:
            data = self._make_request("GET", f"deals/{deal_id}")
            return self._parse_deal_response(data)
        except Exception as e:
            return self._create_mock_deal(deal_id)
    
    def update_stage(self, deal_id: str, stage: DealStage) -> bool:
        """Update deal stage"""
        try:
            data = {"stage": stage.value}
            response = self._make_request("PUT", f"deals/{deal_id}/stage", data)
            return response.get("success", True)
        except Exception as e:
            return True  # Mock success
    
    def list_tasks(self, deal_id: str) -> List[Task]:
        """List all tasks for a deal"""
        try:
            data = self._make_request("GET", f"deals/{deal_id}/tasks")
            tasks = []
            for task_data in data.get("tasks", []):
                task = Task(
                    id=task_data.get("id"),
                    name=task_data.get("name"),
                    description=task_data.get("description"),
                    status=TaskStatus(task_data.get("status", "pending")),
                    assigned_to=task_data.get("assigned_to")
                )
                tasks.append(task)
            return tasks
        except Exception as e:
            return self._create_mock_tasks(deal_id)
    
    def create_task(self, deal_id: str, task_data: Dict[str, Any]) -> Task:
        """Create a new task for a deal"""
        try:
            response = self._make_request("POST", f"deals/{deal_id}/tasks", task_data)
            return Task(**response.get("task", task_data))
        except Exception as e:
            return Task(**task_data)
    
    def _parse_deal_response(self, data: Dict[str, Any]) -> Deal:
        """Parse API response into Deal object"""
        deal_data = data.get("deal", {})
        
        return Deal(
            id=deal_data.get("id"),
            name=deal_data.get("name"),
            parcel_id=deal_data.get("property_id"),
            current_stage=DealStage(deal_data.get("stage", "lead")),
            deal_type=deal_data.get("type", "purchase"),
            deal_value=deal_data.get("value"),
            buyer=deal_data.get("buyer"),
            seller=deal_data.get("seller"),
            purchase_price=deal_data.get("purchase_price"),
            target_closing_date=deal_data.get("target_closing"),
            metadata={"dealpath_data": deal_data}
        )
    
    def _get_mock_deal_data(self, endpoint: str) -> Dict[str, Any]:
        """Generate mock deal data"""
        return {
            "deal": {
                "id": "mock_deal_123",
                "name": "Industrial Property Acquisition",
                "property_id": "parcel_456",
                "stage": "due_diligence",
                "type": "purchase",
                "value": 2500000,
                "buyer": "Industrial Investor LLC",
                "seller": "Property Holdings Corp",
                "purchase_price": 2500000
            }
        }
    
    def _create_mock_deal(self, deal_id: str) -> Deal:
        """Create mock deal for testing"""
        return Deal(
            id=deal_id,
            name=f"Mock Deal {deal_id}",
            parcel_id="mock_parcel_123",
            current_stage=DealStage.DUE_DILIGENCE,
            deal_type="purchase",
            deal_value=2500000,
            buyer="Industrial Investor LLC",
            seller="Property Holdings Corp",
            purchase_price=2500000,
            metadata={"source": "mock_data"}
        )
    
    def _create_mock_tasks(self, deal_id: str) -> List[Task]:
        """Create mock tasks for testing"""
        return [
            Task(
                id="task_1",
                name="Environmental Assessment",
                description="Phase I Environmental Site Assessment",
                status=TaskStatus.IN_PROGRESS,
                assigned_to="Environmental Consultant"
            ),
            Task(
                id="task_2", 
                name="Title Review",
                description="Review property title and survey",
                status=TaskStatus.PENDING,
                assigned_to="Title Company"
            ),
            Task(
                id="task_3",
                name="Financing Approval",
                description="Secure acquisition financing",
                status=TaskStatus.PENDING,
                assigned_to="Lender"
            )
        ] 