# Industrial Site Selection System Design

## Overview

This system implements an end-to-end industrial site-selection pipeline that helps users identify and evaluate potential industrial locations, from initial cluster analysis to generating Letters of Intent (LOI). The system leverages enriched parcel data, dynamic cost modeling, and automated document generation.

## System Flow

```mermaid
graph TD
    A[1. Define Search Area & Criteria] --> B[2. Cluster Analysis]
    B --> C[3. Parcel Prospecting]
    C --> D[4. Ownership Research]
    D --> E[5. Use Analysis]
    E --> F[6. Interactive Underwriting]
    F --> G[7. Document Generation]
    G --> H[8. Export & Share]
```

## Project Structure

```
project_root/
├── data/
│   ├── raw/                   # Raw input data storage
│   └── processed/             # Processed data cache
│
├── src/
│   ├── data/
│   │   ├── data_simulator.py  # Generates mock parcel data for development
│   │   └── parcel_ingest.py   # Handles parcel data ingestion (CSV/API)
│   │
│   ├── analysis/ 
│   │   ├── cluster_Prospector.py  # Cluster analysis and spatial operations
│   │   ├── leads_prospector.py # Parcel filtering and ranking
│   │   ├── *skiptrace_client.py # Owner contact information lookup Via LLM API
│   │   └── Parcel_propsector.py    # CapEx/OpEx calculations
│   │
│   ├── underwriting/
│   │   └── financing.py      # Financial analysis related to mortgages and loans, from Parcel data
│   ├── document/
│   │   ├── templates/
│   │   │   ├── loi.md        # Letter of Intent template
│   │   │   ├── rfp.md        # Request for Proposal template
│   │   │   └── memo.md       # Underwriting memo template
│   │   └── generator.py      # Document generation logic
│   │
│   └── api/
│       ├── main.py           # FastAPI application
│       ├── routes.py         # API endpoints
│       └── schemas.py        # Data models and validation
│
├── config/
│   ├── config.py             # Configuration settings
│   └── logging.py           # Logging configuration
│
├── tests/                    # Unit and integration tests
├── requirements.txt          # Python dependencies
└── README.md                # Project documentation