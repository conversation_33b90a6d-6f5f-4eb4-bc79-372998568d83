"""
Parcel prospecting functions for owner contact enrichment
"""

from models.parcel import Parcel
from typing import Dict, Any


def obtain_contact(parcel: Parcel) -> Parcel:
    """Enrich parcel with owner contact information"""
    
    # In a real implementation, this would call external data sources
    # For now, we'll create mock contact data based on existing owner info
    
    if parcel.owner_name and not parcel.owner_contact:
        # Mock contact enrichment
        contact_info = _generate_mock_contact(parcel.owner_name, parcel.address)
        
        # Create updated parcel with contact info
        parcel_dict = parcel.to_dict()
        parcel_dict["owner_contact"] = contact_info
        
        return Parcel.from_dict(parcel_dict)
    
    return parcel


def _generate_mock_contact(owner_name: str, property_address: str) -> Dict[str, Any]:
    """Generate mock contact information for demonstration"""
    
    # Extract company type and create mock data
    is_company = any(suffix in owner_name.upper() for suffix in ['LLC', 'INC', 'CORP', 'LP'])
    
    if is_company:
        return {
            "type": "business",
            "company_name": owner_name,
            "contact_person": "Property Manager",
            "phone": "555-0123",
            "email": f"contact@{_clean_company_name(owner_name)}.com",
            "mailing_address": property_address,
            "business_type": "Real Estate Investment",
            "established_year": 2010,
            "confidence_score": 0.7
        }
    else:
        return {
            "type": "individual",
            "name": owner_name,
            "phone": "555-0456", 
            "email": f"{_clean_name(owner_name)}@email.com",
            "mailing_address": property_address,
            "estimated_age": 55,
            "confidence_score": 0.6
        }


def _clean_company_name(company_name: str) -> str:
    """Clean company name for email generation"""
    # Remove common suffixes and clean for domain name
    name = company_name.replace(" LLC", "").replace(" Inc", "").replace(" Corp", "")
    name = "".join(c.lower() for c in name if c.isalnum())
    return name[:15]  # Limit length


def _clean_name(full_name: str) -> str:
    """Clean individual name for email generation"""
    # Take first and last name
    parts = full_name.split()
    if len(parts) >= 2:
        return f"{parts[0].lower()}.{parts[-1].lower()}"
    else:
        return full_name.lower().replace(" ", ".")


def enrich_owner_history(parcel: Parcel) -> Dict[str, Any]:
    """Get ownership history and transaction data"""
    
    # Mock ownership history
    return {
        "ownership_duration": "5 years",
        "previous_owners": 2,
        "last_sale_date": "2019-03-15",
        "last_sale_price": parcel.assessed_value * 0.9 if parcel.assessed_value else 450000,
        "price_trend": "increasing",
        "transaction_frequency": "low"
    }


def assess_owner_motivation(parcel: Parcel) -> Dict[str, Any]:
    """Assess potential owner motivation to sell"""
    
    motivation_score = 50  # Default neutral
    factors = []
    
    # Analyze owner type
    if parcel.owner_contact:
        if parcel.owner_contact.get("type") == "business":
            motivation_score += 10
            factors.append("Business owner may be more transaction-oriented")
        
        # Property management companies often more willing to sell
        if "management" in parcel.owner_contact.get("company_name", "").lower():
            motivation_score += 15
            factors.append("Property management company - likely investment focused")
    
    # Large lot sizes may indicate development potential
    if parcel.lot_size and parcel.lot_size > 100000:
        motivation_score += 10
        factors.append("Large parcel - owner may consider development opportunities")
    
    # Industrial zoning increases investment appeal
    if parcel.zoning_code and "I" in parcel.zoning_code:
        motivation_score += 5
        factors.append("Industrial zoning attractive to investors")
    
    return {
        "motivation_score": min(100, motivation_score),
        "factors": factors,
        "recommended_approach": _get_approach_recommendation(motivation_score)
    }


def _get_approach_recommendation(score: int) -> str:
    """Get recommended approach based on motivation score"""
    
    if score >= 70:
        return "Direct acquisition offer - high probability of interest"
    elif score >= 50:
        return "Exploratory discussion - gauge interest level"
    else:
        return "Long-term relationship building - low immediate interest" 