"""
Strategic value computation functions
"""

from models.parcel import Parcel
from typing import Dict, Any
import math


def compute_overlays(parcel: Parcel) -> Dict[str, Any]:
    """Compute strategic value overlays for a parcel"""
    
    overlays = {}
    
    # Location analysis
    overlays.update(_analyze_location(parcel))
    
    # Transportation accessibility  
    overlays.update(_analyze_transportation(parcel))
    
    # Infrastructure availability
    overlays.update(_analyze_infrastructure(parcel))
    
    # Market context
    overlays.update(_analyze_market_context(parcel))
    
    # Risk assessment
    overlays.update(_analyze_risks(parcel))
    
    # Compute overall scores
    overlays["location_score"] = _compute_location_score(overlays)
    overlays["infrastructure_score"] = _compute_infrastructure_score(overlays)
    overlays["overall_strategic_value"] = _compute_overall_score(overlays)
    
    return overlays


def _analyze_location(parcel: Parcel) -> Dict[str, Any]:
    """Analyze location characteristics"""
    
    # Mock location analysis based on coordinates
    lat, lng = parcel.lat, parcel.lng
    
    # Proximity to major metros (using Dallas as reference)
    dallas_lat, dallas_lng = 32.7767, -96.7970
    distance_to_metro = _calculate_distance(lat, lng, dallas_lat, dallas_lng)
    
    return {
        "distance_to_metro": distance_to_metro,
        "metro_accessibility": "high" if distance_to_metro < 20 else "medium" if distance_to_metro < 50 else "low",
        "urban_density": "medium",  # Would be calculated from census data
        "industrial_cluster": True if "industrial" in parcel.land_use.lower() else False
    }


def _analyze_transportation(parcel: Parcel) -> Dict[str, Any]:
    """Analyze transportation infrastructure"""
    
    # Mock transportation analysis
    # In real implementation, would use Google Maps API
    
    distance_to_highway = parcel.distance_to_highway or _estimate_highway_distance(parcel)
    distance_to_port = parcel.distance_to_port or _estimate_port_distance(parcel)
    distance_to_airport = parcel.distance_to_airport or _estimate_airport_distance(parcel)
    distance_to_rail = parcel.distance_to_rail or _estimate_rail_distance(parcel)
    
    return {
        "distance_to_highway": distance_to_highway,
        "distance_to_port": distance_to_port,
        "distance_to_airport": distance_to_airport,
        "distance_to_rail": distance_to_rail,
        "highway_accessibility": "excellent" if distance_to_highway < 2 else "good" if distance_to_highway < 5 else "fair",
        "multimodal_access": distance_to_port < 100 and distance_to_rail < 10,
        "logistics_score": _calculate_logistics_score(distance_to_highway, distance_to_port, distance_to_rail)
    }


def _analyze_infrastructure(parcel: Parcel) -> Dict[str, Any]:
    """Analyze infrastructure availability"""
    
    return {
        "water_available": parcel.water_available if parcel.water_available is not None else True,
        "sewer_available": parcel.sewer_available if parcel.sewer_available is not None else True,
        "electric_available": parcel.electric_available if parcel.electric_available is not None else True,
        "gas_available": parcel.gas_available if parcel.gas_available is not None else True,
        "broadband_available": True,  # Assume broadband availability
        "infrastructure_readiness": "high",  # Would be calculated from utility data
        "utility_capacity": "adequate"  # Would come from utility providers
    }


def _analyze_market_context(parcel: Parcel) -> Dict[str, Any]:
    """Analyze market context and demand"""
    
    # Mock market analysis
    return {
        "industrial_demand": "high",  # Would come from market research
        "land_availability": "limited",  # Based on zoning and development patterns
        "price_trends": "increasing",  # From comparable sales
        "development_activity": "active",  # From permit data
        "market_tier": "primary" if parcel.city in ["Dallas", "Houston", "Austin"] else "secondary",
        "economic_growth": "positive"  # From economic indicators
    }


def _analyze_risks(parcel: Parcel) -> Dict[str, Any]:
    """Analyze potential risks"""
    
    return {
        "flood_risk": "low",  # Would come from FEMA flood maps
        "environmental_risk": "low",  # From EPA databases
        "seismic_risk": "low",  # From USGS data
        "regulatory_risk": "medium",  # Based on local zoning policies
        "market_risk": "low",  # Based on market stability
        "overall_risk_level": "low"
    }


def _compute_location_score(overlays: Dict[str, Any]) -> float:
    """Compute overall location score (0-100)"""
    
    score = 50  # Base score
    
    # Metro accessibility
    if overlays.get("metro_accessibility") == "high":
        score += 20
    elif overlays.get("metro_accessibility") == "medium":
        score += 10
    
    # Industrial clustering
    if overlays.get("industrial_cluster"):
        score += 15
    
    # Highway access
    highway_distance = overlays.get("distance_to_highway", 10)
    highway_score = max(0, 20 - (highway_distance * 2))
    score += highway_score
    
    # Market tier
    if overlays.get("market_tier") == "primary":
        score += 15
    
    return min(100, score)


def _compute_infrastructure_score(overlays: Dict[str, Any]) -> float:
    """Compute infrastructure readiness score (0-100)"""
    
    score = 0
    
    # Utility availability (20 points each)
    utilities = ["water_available", "sewer_available", "electric_available", "gas_available"]
    for utility in utilities:
        if overlays.get(utility):
            score += 20
    
    # Broadband availability
    if overlays.get("broadband_available"):
        score += 20
    
    return score


def _compute_overall_score(overlays: Dict[str, Any]) -> float:
    """Compute overall strategic value score"""
    
    location_score = overlays.get("location_score", 50)
    infrastructure_score = overlays.get("infrastructure_score", 50)
    logistics_score = overlays.get("logistics_score", 50)
    
    # Weighted average
    overall = (location_score * 0.4 + infrastructure_score * 0.3 + logistics_score * 0.3)
    
    # Risk adjustment
    risk_level = overlays.get("overall_risk_level", "medium")
    if risk_level == "low":
        overall *= 1.0
    elif risk_level == "medium":
        overall *= 0.9
    else:  # high risk
        overall *= 0.8
    
    return min(100, overall)


def _calculate_distance(lat1: float, lng1: float, lat2: float, lng2: float) -> float:
    """Calculate distance between two coordinates in miles"""
    
    # Haversine formula
    R = 3959  # Earth's radius in miles
    
    dlat = math.radians(lat2 - lat1)
    dlng = math.radians(lng2 - lng1)
    
    a = (math.sin(dlat/2) * math.sin(dlat/2) + 
         math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) * 
         math.sin(dlng/2) * math.sin(dlng/2))
    
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    
    return R * c


def _estimate_highway_distance(parcel: Parcel) -> float:
    """Estimate distance to nearest highway"""
    # Mock estimation based on location
    if parcel.city in ["Dallas", "Houston", "Austin"]:
        return 2.5  # Urban areas typically closer to highways
    else:
        return 8.0  # Rural areas farther from highways


def _estimate_port_distance(parcel: Parcel) -> float:
    """Estimate distance to nearest port"""
    # Texas-specific port estimates
    if parcel.state == "TX":
        if parcel.city == "Houston":
            return 25
        elif parcel.city == "Dallas":
            return 250  # Dallas to Houston port
        else:
            return 200  # Average for other TX cities
    else:
        return 300  # Other states


def _estimate_airport_distance(parcel: Parcel) -> float:
    """Estimate distance to nearest major airport"""
    # Major metros have closer airport access
    if parcel.city in ["Dallas", "Houston", "Austin"]:
        return 15
    else:
        return 45


def _estimate_rail_distance(parcel: Parcel) -> float:
    """Estimate distance to rail infrastructure"""
    # Industrial areas typically have rail access
    if "industrial" in parcel.land_use.lower():
        return 3.0
    else:
        return 8.0


def _calculate_logistics_score(highway_dist: float, port_dist: float, rail_dist: float) -> float:
    """Calculate logistics accessibility score"""
    
    # Highway score (40% weight)
    highway_score = max(0, 100 - (highway_dist * 10))
    
    # Port score (35% weight)  
    port_score = max(0, 100 - (port_dist * 0.5))
    
    # Rail score (25% weight)
    rail_score = max(0, 100 - (rail_dist * 8))
    
    return highway_score * 0.4 + port_score * 0.35 + rail_score * 0.25 