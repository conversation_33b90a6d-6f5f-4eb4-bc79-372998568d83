
#!/bin/bash

echo "🚀 启动工业地理开发平台"
echo "========================================"

# 检查并启动后端服务器
echo "🔧 启动后端服务器..."
cd backend-server
node server.js &
BACKEND_PID=$!
echo "后端服务器 PID: $BACKEND_PID"
cd ..

# 等待后端启动
sleep 3

# 检查后端是否启动成功
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "✅ 后端服务器启动成功"
    echo "   API地址: http://localhost:3001"
else
    echo "❌ 后端服务器启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端服务器
echo "🎨 启动前端服务器..."
NPM_CONFIG_LOGLEVEL=error npm run dev &
FRONTEND_PID=$!
echo "前端服务器 PID: $FRONTEND_PID"

# 等待前端启动
sleep 5

echo ""
echo "🎉 启动完成!"
echo "========================================"
echo "📱 前端应用: http://localhost:5173"
echo "🔧 后端API:  http://localhost:3001"
echo "📊 健康检查: http://localhost:3001/api/health"
echo ""
echo "💡 请在浏览器中打开: http://localhost:5173"
echo ""
echo "按 Ctrl+C 停止所有服务器"

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务器..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# 保持脚本运行
wait 