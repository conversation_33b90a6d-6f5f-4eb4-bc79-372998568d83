# 后端服务器架构说明

## 目录结构

```
backend-server/
├── server.js                    # 主服务器入口文件
├── package.json                 # Node.js依赖配置
├── config/                      # 配置文件
│   └── database.js             # 数据库配置
├── middleware/                  # 中间件
│   └── auth.js                 # 认证中间件
├── routes/                      # API路由
│   ├── auth.js                 # 认证相关路由
│   ├── cpi.js                  # CPI数据路由
│   ├── projects.js             # 项目管理路由
│   ├── qcew.js                 # QCEW数据路由
│   ├── rag.js                  # RAG AI助手路由
│   └── site-selection.js       # 站点选择路由
├── services/                    # 业务服务层
│   ├── ragService.js           # RAG AI助手服务
│   └── webSearchService.js     # 网络搜索服务
├── regard_api/                  # Python API模块
│   ├── __init__.py
│   ├── data_models.py          # 数据模型
│   ├── mock_data.py            # 模拟数据
│   └── regrid_client.py        # Regrid API客户端
├── site_selection_algorithm/    # 站点选择算法模块
│   ├── __init__.py
│   ├── api_wrapper.py          # API包装器
│   ├── core_algorithm.py       # 核心算法
│   ├── cost_calculator.py      # 成本计算器
│   ├── scoring_system.py       # 评分系统
│   └── utils.py                # 工具函数
└── us_labor/                    # 美国劳动力数据模块
    ├── index.js                # 模块入口
    ├── data/                   # 数据文件
    ├── routes/                 # 路由
    └── services/               # 服务
```

## 技术栈

### Node.js 服务
- **Express.js** - Web框架
- **MySQL2** - 数据库连接
- **JWT** - 身份认证
- **CORS** - 跨域支持
- **ChromaDB** - 向量数据库（用于RAG）

### Python 模块
- **Regrid API** - 地理数据服务
- **站点选择算法** - 工业选址算法
- **数据处理** - 劳动力和经济数据处理

## API 端点

### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/profile` - 获取用户信息

### 项目管理
- `GET /api/projects` - 获取项目列表
- `POST /api/projects` - 创建新项目
- `PUT /api/projects/:id` - 更新项目
- `DELETE /api/projects/:id` - 删除项目

### 数据服务
- `GET /api/cpi` - 获取CPI数据
- `GET /api/qcew` - 获取QCEW劳动力数据
- `POST /api/site-selection` - 站点选择分析

### AI助手
- `POST /api/rag/query` - RAG查询
- `GET /api/rag/documents` - 获取文档列表
- `POST /api/rag/documents` - 上传文档

## 部署说明

### 开发环境
```bash
cd backend-server
npm install
npm start
```

### 生产环境
- 配置环境变量（数据库连接、API密钥等）
- 使用PM2或类似工具进行进程管理
- 配置反向代理（Nginx）
- 设置SSL证书

## 环境变量

```env
# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=industrial_geo_dev

# JWT配置
JWT_SECRET=your_jwt_secret

# API密钥
REGRID_API_KEY=your_regrid_key
OPENAI_API_KEY=your_openai_key

# 服务配置
PORT=3001
NODE_ENV=development
```

## 日志和监控

- 服务器日志：`server.log`
- 错误处理：统一错误处理中间件
- 性能监控：建议集成APM工具

## 安全考虑

- JWT令牌认证
- 输入验证和清理
- CORS配置
- 环境变量保护敏感信息
- SQL注入防护（使用参数化查询）
