const express = require('express');
const { body, validationResult } = require('express-validator');
const router = express.Router();

// 中间件
const { optionalAuth } = require('../middleware/auth');

/**
 * 🤖 智能问答端点
 * POST /api/rag/ask
 */
router.post('/ask', [
  optionalAuth,
  body('question').notEmpty().withMessage('问题不能为空'),
  body('sessionId').optional().isString(),
  body('userId').optional().isString()
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { question, sessionId, enableWebSearch = false } = req.body;
    const userId = req.user?.id || null;
    const { ragService } = req; // 从请求中获取共享的 ragService 实例

    console.log(`🤖 RAG 问答请求 - 用户: ${userId || 'anonymous'}, 会话: ${sessionId || 'new'}, 问题: ${question}, 网络搜索: ${enableWebSearch ? '启用' : '禁用'}`);

    // 调用 RAG 服务进行问答（支持Context Engine和网络搜索开关）
    const result = await ragService.askQuestion(question, userId, sessionId, { enableWebSearch });

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ RAG 问答失败:', error);
    res.status(500).json({
      success: false,
      message: '智能问答服务暂时不可用',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 🌊 流式智能问答端点
 * POST /api/rag/ask-stream
 */
router.post('/ask-stream', [
  optionalAuth,
  body('question').notEmpty().withMessage('问题不能为空'),
  body('sessionId').optional().isString(),
  body('userId').optional().isString(),
  body('enableWebSearch').optional().isBoolean()
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { question, sessionId, enableWebSearch = false } = req.body;
    const userId = req.user?.id || null;
    const { ragService } = req;

    console.log(`🌊 RAG 流式问答请求 - 用户: ${userId || 'anonymous'}, 会话: ${sessionId || 'new'}, 问题: ${question}, 网络搜索: ${enableWebSearch ? '启用' : '禁用'}`);

    // 设置SSE响应头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // 发送初始连接确认
    res.write(`data: ${JSON.stringify({ type: 'connected', message: '连接已建立' })}\n\n`);

    try {
      // 调用RAG服务进行问答
      const result = await ragService.askQuestion(question, userId, sessionId, { enableWebSearch });

      // 发送内容数据
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: result.answer,
        isComplete: true
      })}\n\n`);

      // 发送来源数据
      if (result.sources && result.sources.length > 0) {
        res.write(`data: ${JSON.stringify({
          type: 'sources',
          data: result.sources
        })}\n\n`);
      }

      // 发送完成信号
      res.write(`data: ${JSON.stringify({
        type: 'complete',
        sessionId: result.sessionId,
        searchUsed: result.searchUsed
      })}\n\n`);

    } catch (error) {
      console.error('❌ RAG 流式问答失败:', error);
      res.write(`data: ${JSON.stringify({
        type: 'error',
        message: '智能问答服务暂时不可用',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })}\n\n`);
    }

    res.end();

  } catch (error) {
    console.error('❌ 流式请求处理失败:', error);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        message: '流式请求处理失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
});

/**
 * 🔄 用户反馈端点
 * POST /api/rag/feedback
 */
router.post('/feedback', [
  optionalAuth,
  body('sessionId').notEmpty().withMessage('会话ID不能为空'),
  body('questionId').notEmpty().withMessage('问题ID不能为空'),
  body('rating').isInt({ min: 1, max: 5 }).withMessage('评分必须是1-5的整数'),
  body('feedback').optional().isString()
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { sessionId, questionId, rating, feedback, responseTime, accuracy, issues, improvementSuggestions } = req.body;
    const userId = req.user?.id || null;
    const { ragService } = req;

    console.log(`📝 用户反馈 - 会话: ${sessionId}, 问题: ${questionId}, 评分: ${rating}`);

    // 构建反馈对象
    const feedbackData = {
      rating,
      feedback,
      responseTime,
      accuracy,
      issues: issues || [],
      improvementSuggestions: improvementSuggestions || [],
      timestamp: new Date().toISOString(),
      userId
    };

    // 处理反馈
    await ragService.contextEngine.processFeedback(sessionId, questionId, feedbackData);

    res.json({
      success: true,
      message: '反馈已记录，感谢您的宝贵意见！',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 处理用户反馈失败:', error);
    res.status(500).json({
      success: false,
      message: '处理反馈时出现错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 📊 学习统计端点
 * GET /api/rag/learning-stats
 */
router.get('/learning-stats', optionalAuth, async (req, res) => {
  try {
    const { ragService } = req;
    const stats = ragService.contextEngine.getLearningStats();

    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 获取学习统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学习统计失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 🔧 构建知识库端点
 * POST /api/rag/build-knowledge-base
 */
router.post('/build-knowledge-base', [
  optionalAuth,
  body('force').optional().isBoolean()
], async (req, res) => {
  try {
    const { force = false } = req.body;
    const { ragService } = req;

    console.log('🔨 开始构建知识库...');

    if (ragService.knowledgeBaseBuilt && !force) {
      return res.json({
        success: true,
        message: '知识库已存在，使用 force=true 强制重建',
        status: ragService.getStatus()
      });
    }

    // 构建知识库
    await ragService.buildKnowledgeBase();

    res.json({
      success: true,
      message: '知识库构建完成',
      status: ragService.getStatus(),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 构建知识库失败:', error);
    res.status(500).json({
      success: false,
      message: '构建知识库失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 📊 系统状态端点
 * GET /api/rag/status
 */
router.get('/status', optionalAuth, async (req, res) => {
  try {
    const { ragService } = req;
    
    // 获取基础状态
    const status = ragService.getStatus();
    
    // 获取文档数量
    const documentCount = ragService.vectorStore ? ragService.vectorStore.getCount() : 0;

    res.json({
      success: true,
      status: {
        ...status,
        documentCount,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: {
          hasGeminiKey: !!process.env.GEMINI_API_KEY,
          chromaDbPath: process.env.CHROMA_DB_PATH || './chroma_db',
          hasGoogleSearchKey: !!process.env.GOOGLE_SEARCH_API_KEY
        },
        webSearch: ragService.webSearchService.getStatus(),
        contextEngine: ragService.contextEngine.getStatus()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 获取系统状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取系统状态失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 🧹 清理会话端点
 * POST /api/rag/cleanup-sessions
 */
router.post('/cleanup-sessions', optionalAuth, async (req, res) => {
  try {
    const { ragService } = req;
    
    // 清理过期会话
    ragService.contextEngine.cleanupExpiredSessions();

    res.json({
      success: true,
      message: '会话清理完成',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 清理会话失败:', error);
    res.status(500).json({
      success: false,
      message: '清理会话失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 🔍 搜索测试端点
 * POST /api/rag/test-search
 */
router.post('/test-search', [
  optionalAuth,
  body('query').notEmpty().withMessage('搜索查询不能为空'),
  body('numResults').optional().isInt({ min: 1, max: 10 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { query, numResults = 5 } = req.body;
    const { ragService } = req;

    console.log(`🔍 测试搜索: "${query}"`);

    // 执行搜索
    const searchResults = await ragService.webSearchService.search(query, numResults);

    res.json({
      success: true,
      data: searchResults,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 搜索测试失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索测试失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 📚 知识检索测试端点
 * POST /api/rag/test-retrieval
 */
router.post('/test-retrieval', [
  optionalAuth,
  body('question').notEmpty().withMessage('问题不能为空'),
  body('maxResults').optional().isInt({ min: 1, max: 20 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { question, maxResults = 5 } = req.body;
    const { ragService } = req;

    console.log(`📚 测试知识检索: "${question}"`);

    // 检索相关知识片段
    const relevantChunks = await ragService.retrieveRelevantChunks(question);

    res.json({
      success: true,
      data: {
        question,
        chunks: relevantChunks.slice(0, maxResults),
        totalFound: relevantChunks.length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 知识检索测试失败:', error);
    res.status(500).json({
      success: false,
      message: '知识检索测试失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
