const express = require('express');

// 子路由
const lausRoutes = require('./routes/laus');
const qcewRoutes = require('./routes/qcew');

const router = express.Router();

// 装载子路由
router.use('/laus', lausRoutes);
router.use('/qcew', qcewRoutes);

// 健康检查
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    message: 'US Labor routes working',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
