/**
 * Learning Engine - 学习引擎
 * 实现自适应学习，持续优化AI助手的表现
 */

class LearningEngine {
  constructor() {
    this.feedbackHistory = new Map(); // 用户反馈历史
    this.performanceMetrics = new Map(); // 性能指标
    this.adaptationRules = this.initializeAdaptationRules();
    this.learningThresholds = {
      minFeedbackCount: 5,
      confidenceThreshold: 0.7,
      improvementThreshold: 0.1
    };
  }

  /**
   * 记录用户反馈
   * @param {string} sessionId - 会话ID
   * @param {string} questionId - 问题ID
   * @param {Object} feedback - 用户反馈
   */
  recordFeedback(sessionId, questionId, feedback) {
    const feedbackKey = `${sessionId}_${questionId}`;
    
    const feedbackRecord = {
      sessionId,
      questionId,
      feedback,
      timestamp: new Date(),
      processed: false
    };
    
    this.feedbackHistory.set(feedbackKey, feedbackRecord);
    
    // 触发学习更新
    this.triggerLearningUpdate(sessionId, feedback);
    
    console.log(`📝 记录用户反馈: ${feedbackKey}, 评分: ${feedback.rating}`);
  }

  /**
   * 触发学习更新
   */
  async triggerLearningUpdate(sessionId, feedback) {
    try {
      // 1. 更新性能指标
      this.updatePerformanceMetrics(sessionId, feedback);
      
      // 2. 分析反馈模式
      const patterns = this.analyzeFeedbackPatterns(sessionId);
      
      // 3. 生成适应性调整
      const adaptations = this.generateAdaptations(patterns);
      
      // 4. 应用学习结果
      await this.applyLearning(sessionId, adaptations);
      
    } catch (error) {
      console.error('❌ 学习更新失败:', error);
    }
  }

  /**
   * 更新性能指标
   */
  updatePerformanceMetrics(sessionId, feedback) {
    if (!this.performanceMetrics.has(sessionId)) {
      this.performanceMetrics.set(sessionId, {
        totalFeedback: 0,
        averageRating: 0,
        satisfactionTrend: [],
        responseTimeMetrics: [],
        accuracyMetrics: [],
        lastUpdated: new Date()
      });
    }
    
    const metrics = this.performanceMetrics.get(sessionId);
    
    // 更新基础指标
    metrics.totalFeedback++;
    metrics.averageRating = this.calculateRunningAverage(
      metrics.averageRating,
      feedback.rating,
      metrics.totalFeedback
    );
    
    // 更新趋势数据
    metrics.satisfactionTrend.push({
      rating: feedback.rating,
      timestamp: new Date()
    });
    
    // 保持趋势数据在合理范围内
    if (metrics.satisfactionTrend.length > 20) {
      metrics.satisfactionTrend = metrics.satisfactionTrend.slice(-15);
    }
    
    // 更新特定指标
    if (feedback.responseTime) {
      metrics.responseTimeMetrics.push(feedback.responseTime);
    }
    
    if (feedback.accuracy !== undefined) {
      metrics.accuracyMetrics.push(feedback.accuracy);
    }
    
    metrics.lastUpdated = new Date();
    
    console.log(`📊 更新性能指标: ${sessionId}, 平均评分: ${metrics.averageRating.toFixed(2)}`);
  }

  /**
   * 分析反馈模式
   */
  analyzeFeedbackPatterns(sessionId) {
    const sessionFeedback = Array.from(this.feedbackHistory.values())
      .filter(record => record.sessionId === sessionId)
      .sort((a, b) => a.timestamp - b.timestamp);
    
    if (sessionFeedback.length < this.learningThresholds.minFeedbackCount) {
      return { insufficient_data: true };
    }
    
    const patterns = {
      ratingTrend: this.analyzeRatingTrend(sessionFeedback),
      commonIssues: this.identifyCommonIssues(sessionFeedback),
      responseTimePattern: this.analyzeResponseTimePattern(sessionFeedback),
      satisfactionFactors: this.identifySatisfactionFactors(sessionFeedback),
      improvementAreas: this.identifyImprovementAreas(sessionFeedback)
    };
    
    return patterns;
  }

  /**
   * 分析评分趋势
   */
  analyzeRatingTrend(feedbackList) {
    const ratings = feedbackList.map(f => f.feedback.rating);
    
    if (ratings.length < 3) return 'insufficient_data';
    
    const recentRatings = ratings.slice(-5);
    const earlierRatings = ratings.slice(0, -5);
    
    const recentAvg = recentRatings.reduce((a, b) => a + b, 0) / recentRatings.length;
    const earlierAvg = earlierRatings.length > 0 
      ? earlierRatings.reduce((a, b) => a + b, 0) / earlierRatings.length 
      : recentAvg;
    
    const improvement = recentAvg - earlierAvg;
    
    if (improvement > this.learningThresholds.improvementThreshold) {
      return 'improving';
    } else if (improvement < -this.learningThresholds.improvementThreshold) {
      return 'declining';
    } else {
      return 'stable';
    }
  }

  /**
   * 识别常见问题
   */
  identifyCommonIssues(feedbackList) {
    const issues = new Map();
    
    feedbackList.forEach(record => {
      if (record.feedback.issues) {
        record.feedback.issues.forEach(issue => {
          const count = issues.get(issue) || 0;
          issues.set(issue, count + 1);
        });
      }
    });
    
    // 返回出现频率最高的问题
    return Array.from(issues.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([issue, count]) => ({ issue, frequency: count / feedbackList.length }));
  }

  /**
   * 分析响应时间模式
   */
  analyzeResponseTimePattern(feedbackList) {
    const responseTimes = feedbackList
      .map(f => f.feedback.responseTime)
      .filter(time => time !== undefined);
    
    if (responseTimes.length === 0) return null;
    
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const maxResponseTime = Math.max(...responseTimes);
    
    return {
      average: avgResponseTime,
      maximum: maxResponseTime,
      trend: this.calculateTrend(responseTimes)
    };
  }

  /**
   * 识别满意度因素
   */
  identifySatisfactionFactors(feedbackList) {
    const factors = {
      accuracy: [],
      speed: [],
      helpfulness: [],
      clarity: []
    };
    
    feedbackList.forEach(record => {
      const feedback = record.feedback;
      if (feedback.factors) {
        Object.keys(factors).forEach(factor => {
          if (feedback.factors[factor] !== undefined) {
            factors[factor].push(feedback.factors[factor]);
          }
        });
      }
    });
    
    // 计算各因素的平均分和相关性
    const result = {};
    Object.keys(factors).forEach(factor => {
      if (factors[factor].length > 0) {
        result[factor] = {
          average: factors[factor].reduce((a, b) => a + b, 0) / factors[factor].length,
          correlation: this.calculateCorrelation(factors[factor], feedbackList.map(f => f.feedback.rating))
        };
      }
    });
    
    return result;
  }

  /**
   * 识别改进领域
   */
  identifyImprovementAreas(feedbackList) {
    const lowRatingFeedback = feedbackList.filter(f => f.feedback.rating < 3);
    
    if (lowRatingFeedback.length === 0) return [];
    
    const improvementAreas = new Map();
    
    lowRatingFeedback.forEach(record => {
      if (record.feedback.improvementSuggestions) {
        record.feedback.improvementSuggestions.forEach(suggestion => {
          const count = improvementAreas.get(suggestion) || 0;
          improvementAreas.set(suggestion, count + 1);
        });
      }
    });
    
    return Array.from(improvementAreas.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([area, count]) => ({ area, priority: count / lowRatingFeedback.length }));
  }

  /**
   * 生成适应性调整
   */
  generateAdaptations(patterns) {
    const adaptations = [];
    
    if (patterns.insufficient_data) {
      return adaptations;
    }
    
    // 基于评分趋势的调整
    if (patterns.ratingTrend === 'declining') {
      adaptations.push({
        type: 'response_style',
        adjustment: 'increase_detail_level',
        reason: 'declining_satisfaction',
        priority: 'high'
      });
    }
    
    // 基于常见问题的调整
    patterns.commonIssues.forEach(issue => {
      if (issue.frequency > 0.3) { // 30%以上的反馈提到此问题
        adaptations.push({
          type: 'issue_resolution',
          adjustment: this.getIssueResolution(issue.issue),
          reason: `frequent_issue: ${issue.issue}`,
          priority: 'medium'
        });
      }
    });
    
    // 基于响应时间的调整
    if (patterns.responseTimePattern && patterns.responseTimePattern.average > 5000) {
      adaptations.push({
        type: 'performance',
        adjustment: 'optimize_response_time',
        reason: 'slow_response_time',
        priority: 'high'
      });
    }
    
    return adaptations;
  }

  /**
   * 应用学习结果
   */
  async applyLearning(sessionId, adaptations) {
    if (adaptations.length === 0) return;
    
    console.log(`🧠 应用学习结果: ${sessionId}, ${adaptations.length} 个调整`);
    
    // 按优先级排序
    adaptations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
    
    // 应用调整
    for (const adaptation of adaptations) {
      await this.applyAdaptation(sessionId, adaptation);
    }
  }

  /**
   * 应用单个适应性调整
   */
  async applyAdaptation(sessionId, adaptation) {
    try {
      console.log(`✅ 应用适应性调整: ${adaptation.type} - ${adaptation.adjustment}`);
    } catch (error) {
      console.error(`❌ 应用适应性调整失败: ${adaptation.type}`, error);
    }
  }

  /**
   * 初始化适应规则
   */
  initializeAdaptationRules() {
    return {
      response_style: {
        increase_detail_level: 'Provide more detailed explanations',
        decrease_detail_level: 'Provide more concise responses',
        use_more_examples: 'Include more practical examples',
        simplify_language: 'Use simpler, more accessible language'
      },
      issue_resolution: {
        unclear_instructions: 'Break down instructions into smaller steps',
        missing_information: 'Proactively provide related information',
        technical_complexity: 'Explain technical concepts more clearly'
      }
    };
  }

  /**
   * 获取问题解决方案
   */
  getIssueResolution(issue) {
    const resolutionMap = {
      'unclear_instructions': 'provide_step_by_step_guide',
      'missing_information': 'include_comprehensive_details',
      'too_technical': 'simplify_explanations',
      'not_helpful': 'focus_on_practical_solutions',
      'confusing': 'restructure_response_format'
    };
    
    return resolutionMap[issue] || 'general_improvement';
  }

  /**
   * 计算运行平均值
   */
  calculateRunningAverage(currentAvg, newValue, count) {
    return ((currentAvg * (count - 1)) + newValue) / count;
  }

  /**
   * 计算趋势
   */
  calculateTrend(values) {
    if (values.length < 2) return 'stable';
    
    const recent = values.slice(-Math.min(5, values.length));
    const earlier = values.slice(0, -Math.min(5, values.length));
    
    if (earlier.length === 0) return 'stable';
    
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const earlierAvg = earlier.reduce((a, b) => a + b, 0) / earlier.length;
    
    const change = (recentAvg - earlierAvg) / earlierAvg;
    
    if (change > 0.1) return 'improving';
    if (change < -0.1) return 'declining';
    return 'stable';
  }

  /**
   * 计算相关性
   */
  calculateCorrelation(x, y) {
    if (x.length !== y.length || x.length === 0) return 0;
    
    const n = x.length;
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0);
    const sumY2 = y.reduce((sum, yi) => sum + yi * yi, 0);
    
    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
    
    return denominator === 0 ? 0 : numerator / denominator;
  }

  /**
   * 获取学习统计
   */
  getLearningStats() {
    const totalSessions = this.performanceMetrics.size;
    const totalFeedback = Array.from(this.performanceMetrics.values())
      .reduce((sum, metrics) => sum + metrics.totalFeedback, 0);
    
    const averageRating = totalSessions > 0 
      ? Array.from(this.performanceMetrics.values())
          .reduce((sum, metrics) => sum + metrics.averageRating, 0) / totalSessions
      : 0;
    
    return {
      totalSessions,
      totalFeedback,
      averageRating: averageRating.toFixed(2),
      activeLearning: this.feedbackHistory.size > 0
    };
  }
}

module.exports = LearningEngine;
