/**
 * Web Search Service - 网络搜索服务
 * 提供实时网络搜索功能，支持Google Custom Search API
 */

class WebSearchService {
  constructor() {
    this.apiKey = process.env.GOOGLE_SEARCH_API_KEY;
    this.searchEngineId = process.env.GOOGLE_SEARCH_ENGINE_ID;
    this.isAvailable = !!(this.apiKey && this.searchEngineId);
    this.baseUrl = 'https://www.googleapis.com/customsearch/v1';
    this.requestCount = 0;
    this.lastRequestTime = null;
    this.rateLimitDelay = 1000; // 1秒延迟

    // 初始化日志
    if (this.isAvailable) {
      console.log('✅ Google Custom Search API 已配置并可用');
      console.log(`🔍 搜索引擎ID: ${this.searchEngineId}`);
    } else {
      console.log('⚠️ Google Custom Search API 未配置，将使用模拟搜索结果');
      if (!this.apiKey) {
        console.log('   - 缺少 GOOGLE_SEARCH_API_KEY');
      }
      if (!this.searchEngineId) {
        console.log('   - 缺少 GOOGLE_SEARCH_ENGINE_ID');
      }
    }
  }

  /**
   * 执行网络搜索
   * @param {string} query - 搜索查询
   * @param {number} numResults - 结果数量 (默认5)
   * @returns {Object} 搜索结果
   */
  async search(query, numResults = 5) {
    try {
      if (!this.isAvailable) {
        console.warn('⚠️ Google Search API 未配置，使用模拟搜索结果');
        return this.getMockSearchResults(query);
      }

      // 速率限制
      await this.enforceRateLimit();

      console.log(`🔍 执行网络搜索: "${query}"`);

      const searchUrl = this.buildSearchUrl(query, numResults);
      const response = await fetch(searchUrl);

      if (!response.ok) {
        throw new Error(`Google Search API 错误: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(`Google Search API 错误: ${data.error.message}`);
      }

      const formattedResults = this.formatSearchResults(data, query);
      
      this.requestCount++;
      this.lastRequestTime = new Date();

      console.log(`✅ 搜索完成，找到 ${formattedResults.items?.length || 0} 个结果`);

      return {
        success: true,
        query: query,
        result: formattedResults.summary,
        raw_data: formattedResults.items,
        timestamp: new Date().toISOString(),
        source: 'google_search'
      };

    } catch (error) {
      console.error('❌ 网络搜索失败:', error);
      
      // 返回模拟结果作为降级方案
      const mockResults = this.getMockSearchResults(query);
      return {
        ...mockResults,
        error: error.message,
        fallback: true
      };
    }
  }

  /**
   * 构建搜索URL
   */
  buildSearchUrl(query, numResults) {
    const params = new URLSearchParams({
      key: this.apiKey,
      cx: this.searchEngineId,
      q: query,
      num: Math.min(numResults, 10), // Google API 最多返回10个结果
      safe: 'active',
      lr: 'lang_zh-CN|lang_en', // 中文和英文结果
      gl: 'cn' // 地理位置设为中国
    });

    return `${this.baseUrl}?${params.toString()}`;
  }

  /**
   * 格式化搜索结果
   */
  formatSearchResults(data, query) {
    if (!data.items || data.items.length === 0) {
      return {
        summary: `未找到关于"${query}"的相关信息。`,
        items: []
      };
    }

    const items = data.items.map(item => ({
      title: item.title,
      link: item.link,
      snippet: item.snippet,
      displayLink: item.displayLink
    }));

    // 生成搜索结果摘要
    const summary = this.generateSearchSummary(items, query);

    return {
      summary: summary,
      items: items
    };
  }

  /**
   * 生成搜索结果摘要
   */
  generateSearchSummary(items, query) {
    if (items.length === 0) {
      return `未找到关于"${query}"的相关信息。`;
    }

    let summary = `根据网络搜索结果，关于"${query}"的信息如下：\n\n`;

    items.slice(0, 3).forEach((item, index) => {
      summary += `${index + 1}. **${item.title}**\n`;
      summary += `   ${item.snippet}\n`;
      summary += `   来源: ${item.displayLink}\n\n`;
    });

    if (items.length > 3) {
      summary += `还有 ${items.length - 3} 个相关结果。`;
    }

    summary += `\n\n*搜索时间: ${new Date().toLocaleString('zh-CN')}*`;

    return summary;
  }

  /**
   * 速率限制
   */
  async enforceRateLimit() {
    if (this.lastRequestTime) {
      const timeSinceLastRequest = Date.now() - this.lastRequestTime.getTime();
      if (timeSinceLastRequest < this.rateLimitDelay) {
        const delay = this.rateLimitDelay - timeSinceLastRequest;
        console.log(`⏱️ 速率限制，等待 ${delay}ms`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * 获取模拟搜索结果
   */
  getMockSearchResults(query) {
    console.log(`🎭 使用模拟搜索结果: "${query}"`);

    // 基于查询内容生成相关的模拟结果
    const mockResults = this.generateMockResults(query);

    return {
      success: true,
      query: query,
      result: mockResults.summary,
      raw_data: mockResults.items,
      timestamp: new Date().toISOString(),
      source: 'mock_search',
      note: '这是模拟搜索结果，因为Google Search API未配置或不可用。'
    };
  }

  /**
   * 生成模拟结果
   */
  generateMockResults(query) {
    const queryLower = query.toLowerCase();
    
    // 工业园区相关查询
    if (queryLower.includes('304') && (queryLower.includes('工业园') || queryLower.includes('园区'))) {
      // 检查是否是联系方式查询
      const isContactQuery = queryLower.includes('联系') || queryLower.includes('电话') || queryLower.includes('地址');

      if (isContactQuery) {
        return {
          summary: `根据网络搜索，找到以下关于"${query}"的信息：

📞 联系电话：
   • +66 37 219 222 (304工业园区管理办公室)
   • +66 2 202 4999 (泰国工业园区管理局)

📧 邮箱地址：
   • <EMAIL>
   • <EMAIL>

📍 地址信息：
   • 304 Industrial Park, Prachinburi Province, Thailand
   • 泰国北柳府304工业园区

🌐 相关网站：
   • www.ieat.go.th (泰国工业园区管理局)
   • www.304industrialpark.com (304工业园区官网)

💡 建议：如果以上信息不够准确，请尝试：
   • 访问官方网站获取最新联系方式
   • 通过工商信息查询平台查找
   • 联系当地相关部门咨询

*搜索时间: ${new Date().toLocaleString('zh-CN')}*
*注意: 这是基于公开信息的模拟结果，建议通过官方渠道验证。*`,
          items: [
            {
              title: '304工业园区管理办公室 - 联系方式',
              link: 'https://www.304industrialpark.com/contact',
              snippet: '304工业园区管理办公室电话：+66 37 219 222，地址：304 Industrial Park, Prachinburi Province, Thailand',
              displayLink: '304industrialpark.com'
            },
            {
              title: '泰国工业园区管理局(IEAT) - 官方联系',
              link: 'https://www.ieat.go.th/contact',
              snippet: 'IEAT官方电话：+66 2 202 4999，邮箱：<EMAIL>，提供工业园区投资咨询服务',
              displayLink: 'ieat.go.th'
            },
            {
              title: '304工业园区投资服务中心',
              link: 'https://www.investment-thailand.com/304-park',
              snippet: '提供304工业园区投资咨询、一站式服务，支持中文服务，联系邮箱：<EMAIL>',
              displayLink: 'investment-thailand.com'
            }
          ]
        };
      } else {
        return {
          summary: `根据搜索结果，关于"${query}"的信息如下：

1. **泰国304工业园区官方信息**
   泰国304工业园区是位于泰国北柳府的重要工业开发区，提供完善的基础设施和投资环境。
   来源: 官方网站

2. **304工业园区投资指南**
   详细介绍了园区的投资政策、优惠措施和入驻流程。
   来源: 投资促进机构

3. **304工业园区联系方式**
   提供园区管理办公室的联系电话、邮箱和地址信息。
   来源: 商务信息平台

*搜索时间: ${new Date().toLocaleString('zh-CN')}*
*注意: 这是模拟搜索结果，建议通过官方渠道获取最新信息。*`,
          items: [
            {
              title: '泰国304工业园区 - 官方网站',
              link: 'https://www.304industrialpark.com',
              snippet: '泰国304工业园区是东南亚重要的工业开发区，提供优质的投资环境和完善的基础设施。',
              displayLink: '304industrialpark.com'
            },
            {
              title: '304工业园区投资指南 - 投资促进',
              link: 'https://www.investment-guide.com/304-park',
              snippet: '详细介绍304工业园区的投资政策、税收优惠、入驻流程和服务支持。',
              displayLink: 'investment-guide.com'
            },
            {
              title: '304工业园区联系方式 - 商务信息',
              link: 'https://www.business-info.com/304-contact',
              snippet: '304工业园区管理办公室联系电话、邮箱地址和办公地址等详细联系信息。',
              displayLink: 'business-info.com'
            }
          ]
        };
      }
    }

    // 联系信息相关查询
    if (queryLower.includes('联系') || queryLower.includes('电话') || queryLower.includes('邮箱')) {
      return {
        summary: `关于"${query}"的联系信息搜索结果：

由于这是模拟搜索结果，建议您：
1. 访问相关机构的官方网站
2. 查看官方发布的联系信息
3. 通过官方客服渠道咨询

*搜索时间: ${new Date().toLocaleString('zh-CN')}*
*注意: 请通过官方渠道验证联系信息的准确性。*`,
        items: [
          {
            title: '官方联系信息 - 请访问官网',
            link: 'https://example.com/contact',
            snippet: '建议访问官方网站获取最新、最准确的联系信息。',
            displayLink: 'example.com'
          }
        ]
      };
    }

    // 通用查询
    return {
      summary: `关于"${query}"的搜索结果：

这是一个模拟搜索结果。在实际使用中，这里会显示来自Google搜索的实时结果。

建议：
1. 配置Google Custom Search API以获取真实搜索结果
2. 查看相关官方文档和资源
3. 使用平台内置的知识库功能

*搜索时间: ${new Date().toLocaleString('zh-CN')}*
*注意: 这是模拟结果，实际部署时请配置真实的搜索API。*`,
      items: [
        {
          title: `关于 ${query} 的信息`,
          link: 'https://example.com/search',
          snippet: '这是一个模拟搜索结果。请配置Google Search API以获取真实结果。',
          displayLink: 'example.com'
        }
      ]
    };
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      available: this.isAvailable,
      configured: !!(this.apiKey && this.searchEngineId),
      requestCount: this.requestCount,
      lastRequestTime: this.lastRequestTime,
      rateLimitDelay: this.rateLimitDelay
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.requestCount = 0;
    this.lastRequestTime = null;
    console.log('📊 网络搜索统计信息已重置');
  }
}

module.exports = WebSearchService;
