/**
 * Intent Classifier - 意图分类器
 * 深度分析用户意图，支持多层次分类和上下文理解
 */

class IntentClassifier {
  constructor() {
    this.intentRules = this.initializeIntentRules();
    this.contextPatterns = this.initializeContextPatterns();
  }

  /**
   * 分类用户意图
   * @param {string} question - 用户问题
   * @param {Array} conversationHistory - 对话历史
   * @returns {Object} 意图分析结果
   */
  async classify(question, conversationHistory = []) {
    try {
      // 1. 基础意图识别
      const baseIntent = this.classifyBaseIntent(question);
      
      // 2. 上下文增强
      const contextEnhanced = this.enhanceWithContext(baseIntent, conversationHistory);
      
      // 3. 细粒度分类
      const detailedIntent = this.classifyDetailed(question, contextEnhanced);
      
      // 4. 紧急程度评估
      const urgency = this.assessUrgency(question, detailedIntent);
      
      // 5. 复杂度评估
      const complexity = this.assessComplexity(question, detailedIntent);
      
      return {
        category: detailedIntent.category,
        subcategory: detailedIntent.subcategory,
        confidence: detailedIntent.confidence,
        urgency: urgency,
        complexity: complexity,
        requiresContext: this.requiresContext(detailedIntent),
        expectedResponseType: this.getExpectedResponseType(detailedIntent),
        suggestedActions: this.getSuggestedActions(detailedIntent),
        metadata: {
          keywords: this.extractKeywords(question),
          sentiment: this.analyzeSentiment(question),
          questionType: this.classifyQuestionType(question)
        }
      };
      
    } catch (error) {
      console.error('❌ 意图分类失败:', error);
      return this.createFallbackIntent(question);
    }
  }

  /**
   * 初始化意图规则
   */
  initializeIntentRules() {
    return {
      // 联系信息查询
      contact_info: {
        keywords: ['联系', '电话', '手机', '邮箱', '地址', '联系方式', '联系人', '官网', '网站'],
        patterns: [
          /联系.*?方式/,
          /电话.*?号码/,
          /如何.*?联系/,
          /.*?的.*?联系.*?信息/
        ],
        confidence: 0.9
      },
      
      // 平台功能使用
      platform_usage: {
        keywords: ['如何', '怎么', '使用', '操作', '功能', '步骤', '方法'],
        patterns: [
          /如何.*?使用/,
          /怎么.*?操作/,
          /.*?功能.*?怎么/,
          /.*?的.*?使用.*?方法/
        ],
        confidence: 0.8
      },
      
      // 地图相关
      map_related: {
        keywords: ['地图', '导航', '位置', '坐标', '缩放', '图层', '标记'],
        patterns: [
          /地图.*?功能/,
          /如何.*?地图/,
          /地图.*?使用/,
          /.*?在.*?地图.*?上/
        ],
        confidence: 0.85
      },
      
      // 数据分析
      data_analysis: {
        keywords: ['数据', '分析', '统计', '趋势', '报告', '图表', '可视化'],
        patterns: [
          /数据.*?分析/,
          /.*?趋势.*?分析/,
          /如何.*?分析.*?数据/,
          /.*?统计.*?信息/
        ],
        confidence: 0.8
      },
      
      // 劳动力分析
      labor_analysis: {
        keywords: ['劳动力', '就业', '失业', '工资', '薪资', '人力', '员工', 'QCEW', 'LAUS'],
        patterns: [
          /劳动力.*?趋势/,
          /就业.*?数据/,
          /工资.*?水平/,
          /.*?劳动力.*?分析/
        ],
        confidence: 0.9
      },
      
      // 工业选址
      site_selection: {
        keywords: ['选址', '工业园', '园区', '位置', '选择', '评估', '分析'],
        patterns: [
          /工业.*?选址/,
          /园区.*?选择/,
          /选址.*?分析/,
          /.*?园区.*?评估/
        ],
        confidence: 0.85
      },
      
      // 技术支持
      technical_support: {
        keywords: ['错误', '问题', '故障', '异常', '不能', '无法', '失败', 'bug'],
        patterns: [
          /.*?错误/,
          /.*?问题/,
          /不能.*?/,
          /无法.*?/,
          /.*?失败/
        ],
        confidence: 0.8
      },
      
      // 系统信息
      system_info: {
        keywords: ['系统', '平台', '功能', '介绍', '什么', '有哪些', '包括'],
        patterns: [
          /系统.*?功能/,
          /平台.*?介绍/,
          /有.*?哪些.*?功能/,
          /.*?包括.*?什么/
        ],
        confidence: 0.7
      }
    };
  }

  /**
   * 初始化上下文模式
   */
  initializeContextPatterns() {
    return {
      continuation: ['继续', '然后', '接下来', '还有', '另外'],
      clarification: ['具体', '详细', '更多', '进一步', '深入'],
      comparison: ['比较', '对比', '区别', '差异', '哪个更好'],
      followup: ['那么', '如果', '假如', '要是', '万一']
    };
  }

  /**
   * 基础意图分类
   */
  classifyBaseIntent(question) {
    const questionLower = question.toLowerCase();
    let bestMatch = { category: 'unknown', confidence: 0 };

    for (const [category, rule] of Object.entries(this.intentRules)) {
      let score = 0;
      
      // 关键词匹配
      const keywordMatches = rule.keywords.filter(keyword => 
        questionLower.includes(keyword.toLowerCase())
      ).length;
      score += (keywordMatches / rule.keywords.length) * 0.6;
      
      // 模式匹配
      const patternMatches = rule.patterns.filter(pattern => 
        pattern.test(questionLower)
      ).length;
      score += (patternMatches / rule.patterns.length) * 0.4;
      
      // 应用置信度权重
      score *= rule.confidence;
      
      if (score > bestMatch.confidence) {
        bestMatch = { category, confidence: score };
      }
    }

    return bestMatch;
  }

  /**
   * 上下文增强
   */
  enhanceWithContext(baseIntent, conversationHistory) {
    if (conversationHistory.length === 0) {
      return baseIntent;
    }

    const recentIntent = conversationHistory[conversationHistory.length - 1]?.intent;
    if (!recentIntent) return baseIntent;

    // 如果当前意图置信度较低，考虑继承上下文
    if (baseIntent.confidence < 0.5 && recentIntent.confidence > 0.7) {
      return {
        category: recentIntent.category,
        confidence: Math.min(recentIntent.confidence * 0.8, 0.9),
        contextInherited: true
      };
    }

    // 检查是否是相关话题的延续
    if (this.isRelatedCategory(baseIntent.category, recentIntent.category)) {
      baseIntent.confidence = Math.min(baseIntent.confidence * 1.2, 1.0);
      baseIntent.contextEnhanced = true;
    }

    return baseIntent;
  }

  /**
   * 细粒度分类
   */
  classifyDetailed(question, baseIntent) {
    const subcategories = this.getSubcategories(baseIntent.category, question);
    
    return {
      category: baseIntent.category,
      subcategory: subcategories.best,
      confidence: baseIntent.confidence,
      allSubcategories: subcategories.all
    };
  }

  /**
   * 获取子分类
   */
  getSubcategories(category, question) {
    const subcategoryMap = {
      platform_usage: {
        'map_usage': ['地图', '导航', '缩放'],
        'data_visualization': ['图表', '可视化', '展示'],
        'analysis_tools': ['分析', '工具', '计算'],
        'general_usage': ['使用', '操作', '功能']
      },
      contact_info: {
        'phone': ['电话', '手机', '号码'],
        'email': ['邮箱', '邮件', 'email'],
        'address': ['地址', '位置', '在哪'],
        'website': ['网站', '官网', '网址']
      },
      data_analysis: {
        'labor_data': ['劳动力', '就业', '工资'],
        'economic_data': ['经济', 'CPI', 'GDP'],
        'trend_analysis': ['趋势', '变化', '预测'],
        'statistical_analysis': ['统计', '计算', '分析']
      }
    };

    const subcats = subcategoryMap[category] || {};
    const questionLower = question.toLowerCase();
    
    let bestMatch = 'general';
    let bestScore = 0;
    const allMatches = [];

    for (const [subcat, keywords] of Object.entries(subcats)) {
      const matches = keywords.filter(keyword => 
        questionLower.includes(keyword)
      ).length;
      
      const score = matches / keywords.length;
      allMatches.push({ subcategory: subcat, score });
      
      if (score > bestScore) {
        bestScore = score;
        bestMatch = subcat;
      }
    }

    return {
      best: bestMatch,
      all: allMatches.sort((a, b) => b.score - a.score)
    };
  }

  /**
   * 评估紧急程度
   */
  assessUrgency(question, intent) {
    const urgentKeywords = ['紧急', '急', '马上', '立即', '现在', '快', '错误', '故障', '不能'];
    const questionLower = question.toLowerCase();
    
    const urgentMatches = urgentKeywords.filter(keyword => 
      questionLower.includes(keyword)
    ).length;

    if (intent.category === 'technical_support') return 'high';
    if (urgentMatches > 0) return 'medium';
    return 'low';
  }

  /**
   * 评估复杂度
   */
  assessComplexity(question, intent) {
    const complexKeywords = ['详细', '深入', '全面', '复杂', '高级', '专业'];
    const simpleKeywords = ['简单', '基础', '快速', '简要'];
    
    const questionLower = question.toLowerCase();
    const complexMatches = complexKeywords.filter(k => questionLower.includes(k)).length;
    const simpleMatches = simpleKeywords.filter(k => questionLower.includes(k)).length;
    
    if (complexMatches > simpleMatches) return 'high';
    if (simpleMatches > 0) return 'low';
    return 'medium';
  }

  /**
   * 判断是否需要上下文
   */
  requiresContext(intent) {
    const contextRequiredCategories = ['platform_usage', 'data_analysis', 'site_selection'];
    return contextRequiredCategories.includes(intent.category);
  }

  /**
   * 获取期望的响应类型
   */
  getExpectedResponseType(intent) {
    const responseTypeMap = {
      contact_info: 'direct_answer',
      platform_usage: 'step_by_step',
      data_analysis: 'detailed_explanation',
      technical_support: 'troubleshooting',
      system_info: 'overview'
    };
    
    return responseTypeMap[intent.category] || 'general';
  }

  /**
   * 获取建议操作
   */
  getSuggestedActions(intent) {
    const actionMap = {
      contact_info: ['web_search', 'direct_response'],
      platform_usage: ['knowledge_retrieval', 'step_guide'],
      data_analysis: ['knowledge_retrieval', 'example_provision'],
      technical_support: ['troubleshooting_guide', 'escalation'],
      system_info: ['feature_overview', 'documentation_link']
    };
    
    return actionMap[intent.category] || ['general_response'];
  }

  /**
   * 提取关键词
   */
  extractKeywords(question) {
    // 简化版关键词提取
    const stopWords = ['的', '是', '在', '有', '和', '与', '或', '但', '如何', '什么', '哪里', '为什么'];
    const words = question.split(/\s+|[，。！？；：]/).filter(word => 
      word.length > 1 && !stopWords.includes(word)
    );
    
    return words.slice(0, 10); // 返回前10个关键词
  }

  /**
   * 情感分析
   */
  analyzeSentiment(question) {
    const positiveWords = ['好', '棒', '优秀', '满意', '喜欢', '感谢'];
    const negativeWords = ['不好', '差', '问题', '错误', '失败', '不满'];
    
    const questionLower = question.toLowerCase();
    const positiveCount = positiveWords.filter(word => questionLower.includes(word)).length;
    const negativeCount = negativeWords.filter(word => questionLower.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * 问题类型分类
   */
  classifyQuestionType(question) {
    if (question.includes('如何') || question.includes('怎么')) return 'how_to';
    if (question.includes('什么') || question.includes('是什么')) return 'what_is';
    if (question.includes('为什么')) return 'why';
    if (question.includes('哪里') || question.includes('在哪')) return 'where';
    if (question.includes('什么时候')) return 'when';
    return 'general';
  }

  /**
   * 判断分类是否相关
   */
  isRelatedCategory(cat1, cat2) {
    const relatedGroups = [
      ['platform_usage', 'map_related', 'data_analysis'],
      ['contact_info', 'system_info'],
      ['labor_analysis', 'data_analysis', 'site_selection']
    ];
    
    return relatedGroups.some(group => 
      group.includes(cat1) && group.includes(cat2)
    );
  }

  /**
   * 创建降级意图
   */
  createFallbackIntent(question) {
    return {
      category: 'unknown',
      subcategory: 'general',
      confidence: 0.1,
      urgency: 'low',
      complexity: 'medium',
      requiresContext: false,
      expectedResponseType: 'general',
      suggestedActions: ['general_response'],
      metadata: {
        keywords: this.extractKeywords(question),
        sentiment: 'neutral',
        questionType: 'general'
      }
    };
  }
}

module.exports = IntentClassifier;
