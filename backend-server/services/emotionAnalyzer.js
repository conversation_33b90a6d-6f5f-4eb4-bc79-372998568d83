/**
 * Emotion Analyzer - 情感分析器
 * 分析用户情感状态，提供情感感知的对话体验
 */

class EmotionAnalyzer {
  constructor() {
    this.emotionPatterns = this.initializeEmotionPatterns();
    this.satisfactionIndicators = this.initializeSatisfactionIndicators();
  }

  /**
   * 分析用户情感
   * @param {string} text - 用户输入文本
   * @param {Array} conversationHistory - 对话历史
   * @returns {Object} 情感分析结果
   */
  analyzeEmotion(text, conversationHistory = []) {
    const textLower = text.toLowerCase();
    
    // 1. 基础情感识别
    const primaryEmotion = this.detectPrimaryEmotion(textLower);
    
    // 2. 情感强度分析
    const intensity = this.analyzeIntensity(textLower);
    
    // 3. 满意度评估
    const satisfaction = this.assessSatisfaction(textLower, conversationHistory);
    
    // 4. 挫折感检测
    const frustration = this.detectFrustration(textLower, conversationHistory);
    
    // 5. 紧急感检测
    const urgency = this.detectUrgency(textLower);
    
    return {
      primary: primaryEmotion,
      intensity: intensity,
      satisfaction: satisfaction,
      frustration: frustration,
      urgency: urgency,
      confidence: this.calculateConfidence(primaryEmotion, intensity),
      recommendations: this.getResponseRecommendations(primaryEmotion, frustration, urgency)
    };
  }

  /**
   * 初始化情感模式
   */
  initializeEmotionPatterns() {
    return {
      positive: {
        keywords: ['好', '棒', '优秀', '满意', '喜欢', '感谢', '谢谢', '不错', '很好', '完美'],
        patterns: [/很.*好/, /非常.*满意/, /太.*了/, /真.*棒/],
        weight: 1.0
      },
      negative: {
        keywords: ['不好', '差', '糟糕', '失望', '不满', '讨厌', '烦', '麻烦', '问题', '错误'],
        patterns: [/不.*好/, /很.*差/, /太.*了/, /什么.*破/],
        weight: 1.2
      },
      frustrated: {
        keywords: ['为什么', '怎么回事', '搞不懂', '不明白', '复杂', '困难', '头疼', '烦人'],
        patterns: [/为什么.*不/, /怎么.*这么/, /搞不.*懂/, /太.*复杂/],
        weight: 1.1
      },
      confused: {
        keywords: ['不懂', '不明白', '不清楚', '糊涂', '迷惑', '不知道', '搞不清'],
        patterns: [/不.*懂/, /不.*明白/, /不.*清楚/, /搞不.*清/],
        weight: 0.9
      },
      urgent: {
        keywords: ['急', '紧急', '马上', '立即', '赶紧', '快', '尽快', '现在'],
        patterns: [/马上.*/, /立即.*/, /紧急.*/, /很.*急/],
        weight: 1.3
      },
      neutral: {
        keywords: ['请问', '想了解', '咨询', '查询', '请教'],
        patterns: [/请问.*/, /想.*了解/, /如何.*/, /怎么.*/],
        weight: 0.8
      }
    };
  }

  /**
   * 初始化满意度指标
   */
  initializeSatisfactionIndicators() {
    return {
      high: ['完美', '太好了', '正是我要的', '非常满意', '解决了', '明白了', '清楚了'],
      medium: ['还可以', '基本满意', '差不多', '大概明白', '有帮助'],
      low: ['还是不懂', '没解决', '不太明白', '还有问题', '不够详细'],
      very_low: ['完全不懂', '没用', '浪费时间', '更糊涂了', '没帮助']
    };
  }

  /**
   * 检测主要情感
   */
  detectPrimaryEmotion(text) {
    let maxScore = 0;
    let primaryEmotion = 'neutral';
    
    for (const [emotion, pattern] of Object.entries(this.emotionPatterns)) {
      let score = 0;
      
      // 关键词匹配
      const keywordMatches = pattern.keywords.filter(keyword => 
        text.includes(keyword)
      ).length;
      score += (keywordMatches / pattern.keywords.length) * 0.6;
      
      // 模式匹配
      const patternMatches = pattern.patterns.filter(regex => 
        regex.test(text)
      ).length;
      score += (patternMatches / pattern.patterns.length) * 0.4;
      
      // 应用权重
      score *= pattern.weight;
      
      if (score > maxScore) {
        maxScore = score;
        primaryEmotion = emotion;
      }
    }
    
    return { emotion: primaryEmotion, score: maxScore };
  }

  /**
   * 分析情感强度
   */
  analyzeIntensity(text) {
    const intensifiers = ['非常', '很', '特别', '极其', '超级', '太', '相当', '十分'];
    const diminishers = ['有点', '稍微', '略微', '还算', '比较'];
    
    let intensity = 0.5; // 基础强度
    
    intensifiers.forEach(word => {
      if (text.includes(word)) intensity += 0.2;
    });
    
    diminishers.forEach(word => {
      if (text.includes(word)) intensity -= 0.1;
    });
    
    // 标点符号影响
    const exclamationCount = (text.match(/！|!/g) || []).length;
    const questionCount = (text.match(/？|\?/g) || []).length;
    
    intensity += exclamationCount * 0.1;
    intensity += questionCount * 0.05;
    
    return Math.max(0, Math.min(1, intensity));
  }

  /**
   * 评估满意度
   */
  assessSatisfaction(text, conversationHistory) {
    for (const [level, indicators] of Object.entries(this.satisfactionIndicators)) {
      const matches = indicators.filter(indicator => text.includes(indicator)).length;
      if (matches > 0) {
        return { level, confidence: matches / indicators.length };
      }
    }
    
    // 基于对话历史推断
    if (conversationHistory.length > 0) {
      const lastResponse = conversationHistory[conversationHistory.length - 1];
      if (lastResponse && lastResponse.emotion) {
        if (lastResponse.emotion.primary.emotion === 'positive') {
          return { level: 'medium', confidence: 0.6 };
        }
      }
    }
    
    return { level: 'unknown', confidence: 0 };
  }

  /**
   * 检测挫折感
   */
  detectFrustration(text, conversationHistory) {
    const frustrationIndicators = [
      '还是不行', '又不对', '还是不懂', '为什么总是',
      '怎么这么复杂', '搞不明白', '太难了', '不会用'
    ];
    
    let frustrationScore = 0;
    
    // 直接指标
    frustrationIndicators.forEach(indicator => {
      if (text.includes(indicator)) frustrationScore += 0.3;
    });
    
    // 重复问题检测
    if (conversationHistory.length >= 2) {
      const recentQuestions = conversationHistory.slice(-3).map(h => h.question);
      const similarQuestions = this.findSimilarQuestions(text, recentQuestions);
      frustrationScore += similarQuestions * 0.2;
    }
    
    return Math.min(1, frustrationScore);
  }

  /**
   * 检测紧急感
   */
  detectUrgency(text) {
    const urgentPatterns = this.emotionPatterns.urgent;
    
    let urgencyScore = 0;
    
    urgentPatterns.keywords.forEach(keyword => {
      if (text.includes(keyword)) urgencyScore += 0.2;
    });
    
    urgentPatterns.patterns.forEach(pattern => {
      if (pattern.test(text)) urgencyScore += 0.3;
    });
    
    return Math.min(1, urgencyScore);
  }

  /**
   * 查找相似问题
   */
  findSimilarQuestions(currentQuestion, previousQuestions) {
    const currentWords = currentQuestion.split(/\s+/);
    let similarCount = 0;
    
    previousQuestions.forEach(prevQuestion => {
      const prevWords = prevQuestion.split(/\s+/);
      const commonWords = currentWords.filter(word => 
        prevWords.includes(word) && word.length > 2
      );
      
      if (commonWords.length / currentWords.length > 0.5) {
        similarCount++;
      }
    });
    
    return similarCount;
  }

  /**
   * 计算置信度
   */
  calculateConfidence(primaryEmotion, intensity) {
    const baseConfidence = primaryEmotion.score;
    const intensityBonus = intensity > 0.7 ? 0.1 : 0;
    
    return Math.min(1, baseConfidence + intensityBonus);
  }

  /**
   * 获取响应建议
   */
  getResponseRecommendations(primaryEmotion, frustration, urgency) {
    const recommendations = [];
    
    if (primaryEmotion.emotion === 'frustrated' || frustration > 0.5) {
      recommendations.push('use_empathetic_tone');
      recommendations.push('provide_step_by_step_guidance');
      recommendations.push('offer_alternative_solutions');
    }
    
    if (primaryEmotion.emotion === 'confused') {
      recommendations.push('simplify_explanation');
      recommendations.push('use_examples');
      recommendations.push('break_down_complex_concepts');
    }
    
    if (urgency > 0.5) {
      recommendations.push('prioritize_quick_solution');
      recommendations.push('provide_immediate_help');
      recommendations.push('escalate_if_needed');
    }
    
    if (primaryEmotion.emotion === 'positive') {
      recommendations.push('maintain_positive_tone');
      recommendations.push('build_on_success');
    }
    
    if (primaryEmotion.emotion === 'negative') {
      recommendations.push('acknowledge_concern');
      recommendations.push('provide_reassurance');
      recommendations.push('focus_on_solutions');
    }
    
    return recommendations;
  }

  /**
   * 生成情感感知的回应策略
   */
  generateResponseStrategy(emotionAnalysis, userProfile = null) {
    const { primary, frustration, urgency, recommendations } = emotionAnalysis;
    
    let strategy = {
      tone: 'professional',
      detailLevel: 'medium',
      empathy: false,
      urgency: false,
      encouragement: false
    };
    
    // 基于情感调整策略
    if (primary.emotion === 'frustrated' || frustration > 0.5) {
      strategy.tone = 'empathetic';
      strategy.empathy = true;
      strategy.detailLevel = 'high';
    }
    
    if (primary.emotion === 'confused') {
      strategy.detailLevel = 'high';
      strategy.tone = 'patient';
    }
    
    if (urgency > 0.5) {
      strategy.urgency = true;
      strategy.detailLevel = 'focused';
    }
    
    if (primary.emotion === 'positive') {
      strategy.encouragement = true;
      strategy.tone = 'enthusiastic';
    }
    
    // 基于用户画像调整
    if (userProfile) {
      if (userProfile.experienceLevel === 'beginner') {
        strategy.detailLevel = 'high';
        strategy.tone = 'patient';
      } else if (userProfile.experienceLevel === 'expert') {
        strategy.detailLevel = 'concise';
        strategy.tone = 'technical';
      }
    }
    
    return strategy;
  }
}

module.exports = EmotionAnalyzer;
