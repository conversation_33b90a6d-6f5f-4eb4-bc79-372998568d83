/**
 * Context Engine - 智能上下文理解引擎
 * 为AI助手提供深度上下文理解和对话管理能力
 */

const IntentClassifier = require('./intentClassifier');
const EmotionAnalyzer = require('./emotionAnalyzer');
const LearningEngine = require('./learningEngine');

// 上下文分析器
class ContextAnalyzer {
  async analyze(question, history, intent, entities) {
    return {
      type: 'contextual',
      relevance: this.calculateRelevance(question, history),
      continuity: this.analyzeContinuity(history),
      topicShift: this.detectTopicShift(history, intent),
      referenceResolution: this.resolveReferences(question, history)
    };
  }

  calculateRelevance(question, history) {
    if (history.length === 0) return 1.0;
    
    const recentQuestions = history.slice(-3).map(h => h.question);
    const commonWords = this.findCommonWords(question, recentQuestions.join(' '));
    
    return Math.min(commonWords.length / 10, 1.0);
  }

  analyzeContinuity(history) {
    if (history.length < 2) return 'initial';
    
    const recent = history.slice(-2);
    const intentSimilarity = recent[0].intent?.category === recent[1].intent?.category;
    
    return intentSimilarity ? 'continuous' : 'divergent';
  }

  detectTopicShift(history, currentIntent) {
    if (history.length === 0) return false;
    
    const lastIntent = history[history.length - 1]?.intent?.category;
    return lastIntent !== currentIntent.category;
  }

  resolveReferences(question, history) {
    const pronouns = ['它', '这个', '那个', '这', '那'];
    const hasPronouns = pronouns.some(p => question.includes(p));
    
    if (hasPronouns && history.length > 0) {
      const lastEntities = history[history.length - 1]?.entities || [];
      return lastEntities.map(e => e.value);
    }
    
    return [];
  }

  findCommonWords(text1, text2) {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    
    return words1.filter(word => words2.includes(word) && word.length > 2);
  }
}

// 知识图谱
class KnowledgeGraph {
  constructor() {
    this.concepts = this.initializeConcepts();
    this.relations = this.initializeRelations();
  }

  async findRelatedConcepts(entities, intentCategory) {
    const related = new Set();
    
    entities.forEach(entity => {
      const entityConcepts = this.concepts[entity.value] || [];
      entityConcepts.forEach(concept => related.add(concept));
    });
    
    const categoryConcepts = this.concepts[intentCategory] || [];
    categoryConcepts.forEach(concept => related.add(concept));
    
    return Array.from(related).slice(0, 10);
  }

  initializeConcepts() {
    return {
      '地图': ['可视化', '导航', '空间分析', 'GIS', '图层'],
      '数据分析': ['统计', '趋势', '可视化', '报告', '指标'],
      '劳动力': ['就业', '工资', '人力资源', '经济', 'QCEW'],
      '工业园区': ['选址', '投资', '基础设施', '政策', '经济'],
      'platform_usage': ['功能', '操作', '界面', '工具', '系统'],
      'contact_info': ['联系', '沟通', '信息', '服务', '支持']
    };
  }

  initializeRelations() {
    return {
      'is_part_of': [['地图', '平台'], ['数据分析', '功能']],
      'related_to': [['劳动力', '经济'], ['工业园区', '选址']],
      'enables': [['地图', '空间分析'], ['数据', '决策']]
    };
  }
}

class ContextEngine {
  constructor() {
    this.conversationHistory = new Map(); // 用户对话历史
    this.userProfiles = new Map(); // 用户画像
    this.intentClassifier = new IntentClassifier(); // 意图分类器
    this.contextAnalyzer = new ContextAnalyzer(); // 上下文分析器
    this.knowledgeGraph = new KnowledgeGraph(); // 知识图谱
    this.emotionAnalyzer = new EmotionAnalyzer(); // 情感分析器
    this.learningEngine = new LearningEngine(); // 学习引擎
    this.sessionTimeout = 30 * 60 * 1000; // 30分钟会话超时
  }

  /**
   * 分析用户输入并增强上下文
   * @param {string} question - 用户问题
   * @param {string} userId - 用户ID
   * @param {string} sessionId - 会话ID
   * @returns {Object} 增强的上下文信息
   */
  async analyzeContext(question, userId = null, sessionId = null) {
    try {
      // 1. 获取或创建会话
      const session = this.getOrCreateSession(userId, sessionId);
      
      // 2. 意图分析
      const intent = await this.intentClassifier.classify(question, session.history);
      
      // 3. 实体识别
      const entities = await this.extractEntities(question);
      
      // 4. 情感分析
      const emotionAnalysis = this.emotionAnalyzer.analyzeEmotion(question, session.history);
      
      // 5. 上下文关联分析
      const contextInfo = await this.contextAnalyzer.analyze(
        question, 
        session.history, 
        intent, 
        entities
      );
      
      // 6. 知识图谱增强
      const relatedConcepts = await this.knowledgeGraph.findRelatedConcepts(
        entities, 
        intent.category
      );
      
      // 7. 用户画像更新
      await this.updateUserProfile(userId, intent, entities, question, emotionAnalysis);
      
      // 8. 构建增强上下文
      const enhancedContext = {
        originalQuestion: question,
        intent: intent,
        entities: entities,
        emotionAnalysis: emotionAnalysis,
        contextInfo: contextInfo,
        relatedConcepts: relatedConcepts,
        userProfile: this.getUserProfile(userId),
        conversationContext: this.buildConversationContext(session),
        responseStrategy: this.emotionAnalyzer.generateResponseStrategy(emotionAnalysis, this.getUserProfile(userId)),
        sessionId: session.id,
        timestamp: new Date().toISOString()
      };
      
      // 9. 更新会话历史
      this.updateSessionHistory(session, question, enhancedContext);
      
      return enhancedContext;
      
    } catch (error) {
      console.error('❌ Context Engine 分析失败:', error);
      return this.createFallbackContext(question, userId, sessionId);
    }
  }

  /**
   * 获取或创建用户会话
   */
  getOrCreateSession(userId, sessionId) {
    const key = sessionId || `${userId || 'anonymous'}_${Date.now()}`;
    
    if (!this.conversationHistory.has(key)) {
      this.conversationHistory.set(key, {
        id: key,
        userId: userId,
        history: [],
        startTime: new Date(),
        lastActivity: new Date(),
        context: {}
      });
    }
    
    const session = this.conversationHistory.get(key);
    session.lastActivity = new Date();
    
    return session;
  }

  /**
   * 构建对话上下文摘要
   */
  buildConversationContext(session) {
    const recentHistory = session.history.slice(-5); // 最近5轮对话
    
    return {
      recentTopics: this.extractRecentTopics(recentHistory),
      conversationFlow: this.analyzeConversationFlow(recentHistory),
      unresolved: this.findUnresolvedQuestions(recentHistory),
      userFocus: this.identifyUserFocus(recentHistory)
    };
  }

  /**
   * 提取最近话题
   */
  extractRecentTopics(history) {
    const topics = new Set();
    
    history.forEach(item => {
      if (item.intent && item.intent.category) {
        topics.add(item.intent.category);
      }
      if (item.entities) {
        item.entities.forEach(entity => topics.add(entity.type));
      }
    });
    
    return Array.from(topics);
  }

  /**
   * 分析对话流程
   */
  analyzeConversationFlow(history) {
    if (history.length < 2) return 'initial';
    
    const lastIntent = history[history.length - 1]?.intent?.category;
    const prevIntent = history[history.length - 2]?.intent?.category;
    
    if (lastIntent === prevIntent) return 'continuation';
    if (this.isRelatedIntent(lastIntent, prevIntent)) return 'related';
    return 'topic_switch';
  }

  /**
   * 判断意图是否相关
   */
  isRelatedIntent(intent1, intent2) {
    const relatedGroups = [
      ['map_usage', 'data_visualization', 'spatial_analysis'],
      ['contact_info', 'company_info', 'location_info'],
      ['labor_analysis', 'economic_data', 'trend_analysis']
    ];
    
    return relatedGroups.some(group => 
      group.includes(intent1) && group.includes(intent2)
    );
  }

  /**
   * 查找未解决的问题
   */
  findUnresolvedQuestions(history) {
    return history
      .filter(item => item.intent?.confidence < 0.7)
      .map(item => item.originalQuestion)
      .slice(-3); // 最近3个低置信度问题
  }

  /**
   * 识别用户关注焦点
   */
  identifyUserFocus(history) {
    const focusMap = new Map();
    
    history.forEach(item => {
      if (item.intent?.category) {
        const count = focusMap.get(item.intent.category) || 0;
        focusMap.set(item.intent.category, count + 1);
      }
    });
    
    const sortedFocus = Array.from(focusMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);
    
    return sortedFocus.map(([category, count]) => ({ category, count }));
  }

  /**
   * 实体识别
   */
  async extractEntities(question) {
    const entities = [];
    
    // 地理位置实体
    const locationPatterns = [
      /(\d+工业园区?)/g,
      /(泰国|中国|美国|日本|韩国)/g,
      /([A-Z]{2,}工业园)/g
    ];
    
    locationPatterns.forEach(pattern => {
      const matches = question.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            type: 'location',
            value: match,
            confidence: 0.8
          });
        });
      }
    });
    
    // 功能实体
    const functionPatterns = [
      /(地图|数据|分析|可视化|选址)/g,
      /(劳动力|趋势|经济|CPI|QCEW)/g
    ];
    
    functionPatterns.forEach(pattern => {
      const matches = question.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            type: 'function',
            value: match,
            confidence: 0.7
          });
        });
      }
    });
    
    return entities;
  }

  /**
   * 更新用户画像
   */
  async updateUserProfile(userId, intent, entities, question, emotionAnalysis) {
    if (!userId) return;

    let profile = this.userProfiles.get(userId) || {
      id: userId,
      expertise: new Map(),
      interests: new Map(),
      emotionalPatterns: new Map(),
      preferredStyle: 'balanced',
      interactionCount: 0,
      satisfactionHistory: [],
      firstSeen: new Date(),
      lastSeen: new Date()
    };

    // 更新专业领域
    if (intent.category) {
      const count = profile.expertise.get(intent.category) || 0;
      profile.expertise.set(intent.category, count + 1);
    }

    // 更新兴趣实体
    entities.forEach(entity => {
      const count = profile.interests.get(entity.value) || 0;
      profile.interests.set(entity.value, count + 1);
    });

    // 更新情感模式
    if (emotionAnalysis) {
      const emotion = emotionAnalysis.primary.emotion;
      const count = profile.emotionalPatterns.get(emotion) || 0;
      profile.emotionalPatterns.set(emotion, count + 1);

      // 记录满意度历史
      if (emotionAnalysis.satisfaction.level !== 'unknown') {
        profile.satisfactionHistory.push({
          level: emotionAnalysis.satisfaction.level,
          timestamp: new Date()
        });

        // 保持历史记录在合理范围内
        if (profile.satisfactionHistory.length > 20) {
          profile.satisfactionHistory = profile.satisfactionHistory.slice(-15);
        }
      }
    }

    // 更新统计信息
    profile.interactionCount++;
    profile.lastSeen = new Date();

    // 推断偏好风格
    profile.preferredStyle = this.inferPreferredStyle(profile);

    this.userProfiles.set(userId, profile);
  }

  /**
   * 推断用户偏好风格
   */
  inferPreferredStyle(profile) {
    const techQueries = profile.expertise.get('technical') || 0;
    const basicQueries = profile.expertise.get('basic_usage') || 0;
    const total = profile.interactionCount;

    if (techQueries / total > 0.6) return 'technical';
    if (basicQueries / total > 0.6) return 'simple';
    return 'balanced';
  }

  /**
   * 获取用户画像
   */
  getUserProfile(userId) {
    if (!userId) return null;

    const profile = this.userProfiles.get(userId);
    if (!profile) return null;

    return {
      expertise: Array.from(profile.expertise.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5),
      interests: Array.from(profile.interests.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10),
      preferredStyle: profile.preferredStyle,
      interactionCount: profile.interactionCount,
      experienceLevel: this.calculateExperienceLevel(profile)
    };
  }

  /**
   * 计算用户经验级别
   */
  calculateExperienceLevel(profile) {
    const count = profile.interactionCount;
    const diversity = profile.expertise.size;

    if (count > 50 && diversity > 5) return 'expert';
    if (count > 20 && diversity > 3) return 'intermediate';
    return 'beginner';
  }

  /**
   * 更新会话历史
   */
  updateSessionHistory(session, question, context) {
    session.history.push({
      question: question,
      intent: context.intent,
      entities: context.entities,
      timestamp: new Date()
    });

    // 保持历史记录在合理范围内
    if (session.history.length > 20) {
      session.history = session.history.slice(-15);
    }
  }

  /**
   * 创建降级上下文
   */
  createFallbackContext(question, userId, sessionId) {
    return {
      originalQuestion: question,
      intent: { category: 'unknown', confidence: 0.1 },
      entities: [],
      contextInfo: { type: 'fallback' },
      relatedConcepts: [],
      userProfile: null,
      conversationContext: { recentTopics: [], conversationFlow: 'initial' },
      sessionId: sessionId || 'fallback',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 处理用户反馈
   */
  async processFeedback(sessionId, questionId, feedback) {
    try {
      // 记录到学习引擎
      this.learningEngine.recordFeedback(sessionId, questionId, feedback);

      // 更新用户画像
      const session = this.conversationHistory.get(sessionId);
      if (session && session.userId) {
        await this.updateUserProfileFromFeedback(session.userId, feedback);
      }

      console.log(`📝 处理用户反馈: ${sessionId}, 评分: ${feedback.rating}`);

    } catch (error) {
      console.error('❌ 处理用户反馈失败:', error);
    }
  }

  /**
   * 基于反馈更新用户画像
   */
  async updateUserProfileFromFeedback(userId, feedback) {
    const profile = this.userProfiles.get(userId);
    if (!profile) return;

    // 更新满意度历史
    profile.satisfactionHistory.push({
      rating: feedback.rating,
      timestamp: new Date(),
      feedback: feedback
    });

    // 基于反馈调整偏好风格
    if (feedback.rating >= 4 && feedback.responseStyle) {
      // 用户喜欢当前的回应风格
      profile.preferredResponseStyle = feedback.responseStyle;
    }

    this.userProfiles.set(userId, profile);
  }

  /**
   * 获取学习统计
   */
  getLearningStats() {
    return this.learningEngine.getLearningStats();
  }

  /**
   * 清理过期会话
   */
  cleanupExpiredSessions() {
    const now = new Date();

    for (const [key, session] of this.conversationHistory.entries()) {
      if (now - session.lastActivity > this.sessionTimeout) {
        this.conversationHistory.delete(key);
      }
    }
  }

  /**
   * 获取系统状态
   */
  getStatus() {
    return {
      activeSessions: this.conversationHistory.size,
      userProfiles: this.userProfiles.size,
      memoryUsage: {
        conversations: this.conversationHistory.size,
        profiles: this.userProfiles.size
      },
      learningStats: this.getLearningStats(),
      emotionAnalysis: {
        enabled: true,
        patterns: Array.from(this.userProfiles.values())
          .flatMap(profile => Array.from(profile.emotionalPatterns?.keys() || []))
          .filter((emotion, index, arr) => arr.indexOf(emotion) === index)
      }
    };
  }
}

module.exports = ContextEngine;
