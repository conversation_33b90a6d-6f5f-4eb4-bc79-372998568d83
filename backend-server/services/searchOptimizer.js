/**
 * 搜索优化服务
 * 提供智能搜索查询优化、结果排序和相关性评分功能
 * 支持中文查询优化和结构化信息提取
 */
class SearchOptimizer {
  constructor() {
    this.queryCache = new Map();
    this.resultCache = new Map();
    this.cacheTimeout = 30 * 60 * 1000; // 30分钟缓存

    // 中文同义词词典
    this.chineseSynonyms = {
      '联系方式': ['联系电话', '电话号码', '联系人', '客服电话', '咨询电话', '热线'],
      '地址': ['位置', '所在地', '详细地址', '办公地址', '注册地址'],
      '工业园区': ['工业园', '产业园', '科技园', '开发区', '经济区'],
      '公司': ['企业', '集团', '有限公司', '股份公司', '机构'],
      '官网': ['官方网站', '网站', '主页', '门户网站']
    };

    // 联系方式查询模板
    this.contactQueryTemplates = [
      '{name} 联系电话',
      '{name} 客服热线',
      '{name} 官方联系方式',
      '{name} 电话 地址',
      '{name} 联系我们',
      '{name} 官网 联系'
    ];
  }

  /**
   * 优化搜索查询
   */
  optimizeQuery(originalQuery, context = {}) {
    const cacheKey = `query_${originalQuery}_${JSON.stringify(context)}`;
    
    if (this.queryCache.has(cacheKey)) {
      const cached = this.queryCache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.optimizedQuery;
      }
    }

    const optimizedQuery = this.performQueryOptimization(originalQuery, context);
    
    this.queryCache.set(cacheKey, {
      optimizedQuery,
      timestamp: Date.now()
    });

    return optimizedQuery;
  }

  /**
   * 执行查询优化
   */
  performQueryOptimization(query, context) {
    // 检测查询类型
    const queryType = this.detectQueryType(query);

    // 针对不同查询类型使用不同策略
    if (queryType === 'contact') {
      return this.optimizeContactQuery(query, context);
    }

    // 1. 查询扩展
    let expandedQuery = this.expandQuery(query);

    // 2. 中文同义词替换
    expandedQuery = this.applyChineseSynonyms(expandedQuery);

    // 3. 上下文增强
    if (context.userProfile) {
      expandedQuery = this.addContextualTerms(expandedQuery, context.userProfile);
    }

    // 4. 查询重写
    expandedQuery = this.rewriteQuery(expandedQuery);

    // 5. 权重调整
    return this.adjustQueryWeights(expandedQuery, query);
  }

  /**
   * 检测查询类型
   */
  detectQueryType(query) {
    const contactKeywords = ['联系方式', '联系电话', '电话', '联系人', '客服', '热线', '咨询'];
    const addressKeywords = ['地址', '位置', '在哪', '怎么去'];

    if (contactKeywords.some(keyword => query.includes(keyword))) {
      return 'contact';
    }
    if (addressKeywords.some(keyword => query.includes(keyword))) {
      return 'address';
    }
    return 'general';
  }

  /**
   * 优化联系方式查询
   */
  optimizeContactQuery(query, context) {
    // 提取实体名称
    const entityName = this.extractEntityName(query);

    if (entityName) {
      // 使用多个查询模板
      const queries = this.contactQueryTemplates.map(template =>
        template.replace('{name}', entityName)
      );

      // 返回最优查询（可以后续实现多查询策略）
      return queries[0];
    }

    // 如果无法提取实体名称，使用同义词扩展
    return this.applyChineseSynonyms(query);
  }

  /**
   * 提取实体名称
   */
  extractEntityName(query) {
    // 简单的实体提取逻辑
    const patterns = [
      /(.+?)(?:的)?(?:联系方式|联系电话|电话|地址)/,
      /(?:找|查|搜)(.+?)(?:联系方式|联系电话|电话)/,
      /(.+?)(?:工业园区?|公司|企业|集团)/
    ];

    for (const pattern of patterns) {
      const match = query.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * 查询扩展
   */
  expandQuery(query) {
    const expansionRules = {
      '304工业园区': ['304工业园', '泰国304园区', '304 Industrial Park'],
      '联系方式': ['联系电话', '联系地址', '联系邮箱', '官方联系'],
      '平台功能': ['功能介绍', '使用方法', '操作指南', '功能说明'],
      '数据分析': ['数据统计', '分析报告', '数据可视化', '统计分析'],
      'QCEW': ['季度就业数据', '就业统计', '工资数据', 'Quarterly Census'],
      'CPI': ['消费价格指数', '通胀指数', '价格指数', 'Consumer Price Index']
    };

    let expandedQuery = query;
    
    Object.entries(expansionRules).forEach(([term, expansions]) => {
      if (query.includes(term)) {
        // 添加相关术语，但不超过原查询长度的2倍
        const additionalTerms = expansions.slice(0, 2).join(' ');
        expandedQuery += ' ' + additionalTerms;
      }
    });

    return expandedQuery;
  }

  /**
   * 中文同义词替换
   */
  applyChineseSynonyms(query) {
    let expandedQuery = query;

    // 应用中文同义词
    Object.keys(this.chineseSynonyms).forEach(word => {
      if (query.includes(word)) {
        const synonyms = this.chineseSynonyms[word];
        // 添加同义词，但保持原词权重最高
        expandedQuery += ' ' + synonyms.slice(0, 2).join(' ');
      }
    });

    return expandedQuery;
  }

  /**
   * 应用同义词（保留原有功能）
   */
  applySynonyms(query) {
    const synonymMap = {
      '怎么': '如何',
      '怎样': '如何',
      '咋样': '如何',
      '电话号码': '联系电话',
      '手机号': '联系电话',
      '邮件': '邮箱',
      'email': '邮箱',
      '地址': '位置',
      '位置': '地址',
      '园区': '工业园区',
      '开发区': '工业园区'
    };

    let processedQuery = query;

    Object.entries(synonymMap).forEach(([original, synonym]) => {
      const regex = new RegExp(original, 'gi');
      processedQuery = processedQuery.replace(regex, `${original} ${synonym}`);
    });

    return processedQuery;
  }

  /**
   * 添加上下文术语
   */
  addContextualTerms(query, userProfile) {
    if (!userProfile || !userProfile.interests) return query;

    const relevantInterests = userProfile.interests
      .slice(0, 3)
      .map(([interest]) => interest);

    // 如果用户对某些领域感兴趣，添加相关术语
    const contextTerms = [];
    
    relevantInterests.forEach(interest => {
      if (interest.includes('工业') && !query.includes('工业')) {
        contextTerms.push('工业');
      }
      if (interest.includes('投资') && !query.includes('投资')) {
        contextTerms.push('投资');
      }
      if (interest.includes('数据') && !query.includes('数据')) {
        contextTerms.push('数据');
      }
    });

    return contextTerms.length > 0 ? 
      `${query} ${contextTerms.join(' ')}` : query;
  }

  /**
   * 查询重写
   */
  rewriteQuery(query) {
    const rewriteRules = [
      {
        pattern: /(.+)的联系方式/,
        replacement: '$1 联系电话 联系地址 联系邮箱'
      },
      {
        pattern: /如何使用(.+)/,
        replacement: '$1 使用方法 操作指南 教程'
      },
      {
        pattern: /(.+)有什么功能/,
        replacement: '$1 功能介绍 特点 优势'
      },
      {
        pattern: /(.+)怎么操作/,
        replacement: '$1 操作步骤 使用方法 指南'
      }
    ];

    let rewrittenQuery = query;
    
    rewriteRules.forEach(rule => {
      if (rule.pattern.test(query)) {
        rewrittenQuery = query.replace(rule.pattern, rule.replacement);
      }
    });

    return rewrittenQuery;
  }

  /**
   * 调整查询权重
   */
  adjustQueryWeights(expandedQuery, originalQuery) {
    // 保持原始查询的核心术语权重最高
    const originalTerms = originalQuery.split(/\s+/);
    const expandedTerms = expandedQuery.split(/\s+/);
    
    // 构建加权查询
    const weightedTerms = [];
    
    // 原始术语权重为2
    originalTerms.forEach(term => {
      if (term.length > 1) {
        weightedTerms.push(`${term}^2`);
      }
    });
    
    // 扩展术语权重为1
    expandedTerms.forEach(term => {
      if (term.length > 1 && !originalTerms.includes(term)) {
        weightedTerms.push(term);
      }
    });

    return weightedTerms.join(' ');
  }

  /**
   * 评估搜索结果相关性
   */
  scoreSearchResults(results, originalQuery, context = {}) {
    if (!results || !results.length) return results;

    const queryTerms = originalQuery.toLowerCase().split(/\s+/);
    
    return results.map(result => {
      const score = this.calculateRelevanceScore(result, queryTerms, context);
      return { ...result, relevanceScore: score };
    }).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * 计算相关性得分
   */
  calculateRelevanceScore(result, queryTerms, context) {
    let score = 0;
    const text = `${result.title} ${result.snippet}`.toLowerCase();
    
    // 1. 术语匹配得分
    queryTerms.forEach(term => {
      if (term.length > 1) {
        const matches = (text.match(new RegExp(term, 'g')) || []).length;
        score += matches * 10;
        
        // 标题匹配额外加分
        if (result.title.toLowerCase().includes(term)) {
          score += 15;
        }
      }
    });

    // 2. 权威性得分
    const authorityDomains = [
      'gov.cn', 'gov.th', '.gov', '.edu',
      'investmentguide', 'industrialpark',
      'official', 'chamber'
    ];
    
    if (result.displayLink) {
      const domain = result.displayLink.toLowerCase();
      if (authorityDomains.some(auth => domain.includes(auth))) {
        score += 20;
      }
    }

    // 3. 内容质量得分
    if (result.snippet) {
      const snippetLength = result.snippet.length;
      if (snippetLength > 50 && snippetLength < 300) {
        score += 5; // 适中长度的摘要通常质量更好
      }
      
      // 包含具体信息的内容加分
      if (/\d{3,}/.test(result.snippet)) { // 包含电话号码等
        score += 10;
      }
      if (/@/.test(result.snippet)) { // 包含邮箱
        score += 10;
      }
    }

    // 4. 时效性得分（如果有时间信息）
    if (result.publishDate) {
      const daysSincePublish = (Date.now() - new Date(result.publishDate)) / (1000 * 60 * 60 * 24);
      if (daysSincePublish < 30) {
        score += 15; // 最近30天的内容加分
      } else if (daysSincePublish < 365) {
        score += 5; // 最近一年的内容少量加分
      }
    }

    return score;
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.queryCache.clear();
    this.resultCache.clear();
    console.log('🧹 搜索优化器缓存已清理');
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      queryCacheSize: this.queryCache.size,
      resultCacheSize: this.resultCache.size,
      cacheTimeout: this.cacheTimeout
    };
  }
}

module.exports = SearchOptimizer;
